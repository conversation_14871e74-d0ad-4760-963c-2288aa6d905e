﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
		<AssemblyName>Kean.Infrastructure.Database</AssemblyName>
		<RootNamespace>Kean.Infrastructure.Database</RootNamespace>
		<DefaultItemExcludes>Seedwork\*.cs</DefaultItemExcludes>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="Repository\Default\Migrations\Scripts\Oracle\T_MANAGE_TRIGGER%40001.sql" />
	  <None Remove="Repository\Default\Migrations\Scripts\Oracle\T_MANAGE_TRIGGER_HIS%40001.sql" />
	  <None Remove="Repository\Default\Migrations\Scripts\Oracle\V_MANAGE_TRIGGER%40001.sql" />
	  <None Remove="Repository\Default\Migrations\Scripts\SqlServer\T_MANAGE_LIST%40000.sql" />
	  <None Remove="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_PLATFORM%40000.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\IO_CONTROL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\IO_CONTROL_APPLY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\IO_CONTROL_ROUTE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\P_INIT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\P_WH_CELL_INIT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_DEVICE_ERROR%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_DEVICE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_DEVICE_ROUTE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_DEVICE_TASK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_GOODS_CLASS%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_GOODS_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_GOODS_SAFETY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_GOODS_SAFETY_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_INF_HIS%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_INF_INPUT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_INF_OUTPUT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_MANAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_MANAGE_TRIGGER%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_MANAGE_TRIGGER_HIS%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_HIS_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_HIS_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_SERIAL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_PLAN_TYPE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_RECORD_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_RECORD_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_STORAGE_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_STORAGE_LOCK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_STORAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_LOG%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_MENU%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_MENU%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_PARAM%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_ROLE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_ROLE_MENU%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_SECURITY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_SECURITY_LOG%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_USER%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_USER_MESSAGE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_SYS_USER_ROLE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_WH_AREA%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_WH_CELL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\T_WH_WAREHOUSE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_GOODS_SAFETY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_MANAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_MANAGE_TRIGGER%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_PLAN_HIS_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_PLAN_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_RECORD_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_RECORD_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_STORAGE_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_STORAGE_LOCK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_STORAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\Oracle\V_STORAGE_SAFETY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\IO_CONTROL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\IO_CONTROL_APPLY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\IO_CONTROL_ROUTE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\P_INIT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\P_WH_CELL_INIT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PDA_PRINTER_IP.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_DEVICE_ERROR%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_DEVICE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_DEVICE_ROUTE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_DEVICE_TASK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_GOODS_CLASS%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_GOODS_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_GOODS_SAFETY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_GOODS_SAFETY_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_INF_HIS%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_INF_INPUT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_INF_OUTPUT%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\<EMAIL>" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_MANAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_MANAGE_TRIGGER%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_MANAGE_TRIGGER_HIS%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_HIS_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_HIS_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_SERIAL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_PLAN_TYPE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_RECORD_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_RECORD_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_STORAGE_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_STORAGE_LOCK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_STORAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_LOG%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_MENU%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_MENU%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_PARAM%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\<EMAIL>" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_ROLE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_ROLE_MENU%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_SECURITY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_SECURITY_LOG%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_USER%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_USER_MESSAGE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_SYS_USER_ROLE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_WH_AREA%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_WH_CELL%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_WH_WAREHOUSE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_GOODS_SAFETY%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_MANAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_MANAGE_TRIGGER%40001.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_PLAN_HIS_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_PLAN_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_RECORD_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_RECORD_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_STORAGE_LIST%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_STORAGE_LOCK%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_STORAGE_MAIN%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\V_STORAGE_SAFETY%40000.sql" />

		<!--NEW TABLES/VIEWS FOR KSF-->
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_OPERATION%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_CLIENT_INFO%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_CLIENT_CATE%40000.sql" />
		<EmbeddedResource Include="Repository\Default\Migrations\Scripts\SqlServer\T_WORK_GROUP%40000.sql" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="FluentMigrator.Runner" Version="6.2.0" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
		<PackageReference Include="MySql.Data" Version="9.1.0" />
		<PackageReference Include="Npgsql" Version="9.0.1" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.6.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Wms.Core.Infrastructure.Utilities\Wms.Core.Infrastructure.Utilities.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Include="Repository\AbstractDatabase.cs;Repository\DatabaseCollection.cs;Repository\IDatabase.cs;Repository\IDatabaseCollection.cs" Visible="False" />
	</ItemGroup>

	<Target Name="IncludeSourceCodes" BeforeTargets="CoreCompile">
		<ItemGroup>
			<Compile Include="Seedwork\*.cs" />
		</ItemGroup>
	</Target>

</Project>
