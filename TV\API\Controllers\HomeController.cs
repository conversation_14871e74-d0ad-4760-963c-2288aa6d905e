﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Org.BouncyCastle.Asn1.Cms;
using Swashbuckle.AspNetCore.Annotations;
using System;

namespace API.Controllers
{
    [ApiExplorerSettings(GroupName = "首页")]
    [ApiController]
    [Route("tv")]
    public class HomeController : Controller
    {
        [SwaggerOperation(Summary = "获取首页数据")]
        [HttpGet]
        public string Get(string ip, string search)
        {

            var res = Program.data.GetInfo(ip, search);

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(res);

            Program.logger.Debug(json);
            return json;
        }

    }
}
