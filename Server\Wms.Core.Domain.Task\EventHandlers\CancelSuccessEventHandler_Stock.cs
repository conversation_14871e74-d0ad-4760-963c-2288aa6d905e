﻿using Kean.Domain.Shared;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Repositories;
using Newtonsoft.Json.Linq;
using System;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 任务取消命令成功时，处理后续
    /// </summary>
    public sealed class CancelSuccessEventHandler_Stock(
        ICommandBus commandBus,         // 命令总线
        IStockService stockService,  // 仓储域共享服务
        ITaskRepository taskRepository  // 任务仓库
    ) : EventHandler<CancelSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(CancelSuccessEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Tag != null && (@event.Tag.Contains("PlanLockStockOut") || @event.Tag.Contains("PlanLockFeedOut")))
            {
                JArray StockTemplate = await stockService.GetStockLine(@event.Barcode);
                JArray StockLines = new JArray();
                JArray Locks;
                JObject StockLine;
                if (StockTemplate.Count > 0)
                {
                    Locks = await stockService.GetLockIdByStorageListId((int)StockTemplate[0]["Id"]);
                    if (Locks.Count == 1)
                    {
                        StockLines = StockTemplate;
                        StockLines[0]["Quantity"] = -StockLines[0].Value<decimal>("Quantity");
                        StockLines[0]["Lock"] = Locks[0]["LOCK_ID"];
                        StockLines[0]["Order"] = Locks[0]["PLAN_LIST_ID"];
                    }
                    else
                    {
                        foreach (var @lock in Locks)
                        {
                            StockLine = StockTemplate[0].DeepClone().ToObject<JObject>();
                            StockLine["Quantity"] = -@lock.Value<decimal>("LOCK_QUANTITY");
                            StockLine["Lock"] = @lock["LOCK_ID"];
                            StockLine["PlanListId"] = @lock["PLAN_LIST_ID"];
                            StockLine["Order"] = @lock["PLAN_LIST_ID"];
                            StockLines.Add(StockLine);
                        }
                    }
                    await stockService.LockStorage(@event.Barcode, StockLines, 0, "PlanLock", false, DateTime.Now);
                }
            }
            foreach (var item in await taskRepository.GetFollows(@event.Id))
            {

                await commandBus.Execute(new CancelCommand { Id = item.Id }, cancellationToken);
            }
        }
    }
}
