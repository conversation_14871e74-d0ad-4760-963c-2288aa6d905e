import {
  Component,
  AfterViewInit,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";
import { DialogService } from "@app/core/services/dialog.service";
import { HttpService } from "@app/core/services/http.service";
import { I18nService } from "@app/core/services/i18n.service";
import { ToastService } from "@app/core/services/toast.service";
import { SelectionModel } from "@angular/cdk/collections";
import { Console } from "console";
import { FilterComponent } from "./filter/filter.component";
import { BindplatformComponent } from "./bindplatform/bindplatform.component";

@Component({
  selector: "app-todo-out",
  templateUrl: "./todo-out.component.html",
  styleUrls: ["./todo-out.component.scss"],
  queries: {
    _sort: new ViewChild(MatSort),
    _remark: new ViewChild("$area"),
    // _qtyColumn: new ViewChild("$qty"),
  },
})
export class TodoOutComponent implements AfterViewInit {
  // private _qtyColumn: TemplateRef<any>;
  public selectionP: any[] = [];
  public selectionS: any[] = [];
  private _selection = new SelectionModel<any>(true, []);

  public half: boolean = true;
  //整板出货
  public full: boolean = true;
  //指定允收天数
  public recieveTime: number = 0;
  // 省略无库存单据
  public ignoreNullStorage: boolean = false;
  //>=满板量
  public overFullPallet: boolean = false;
  //  var craneBack: any = await this._httpService.get("orders/crane");
  //       var craneS = craneBack?.craneList?.paraM_VALUE;
  //       craneObj = JSON.parse(craneS);
  //吊车
  public crane: any;
  public craneL: any;
  public planlistsheet: boolean = true;
  public buttonshow: boolean = false;
  private _sort: MatSort;
  private _columns: { [key: string]: any };
  private _columnsD: { [key: string]: any };
  private _rows: MatTableDataSource<any>;
  private _loading: boolean;
  private _colorful: boolean;
  //单据条码
  private _planCode204099: any = null;
  constructor(
    private _dialogService: DialogService,
    private _httpService: HttpService,
    private _i18nService: I18nService,
    private _toastService: ToastService
  ) {
    this._columns = this.planlistColumn;

    this._rows = new MatTableDataSource<any>([]);
    this._loading = false;
    this._colorful = false;

    this._httpService.get("orders/crane").then((res: any) => {
      var craneS = res.craneList.paraM_VALUE;
      this.craneL = JSON.parse(craneS);
    });
  }

  ngAfterViewInit(): void {
    this._rows.sort = this._sort;
  }
  public get planCode204099() {
    return this._planCode204099;
  }
  public set planCode204099(value: any) {
    this._planCode204099 = value;
  }
  public get columns() {
    return this._columns;
  }
  public get columnsD() {
    return this._columns.filter((x) => x.id != "check");
  }
  public get selection() {
    return this._selection;
  }
  public get rows() {
    return this._rows;
  }

  public get loading() {
    return this._loading;
  }

  public get colorful() {
    return this._colorful;
  }

  public set colorful(value: boolean) {
    this._colorful = value;
  }

  public planlistColumn = [
    { id: "check" },
    {
      id: "number",
      header: "routes.order.number",
    },
    {
      id: "creater",
      header: "routes.order.creater",
    },
    {
      id: "plancode40",
      header: "routes.order.plancode40",
    },
    {
      id: "plancode99",
      header: "routes.order.plancode99",
    },
    {
      id: "platform",
      header: "routes.order.platform",
    },
    {
      id: "state",
      header: "routes.order.state",
    },
    {
      id: "createTime",
      header: "routes.order.created",
    },
    {
      id: "code",
      header: "routes.material.code",
    },
    {
      id: "name",
      header: "routes.material.name",
    },
    {
      id: "model",
      header: "routes.material.model",
    },
    {
      id: "quantity",
      header: "routes.material.qty",
    },
    {
      id: "finished",
      header: "routes.order.finished",
    },
    {
      id: "executing",
      header: "routes.order.executing",
    },
    {
      id: "unit",
      header: "routes.material.unit",
    },/*
    {
      id: "batch",
      header: "routes.material.batch",
    },
    {
      id: "bill",
      header: "routes.material.bill",
    },
    {
      id: "supplier",
      header: "routes.material.supplier",
    },
    {
      id: "brand",
      header: "routes.material.brand",
    },
    {
      id: "adjustQuantity",
      header: "routes.material.adjustQuantity",
    },
    {
      id: "adjustReason",
      header: "routes.material.adjustReason",
    },
    {
      id: "clientCode",
      header: "routes.material.clientCode",
    },
    {
      id: "effectivePeriod",
      header: "routes.material.effectivePeriod",
    },
    {
      id: "expirationPeriod",
      header: "routes.material.expirationPeriod",
    },
    {
      id: "financialPostLatestTime",
      header: "routes.material.financialPostLatestTime",
    },
    {
      id: "financialPostQuantity",
      header: "routes.material.financialPostQuantity",
    },
    {
      id: "financialPostStatus",
      header: "routes.material.financialPostStatus",
    },
    {
      id: "adjustRfinancialPostTimeeason",
      header: "routes.material.financialPostTime",
    },
    {
      id: "financialPostUploadTime",
      header: "routes.material.financialPostUploadTime",
    },
    {
      id: "importTime",
      header: "routes.material.importTime",
    },
    {
      id: "productionLine",
      header: "routes.material.productionLine",
    },
    {
      id: "sapNo",
      header: "routes.material.sapNo",
    },
    {
      id: "transferType",
      header: "routes.material.transferType",
    },*/
    {
      id: "truckInfo",
      header: "routes.material.truckInfo",
    },/*
    {
      id: "warehouseCode",
      header: "routes.material.warehouseCode",
    },
    {
      id: "workGroup",
      header: "routes.material.workGroup",
    },*/
  ];
  public storagelistColumn = [
    { id: "check" },
    {
      id: "code",
      header: "routes.material.code",
    },
    {
      id: "name",
      header: "routes.material.name",
    },
    {
      id: "model",
      header: "routes.material.model",
    },
    {
      id: "quantity",
      header: "routes.material.qty",
    },
    {
      id:"remark",
      header:"预约操作",
    },
    {
      id: "category",
      header: "routes.material.cat",
    },
    { id: "group", header: "routes.material.group" },
    {
      id: "lockedQuantity",
      header: "routes.query.stock.locked",
    },
    {
      id: "unlockedQuantity",
      header: "routes.query.stock.unlocked",
    },
    {
      id: "managelistquantity",
      header: "routes.material.manageListQuantity",
      template: null,
    },
    { id: "inboundTime", header: "routes.query.blocking.create" },
    { id: "inventoryTime", header: "routes.query.blocking.update" },

    { id: "cell", header: "routes.stock.outbound.cell" },

    { id: "unit", header: "routes.material.unit" },
    {
      id: "brand",
      header: "routes.material.brand",
    },
    /*{
      id: "price",
      header: "routes.material.price",
    },
    {
      id: "weight",
      header: "routes.material.weight",
    },
    {
      id: "length",
      header: "routes.material.length",
    },
    {
      id: "width",
      header: "routes.material.width",
    },
    {
      id: "height",
      header: "routes.material.height",
    },
    {
      id: "facade",
      header: "routes.material.facade",
    },
    {
      id: "effectivePeriod",
      header: "routes.material.eff",
    },
    {
      id: "expirationPeriod",
      header: "routes.material.exp",
    },
    {
      id: "qualityControl",
      header: "routes.material.qc",
    },
    {
      id: "minimumStockPeriod",
      header: "routes.material.mintime",
    },
    {
      id: "maximumStockPeriod",
      header: "routes.material.maxtime",
    },*/
    { id: "operation" },
  ];
  public orderLine: any;
  public storageLine: any;
  public convertSheet = async (isPlanlist: boolean) => {
    this.planlistsheet = isPlanlist;

    if (this.planlistsheet) {
      this.rows.data = this.orderLine;

      this._columns = this.planlistColumn;
      this.selection.clear();
      this.selection.select(...this.selectionP);
      document.getElementById("buttonPlan").style.backgroundColor = "gray";
      document.getElementById("buttonPlan").style.color = "black";
      document.getElementById("buttonStorage").style.backgroundColor = "";
      document.getElementById("buttonStorage").style.color = "";
    } else {
      this.selectionP = [...new Set(this.selection.selected)];
      if (this.selectionP.length == 0) {
        this._toastService.show(
          this._i18nService.translate(
            "routes.stock.todooutbound.notify.chooseorderfirst"
          )
        );
        this.planlistsheet = true;
        return;
      }
      else {
        const res: any = await this._httpService
          .post(`orders/getStoragelist`, {
            orders: this.selectionP,
            half: this.half,
            full: this.full,
            overFullPallet: this.overFullPallet,
            ignoreNullStorage: this.ignoreNullStorage,
            crane: this.crane?.CRANE_TYPE,
            allowdays: this.recieveTime,
          })
          .catch((e) => {
            this._toastService.show(e.error);
            return "error";
            //console.log(e);
          });

        if (res == "error") {
          return;
        }
        res.storageLines.forEach((element) => {
          element.managelistquantityEdited = element.managelistquantity;
        });
        this.storageLine = res.storageLines;
      }
      this.rows.data = this.storageLine;
      this._columns = this.storagelistColumn;
      this.selection.clear();
      this.selection.select(...this.rows.data);

      document.getElementById("buttonStorage").style.backgroundColor = "gray";
      document.getElementById("buttonStorage").style.color = "black";
      document.getElementById("buttonPlan").style.backgroundColor = "";
      document.getElementById("buttonPlan").style.color = "";
    }
  };

  public outbound = async () => {
    this.selectionS = [...new Set(this.selection.selected)];

    this._loading = true;
    var res = await this._httpService
      .post("orders/sendOutfeed", {
        orders: this.selectionP,
        stocks: this.selectionS,
      })
      .catch((e) => {
        this._toastService.show(
          e.error?.errorMessage
            ? `${this._i18nService.translate(
              `server.${e.error.errorMessage}`
            )} (${e.error.errorMessage})`
            : this._i18nService.translate("shared.notification.unknown")
        );
      });
    if (res !== undefined) {
      this._toastService.show(
        this._i18nService.translate("shared.notification.success")
      );
      this._rows.data = [];
      this.buttonshow = false;
      this.half = true;
      this.full = true;
      this._selection.clear();
      this.selectionP = [];
      this.selectionS = [];
      this.selection.clear();
      this._columns = this.planlistColumn;
      this.planlistsheet = true;
      this.recieveTime = 0;
      this.ignoreNullStorage = false;
      this.overFullPallet = false;
      this.crane = null;
    }
    this._loading = false;
  };
  public async platform(plancode: any): Promise<any> {
    var platforms: any = await this._httpService.get("platforms");
    let res: any = await this._dialogService.open(BindplatformComponent, {
      data: {
        platformL: platforms.items,
      },
    });
    if (res) {
      const res2 = await this._httpService
        .get(
          `orders/platform?plancode=${plancode}&no=${res.platform}&truck=${res.truck}`
        )
        .catch((e) => {
          this._toastService.show(
            e.error?.errorMessage
              ? `${this._i18nService.translate(
                `server.${e.error.errorMessage}`
              )} (${e.error.errorMessage})`
              : this._i18nService.translate("shared.notification.unknown")
          );
        });
      if (res2 !== undefined) {
        this._toastService.show(
          this._i18nService.translate("shared.notification.success")
        );
      }
    }
  }
  public reset = async () => {
    if (
      await this._dialogService.confirm(
        this._i18nService.translate("shared.notification.confirm")
      )
    ) {
      this._rows.data = [];
      this.planCode204099 = null;
      this.buttonshow = false;
      this.half = true;
      this.full = true;
      this._selection.clear();
      this.selectionP = [];
      this.selectionS = [];
      this.planlistsheet = true;
      this.recieveTime = 0;
      this.ignoreNullStorage = false;
      this.overFullPallet = false;
      this.crane = null;
    }
  };
  public testButton = async (buttonName: any) => {
    console.log("testButton——————————" + buttonName);
    this.selectionS = [...new Set(this.selection.selected)];

    this._loading = true;
    var res = await this._httpService
      .post("orders/sendOutfeedPK", {
        orders: this.selectionP,
        stocks: this.selectionS,
      })
      .catch((e) => {
        this._toastService.show(
          e.error?.errorMessage
            ? `${this._i18nService.translate(
              `server.${e.error.errorMessage}`
            )} (${e.error.errorMessage})`
            : this._i18nService.translate("shared.notification.unknown")
        );
      });
    if (res !== undefined) {
      this._toastService.show(
        this._i18nService.translate("shared.notification.success")
      );
      this._rows.data = [];
      this.buttonshow = false;
      this.half = true;
      this.full = true;
      this._selection.clear();
      this.selectionP = [];
      this.selectionS = [];
      this.selection.clear();
      this._columns = this.planlistColumn;
      this.planlistsheet = true;
      this.recieveTime = 0;
      this.ignoreNullStorage = false;
      this.overFullPallet = false;
      this.crane = null;
    }
    this._loading = false;
    console.log("half" + this.half + "||full" + this.full);
  };

  public getPlanInfo = async (plancode: number) => {
    this.selection.clear();
    const res: any = await this._httpService
      .get(
        `orders/getPSlist?plancode=${plancode}&half=${this.half}&full=${this.full}`
      )
      .catch((e) => {
        console.log(e);
      });
    if (res && res.orderLines.length > 0 && res.canBind && plancode) {
      this.buttonshow = true;
      this.orderLine = res.orderLines;
      //this.storageLine = res.storageLines;
      this.rows.data = this.orderLine;
    } else {
      this.buttonshow = false;
      this.rows.data = [];
    }
  };
  public edit = async (item: any) => {
    const quantity = await this._dialogService.prompt(
      this._i18nService.translate("routes.stock.inbound.quantity"),
      item.managelistquantityEdited,
      {
        type: "number",
        range: [0, item.managelistquantityEdited],
        required: true,
      }
    );
    if (quantity && quantity > 0 && quantity <= item.managelistquantity) {
      item.managelistquantityEdited = quantity;
    } else {
      this._toastService.show(
        this._i18nService.translate("routes.stock.inbound.quantityE")
      );
    }
  };
  public setfilter = async () => {
    var res1: any = await this._dialogService.open(FilterComponent, {
      data: {
        //零头单出货
        half: this.half,
        //整板出货
        full: this.full,
        //指定允收天数
        recieveTime: this.recieveTime,
        // 省略无库存单据
        ignoreNullStorage: this.ignoreNullStorage,
        //>=满板量
        overFullPallet: this.overFullPallet,
        craneL: this.craneL,
        //吊车
        crane: this.crane,
      },
    });
    if (res1) {
      //零头单出货
      this.half = res1.half;
      //整板出货
      this.full = res1.full;
      //指定允收天数
      this.recieveTime = res1.recieveTime ? res1.recieveTime : 0;
      // 省略无库存单据
      this.ignoreNullStorage = res1.ignoreNullStorage;
      //>=满板量
      this.overFullPallet = res1.overFullPallet;

      //吊车
      this.crane = res1.crane;
    }
  };
}
