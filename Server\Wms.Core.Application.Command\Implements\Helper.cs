﻿
using System;
using System.Drawing;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;
using System.IO;
using static System.Net.Mime.MediaTypeNames;
using ZXing.Windows.Compatibility;

namespace Kean.Application.Command.Implements
{

    public sealed class BarcodeHelper
    {
        public static Bitmap ToBarcode128(string data, int height, int? width = null)
        {
            return ToBarcode(data, height, width, BarcodeFormat.CODE_128);
        }
        private static Bitmap ToBarcode(string data, int height, int? width, BarcodeFormat format)
        {
            var options = new EncodingOptions();
            options.Hints.Add(EncodeHintType.HEIGHT, height);
            options.Hints.Add(EncodeHintType.MARGIN, 0);
            options.Hints.Add(EncodeHintType.PURE_BARCODE, true);
            if (width.HasValue)
            {
                options.Hints.Add(EncodeHintType.WIDTH, width.Value);
            }
            return new BarcodeWriter
            {
                Format = format,
                Options = options
            }.Write(data);
        }
        /// <summary>
        /// 根据数据生成二维码，并转换为位图
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="size">尺寸</param>
        /// <param name="format">条码类型</param>
        /// <returns>条码位图</returns>
        public static Bitmap ToQRCode(string data, int size)
        {
            var options = new QrCodeEncodingOptions();
            options.Hints.Add(EncodeHintType.CHARACTER_SET, "UTF-8");
            options.Hints.Add(EncodeHintType.DISABLE_ECI, true);
            options.Hints.Add(EncodeHintType.HEIGHT, size);
            options.Hints.Add(EncodeHintType.MARGIN, 0);
            options.Hints.Add(EncodeHintType.WIDTH, size);
            return new ZXing.Windows.Compatibility.BarcodeWriter
            {
                Format = BarcodeFormat.QR_CODE,
                Options = options,
                //Renderer = new BitmapRenderer()
            }.Write(data);

            ////var options = new QrCodeEncodingOptions();
            //Dictionary<EncodeHintType, Object> hints = new Dictionary<EncodeHintType, object>();
            //hints.Add(EncodeHintType.CHARACTER_SET, "UTF-8");
            //hints.Add(EncodeHintType.DISABLE_ECI, true);
            ////options.Hints.Add(EncodeHintType.HEIGHT, size);
            //hints.Add(EncodeHintType.MARGIN, 0);
            ////options.Hints.Add(EncodeHintType.WIDTH, size);
            //MultiFormatWriter writer = new MultiFormatWriter();
            //ZXing.Common.BitMatrix bm = writer.encode(data, ZXing.BarcodeFormat.QR_CODE, size, size, hints);
            //BarcodeWriter barcodeWriter = new BarcodeWriter();
            //System.Drawing.Bitmap bmp = RemoveWhiteMargin(bm, barcodeWriter.Write(bm));//去除白边   
            ////var res = new ZXing.Windows.Compatibility.BarcodeWriter
            ////{
            ////    Format = BarcodeFormat.QR_CODE,
            ////    Options = options,
            ////    //Renderer = new BitmapRenderer()
            ////}.Write(data);
            //return bmp;
        }
        private static Bitmap RemoveWhiteMargin(ZXing.Common.BitMatrix bitMatrix, Bitmap bitmap)
        {

            //获取参数
            int[] rec = bitMatrix.getEnclosingRectangle();
            int left = rec[0];
            int top = rec[1];
            int width = rec[2];
            int height = rec[3];
            Bitmap newImg = new Bitmap(width, height);
            Graphics g = Graphics.FromImage(newImg);
            //截取
            g.DrawImage(bitmap, 0, 0, new Rectangle(left, top, newImg.Width, newImg.Height), GraphicsUnit.Pixel);
            return newImg;
        }
    }
    public sealed class ImgHelper
    {
        public static Bitmap ReadImageFile(string path)
        {
            if (!File.Exists(path))
            {
                return null; // 文件不存在
            }
            using (FileStream fs = File.OpenRead(path)) // 打开文件
            {
                byte[] image = new byte[fs.Length]; // 创建字节数组
                fs.Read(image, 0, image.Length); // 读取文件内容
                System.Drawing.Image result = System.Drawing.Image.FromStream(fs); // 从流创建图像

                Bitmap bit = new Bitmap(result); // 创建Bitmap
                return bit;
            }
        }
        public static string ImgToBase64String(Bitmap bmp)
        {
            try
            {
                MemoryStream ms = new MemoryStream();
                bmp.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                byte[] arr = new byte[ms.Length];
                ms.Position = 0;
                ms.Read(arr, 0, (int)ms.Length);
                ms.Close();
                return Convert.ToBase64String(arr);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

    }
}
