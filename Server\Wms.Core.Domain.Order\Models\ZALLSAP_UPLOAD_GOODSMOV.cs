﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public class ZALLSAP_UPLOAD_GOODSMOV
    {
        private string sOURCESYSField;

        private string tARGETSYSField;

        private string uPDATETIMEField;

        private ZALLSAP_UPLOAD_GOODSMOV_2[] iT_MATDOC_DETAILSField;

        private ZALLSAP_UPLOAD_GOODSMOV_1[] iT_MATDOC_HEADField;

        private ZALLSAP_UPLOAD_GOODSMOV_3[] oT_MATDOCField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string SOURCESYS
        {
            get
            {
                return this.sOURCESYSField;
            }
            set
            {
                this.sOURCESYSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string TARGETSYS
        {
            get
            {
                return this.tARGETSYSField;
            }
            set
            {
                this.tARGETSYSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string UPDATETIME
        {
            get
            {
                return this.uPDATETIMEField;
            }
            set
            {
                this.uPDATETIMEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILS
        {
            get
            {
                return this.iT_MATDOC_DETAILSField;
            }
            set
            {
                this.iT_MATDOC_DETAILSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEAD
        {
            get
            {
                return this.iT_MATDOC_HEADField;
            }
            set
            {
                this.iT_MATDOC_HEADField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOC
        {
            get
            {
                return this.oT_MATDOCField;
            }
            set
            {
                this.oT_MATDOCField = value;
            }
        }
    }
}
