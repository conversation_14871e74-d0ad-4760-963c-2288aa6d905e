using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Wms.Core.Domain.VisualIntegration.Interfaces;
using Wms.Core.Domain.VisualIntegration.Models.Messages;

namespace Wms.Core.Domain.VisualIntegration.Services
{
    /// <summary>
    /// 数据转换器，处理业务数据的转换和映射
    /// </summary>
    public class DataTransformer : IDataTransformer
    {
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly ILogger<DataTransformer>? _logger;

        /// <summary>
        /// 构造函数，初始化JSON序列化选项
        /// </summary>
        /// <param name="logger">日志记录器（可选）</param>
        public DataTransformer(ILogger<DataTransformer>? logger = null)
        {
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = null, // 保持原始属性名
                WriteIndented = false, // 紧凑格式
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                PropertyNameCaseInsensitive = true, // 忽略属性名大小写
                AllowTrailingCommas = true, // 允许尾随逗号
                ReadCommentHandling = JsonCommentHandling.Skip, // 跳过注释
                NumberHandling = JsonNumberHandling.AllowReadingFromString // 允许从字符串读取数字
            };
        }

        /// <summary>
        /// 序列化消息对象为JSON字符串
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="message">消息对象</param>
        /// <returns>JSON字符串</returns>
        /// <exception cref="ArgumentNullException">当消息对象为null时抛出</exception>
        /// <exception cref="JsonException">当序列化失败时抛出</exception>
        public string SerializeMessage<T>(T message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            try
            {
                return JsonSerializer.Serialize(message, _jsonOptions);
            }
            catch (Exception ex)
            {
                throw new JsonException($"Failed to serialize message of type {typeof(T).Name}", ex);
            }
        }

        /// <summary>
        /// 反序列化JSON字符串为消息对象
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>消息对象</returns>
        /// <exception cref="ArgumentException">当JSON字符串为空或null时抛出</exception>
        /// <exception cref="JsonException">当反序列化失败时抛出</exception>
        public T DeserializeMessage<T>(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                throw new ArgumentException("JSON string cannot be null or empty", nameof(json));

            try
            {
                // 添加详细的调试日志
                _logger?.LogDebug("尝试反序列化JSON到类型 {TypeName}，JSON内容: {JsonContent}",
                    typeof(T).Name, json);

                var result = JsonSerializer.Deserialize<T>(json, _jsonOptions);
                if (result == null)
                    throw new JsonException($"Deserialization resulted in null for type {typeof(T).Name}");

                _logger?.LogDebug("JSON反序列化成功，类型: {TypeName}", typeof(T).Name);
                return result;
            }
            catch (JsonException ex)
            {
                _logger?.LogError(ex, "JSON反序列化失败，目标类型: {TypeName}，JSON内容: {JsonContent}，错误: {ErrorMessage}",
                    typeof(T).Name, json, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                throw new JsonException($"Failed to deserialize JSON to type {typeof(T).Name}", ex);
            }
        }

        /// <summary>
        /// 验证消息对象
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="message">消息对象</param>
        /// <returns>是否有效</returns>
        public bool ValidateMessage<T>(T message)
        {
            if (message == null)
                return false;

            try
            {
                bool isValid = true;

                // 1. 对于BaseMessage类型，首先验证基础字段
                if (message is BaseMessage baseMessage)
                {
                    isValid = ValidateBaseMessage(baseMessage);
                    if (!isValid)
                        return false;
                }

                // 2. 使用数据注解验证
                var validationContext = new ValidationContext(message);
                var validationResults = new List<ValidationResult>();
                isValid = isValid && Validator.TryValidateObject(message, validationContext, validationResults, true);

                // 3. 检查是否有自定义的Validate方法，并且只有在前面验证都通过时才调用
                if (isValid)
                {
                    var validateMethod = typeof(T).GetMethod("Validate", BindingFlags.Public | BindingFlags.Instance);
                    if (validateMethod != null && validateMethod.ReturnType == typeof(bool))
                    {
                        var result = validateMethod.Invoke(message, null);
                        if (result is bool customValidationResult)
                            isValid = isValid && customValidationResult;
                    }
                }

                return isValid;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证基础消息字段
        /// </summary>
        /// <param name="baseMessage">基础消息对象</param>
        /// <returns>是否有效</returns>
        private bool ValidateBaseMessage(BaseMessage baseMessage)
        {
            // FuncID不能为空
            if (string.IsNullOrWhiteSpace(baseMessage.FuncID))
                return false;

            // FuncSeqNo不能为空且必须是数字
            if (string.IsNullOrWhiteSpace(baseMessage.FuncSeqNo))
                return false;

            // 验证FuncSeqNo是否为有效数字（1-99999999）
            if (!int.TryParse(baseMessage.FuncSeqNo, out int seqNo))
                return false;

            if (seqNo < 1 || seqNo > 99999999)
                return false;

            return true;
        }
    }
}