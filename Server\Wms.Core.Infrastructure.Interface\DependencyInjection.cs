﻿using Microsoft.Extensions.DependencyInjection;

namespace Kean.Infrastructure.Interface
{
    /// <summary>
    /// 依赖注入
    /// </summary>
    public sealed class DependencyInjection
    {
        /// <summary>
        /// 初始化 Kean.Infrastructure.Interface.DependencyInjection 类的新实例
        /// </summary>
        /// <param name="services">服务描述符</param>
        public DependencyInjection(IServiceCollection services)
        {
            services.AddScoped<Domain.Interface.RemoteClients.IWcsClient, Wcs.IntermediateDbClient>();


            services.AddScoped<Domain.Interface.RemoteClients.ISapClient, SapWebClient>(); // 这是例子，项目不用请删除！！！
            // ！！！
            services.AddScoped<Domain.Interface.RemoteClients.IErpClient, ErpClient>(); // 这是例子，项目不用请删除！！！
            // ！！！
        }
    }
}
