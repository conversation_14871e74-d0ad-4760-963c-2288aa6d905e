﻿using AutoMapper;
using <PERSON>pper;
using Kean.Application.Query.Interfaces;
using Kean.Application.Query.ViewModels;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Application.Query.Implements
{
    /// <summary>
    /// 订单信息查询服务实现
    /// </summary>
    public sealed class OrderService(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : IOrderService
    {
        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetTypeList 方法
         */
        public async Task<IEnumerable<Ordtyp>> GetTypeList()
        {
            return mapper.Map<IEnumerable<Ordtyp>>(await database.From<T_PLAN_TYPE>().Select());
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetOrderCount 方法
         */
        public async Task<int> GetOrderCount(int? type, string number, string state, string creater, DateTime? createFrom, DateTime? createTo)
        {
            return (int)(await GetOrderSchema(type, number, state, creater, createFrom, createTo)
                .Single(p => new { Count = Function.Count(p.PLAN_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetOrderList 方法
         */
        public async Task<IEnumerable<ViewModels.Order>> GetOrderList(int? type, string number, string state, string creater, DateTime? createFrom, DateTime? createTo, string sort, int? offset, int? limit)
        {
            var action = (await database.From<T_PLAN_TYPE>().Select())
                .ToDictionary(
                    t => t.TYPE_ID,
                    t => t.TYPE_PROC.Split('-'));
            return (await GetOrderSchema(type, number, state, creater, createFrom, createTo)
                .Sort<T_PLAN_MAIN, ViewModels.Order>(sort, mapper)
                .Page(offset, limit)
                .Select())
                .Select(i =>
                {
                    var vm = mapper.Map<ViewModels.Order>(i);
                    vm.Action = action[i.PLAN_TYPE][i.PLAN_STATUS].Split(',');
                    //Alucard 
                    //vm.Action = action[i.PLAN_TYPE][1].Split(',');
                    return vm;
                });
        }

        /*
         * 组织 GetOrder 相关方法的条件
         */
        private ISchema<T_PLAN_MAIN> GetOrderSchema(int? type, string number, string state, string creater, DateTime? createFrom, DateTime? createTo)
        {
            var schema = database.From<T_PLAN_MAIN>();
            if (type.HasValue)
            {
                schema = schema.Where(p => p.PLAN_TYPE == type.Value);
            }
            if (number != null)
            {
                schema = schema.Where(p => p.PLAN_CODE.Contains(number));
            }
            if (state != null)
            {
                schema = schema.Where(p => p.PLAN_STATUS == Convert.ToInt32(state));
            }
            if (creater != null)
            {
                schema = schema.Where(p => p.PLAN_CREATER.Contains(creater));
            }
            if (createFrom.HasValue)
            {
                schema = schema.Where(p => p.PLAN_CREATE_TIME >= createFrom.Value);
            }
            if (createTo.HasValue)
            {
                schema = schema.Where(p => p.PLAN_CREATE_TIME <= createTo.Value.AddDays(1));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetLineCount 方法
         */
        public async Task<int> GetLineCount(int? order, int? state, string code)
        {
            return (int)(await GetLineSchema(order, state, code)
                .Single(p => new { Count = Function.Count(p.PLAN_LIST_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetLineList 方法
         */
        public async Task<IEnumerable<ViewModels.Order>> GetLineList(int? order, int? state, string code, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<ViewModels.Order>>(await GetLineSchema(order, state, code)
                .Sort<V_PLAN_LIST, ViewModels.Order>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetLine 相关方法的条件
         */
        private ISchema<V_PLAN_LIST> GetLineSchema(int? order, int? state, string code)
        {
            var schema = database.From<V_PLAN_LIST>();
            if (order.HasValue)
            {
                schema = schema.Where(p => p.PLAN_ID == order.Value);
            }
            switch (state)
            {
                case -1:
                    schema = schema.Where(p => p.FINISHED_QUANTITY < p.PLANNED_QUANTITY);
                    break;
                case 1:
                    schema = schema.Where(p => p.FINISHED_QUANTITY >= p.PLANNED_QUANTITY);
                    break;
            }
            if (!string.IsNullOrEmpty(code))
            {
                schema = schema.Where(p => p.GOODS_CODE.Contains(code));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetArchivedCount 方法
         */
        public async Task<int> GetArchivedCount(int? type, string number, string creater, DateTime? createFrom, DateTime? createTo, DateTime? finalFrom, DateTime? finalTo)
        {
            return (int)(await GetArchivedSchema(type, number, creater, createFrom, createTo, finalFrom, finalTo)
                .Single(p => new { Count = Function.Count(p.PLAN_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetOrderList 方法
         */
        public async Task<IEnumerable<ViewModels.Order>> GetArchivedList(int? type, string number, string creater, DateTime? createFrom, DateTime? createTo, DateTime? finalFrom, DateTime? finalTo, string sort, int? offset, int? limit)
        {
            var schema = GetArchivedSchema(type, number, creater, createFrom, createTo, finalFrom, finalTo)
                .Page(offset, limit);
            schema = sort == null ?
                schema.OrderBy(a => a.PLAN_FINAL_TIME, Infrastructure.Database.Order.Descending) :
                schema.Sort<T_PLAN_HIS_MAIN, ViewModels.Order>(sort, mapper);
            return mapper.Map<IEnumerable<ViewModels.Order>>(await schema.Select());
        }

        /*
         * 组织 GetArchived 相关方法的条件
         */
        private ISchema<T_PLAN_HIS_MAIN> GetArchivedSchema(int? type, string number, string creater, DateTime? createFrom, DateTime? createTo, DateTime? finalFrom, DateTime? finalTo)
        {
            var schema = database.From<T_PLAN_HIS_MAIN>();
            if (type.HasValue)
            {
                schema = schema.Where(p => p.PLAN_TYPE == type.Value);
            }
            if (number != null)
            {
                schema = schema.Where(p => p.PLAN_CODE.Contains(number));
            }
            if (creater != null)
            {
                schema = schema.Where(p => p.PLAN_CREATER.Contains(creater));
            }
            if (createFrom.HasValue)
            {
                schema = schema.Where(p => p.PLAN_CREATE_TIME >= createFrom.Value);
            }
            if (createTo.HasValue)
            {
                schema = schema.Where(p => p.PLAN_CREATE_TIME <= createTo.Value.AddDays(1));
            }
            if (finalFrom.HasValue)
            {
                schema = schema.Where(p => p.PLAN_FINAL_TIME >= finalFrom.Value);
            }
            if (finalTo.HasValue)
            {
                schema = schema.Where(p => p.PLAN_FINAL_TIME <= finalTo.Value.AddDays(1));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetArchivedLineCount 方法
         */
        public async Task<int> GetArchivedLineCount(int? order, int[] category, string code, string name, string batch)
        {
            return (int)(await GetArchivedLineSchema(order, category, code, name, batch)
                .Single(p => new { Count = Function.Count(p.PLAN_LIST_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetArchivedLineList 方法
         */
        public async Task<IEnumerable<ViewModels.Order>> GetArchivedLineList(int? order, int[] category, string code, string name, string batch, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<ViewModels.Order>>(await GetArchivedLineSchema(order, category, code, name, batch)
                .Sort<V_PLAN_HIS_LIST, ViewModels.Order>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetArchivedLine 相关方法的条件
         */
        private ISchema<V_PLAN_HIS_LIST> GetArchivedLineSchema(int? order, int[] category, string code, string name, string batch)
        {
            var schema = database.From<V_PLAN_HIS_LIST>();
            if (order.HasValue)
            {
                schema = schema.Where(p => p.PLAN_ID == order.Value);
            }
            if (category != null)
            {
                schema = schema.Where(p => category.Contains(p.CLASS_ID.Value));
            }
            if (code != null)
            {
                schema = schema.Where(p => p.GOODS_CODE.Contains(code));
            }
            if (name != null)
            {
                schema = schema.Where(p => p.GOODS_NAME.Contains(name));
            }
            if (batch != null)
            {
                schema = schema.Where(p => p.GOODS_BATCH_NO.Contains(batch));
            }
            return schema;
        }
        public async Task<IEnumerable<ViewModels.Order>> GetOrderListBy204099(int? type, string number)
        {
            try
            {
                var res = database.Context.Query<T_PLAN_MAIN>($"SELECT * FROM T_PLAN_MAIN WHERE PLAN_ID IN ({number})", transaction: database.Context.Transaction);
                //var res = await database.From<T_PLAN_MAIN>().Where(x => x.PLAN_TYPE == 2 && (x.PLAN_CODE_40.Contains(number) || x.PLAN_CODE_99.Contains(number) || x.PLAN_CODE.Contains(number))).Select();
                return mapper.Map<IEnumerable<ViewModels.Order>>(res);
            }
            catch (Exception)
            {

                throw;
            }
        }/*
         * 实现 Kean.Application.Query.Interfaces.IOrderService.GetLineListByPlanCode 方法
         */
        public async Task<IEnumerable<ViewModels.Order>> GetLineListByPlanCode(string planCodes)
        {
            //IEnumerable<V_PLAN_LIST> lst = await database.From<V_PLAN_LIST>()
            //      .Where(c => (c.PLAN_CODE.Contains(planCode) || c.PLAN_CODE_40.Contains(planCode) || c.PLAN_CODE_99.Contains(planCode)) && c.PLAN_TYPE == 2 && (c.PLANNED_QUANTITY - c.ORDERED_QUANTITY - c.FINISHED_QUANTITY) > 0).Select();
            //return mapper.Map<IEnumerable<ViewModels.Order>>(lst);
            List<V_PLAN_LIST> lstResult = new List<V_PLAN_LIST>();
            List<string> lstPlancode = planCodes.Split(",").ToList();
            foreach (string plancode in lstPlancode)
            {
                var lstPlanList = await database.From<V_PLAN_LIST>()
                     .Where(c => (c.PLAN_CODE.Contains(plancode) || c.PLAN_CODE_40.Contains(plancode) || c.PLAN_CODE_99.Contains(plancode)) && c.PLAN_TYPE == 2 && (c.PLANNED_QUANTITY - c.ORDERED_QUANTITY - c.FINISHED_QUANTITY) > 0).Select();
                lstResult.AddRange(lstPlanList);
            }
            lstResult = lstResult.DistinctBy(p => new { p.PLAN_CODE, p.GOODS_ID }).ToList();
            return mapper.Map<IEnumerable<ViewModels.Order>>(lstResult);

            //return mapper.Map<IEnumerable<ViewModels.Order>>(await database.From<V_PLAN_LIST>()
            //     .Where(c => (c.PLAN_CODE.Contains(planCode) || c.PLAN_CODE_40.Contains(planCode) || c.PLAN_CODE_99.Contains(planCode)) && c.PLAN_TYPE == 2 && (c.PLANNED_QUANTITY - c.ORDERED_QUANTITY - c.FINISHED_QUANTITY) > 0).Select());
        }

        ///*
        // * 实现 Kean.Application.Query.Interfaces.IOrderService.GetLineListByPlanCode 方法
        // */
        //public async Task<IEnumerable<ViewModels.Order>> GetLineListByPlanCode(string planCode)
        //{
        //    return mapper.Map<IEnumerable<ViewModels.Order>>(await database.From<V_PLAN_LIST>()
        //         .Where(c => (c.PLAN_CODE.Contains(planCode) || c.PLAN_CODE_40.Contains(planCode) || c.PLAN_CODE_99.Contains(planCode)) && c.PLAN_TYPE == 2 && (c.PLANNED_QUANTITY - c.ORDERED_QUANTITY - c.FINISHED_QUANTITY) > 0).Select());
        //}
    }
}
