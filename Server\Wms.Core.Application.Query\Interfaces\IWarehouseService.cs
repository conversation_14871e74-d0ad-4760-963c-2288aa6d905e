﻿using Kean.Application.Query.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Kean.Application.Query.Interfaces
{
    /// <summary>
    /// 表示库房查询服务
    /// </summary>
    public interface IWarehouseService
    {
        /// <summary>
        /// 获取库房数量
        /// </summary>
        /// <returns>结果</returns>
        Task<int> GetWarehouseCount();

        /// <summary>
        /// 获取库房列表
        /// </summary>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Warehouse>> GetWarehouseList(string sort, int? offset, int? limit);

        /// <summary>
        /// 获取库区数量
        /// </summary>
        /// <param name="id">标识</param>
        /// <param name="warehouse">库房</param>
        /// <returns>结果</returns>
        Task<int> GetAreaCount(int[] id, int? warehouse);

        /// <summary>
        /// 获取库区列表
        /// </summary>
        /// <param name="id">标识</param>
        /// <param name="warehouse">库房</param>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Area>> GetAreaList(int[] id, int? warehouse, string sort, int? offset, int? limit);

        /// <summary>
        /// 获取货位数量
        /// </summary>
        /// <param name="area">库区</param>
        /// <param name="type">类型</param>
        /// <param name="in">是否可入</param>
        /// <param name="out">是否可出</param>
        /// <param name="name">名称</param>
        /// <param name="state">状态</param>
        /// <returns>结果</returns>
        Task<int> GetCellCount(int[] area, string type, bool? @in, bool? @out, string name, string state);

        /// <summary>
        /// 获取货位列表
        /// </summary>
        /// <param name="area">库区</param>
        /// <param name="type">类型</param>
        /// <param name="in">是否可入</param>
        /// <param name="out">是否可出</param>
        /// <param name="name">名称</param>
        /// <param name="state">状态</param>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Cell>> GetCellList(int[] area, string type, bool? @in, bool? @out, string name, string state, string sort, int? offset, int? limit);

        Task<IEnumerable<Cell>> GetCellListNodata(int[] area, string type, bool? @in, bool? @out, string name, string state, string sort, int? offset, int? limit);


        public Task<int> getOutPutDestination(int? startCellId, string startCellCode, int areaId);


        public Task<string> getOutPutDestinationCode(int? startCellId, string startCellCode, int areaId);
    }
}
