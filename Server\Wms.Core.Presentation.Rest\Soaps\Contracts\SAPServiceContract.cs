﻿/*
 * 这是一个例子，对外发布一个 WebService 接口
 * 这里是契约，包含一个 CreateTask 方法
 */

using System.ServiceModel;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Soaps.Contracts
{
    [ServiceContract]
    public interface SAPServiceContract
    {

        [OperationContract]
        string UpdateMaterial(string xml);
        [OperationContract]
        string UpdateStorageStatus(string xml);
        [OperationContract]
        string SendTask(string xml);
    }
}
