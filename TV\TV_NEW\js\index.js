
$(window).load(function () {
    // 
    $(".loading").fadeOut();
})

$(function () {
    //更新时间
    console.log('asdad');
    var t = setTimeout(updateTime, 1000);
    var t2 = setTimeout(updateTime2, 1200);
    //获取服务器数据
    var title = null;
    var truckCode = null;
    var platform = null;
    var category = null;
    var taskNo = null;
    var remark = null;
    var quantity = null;
    var pickQuantity = null;
    var ip = null;
    var search = window.location.search.split('?')[1];;
    fetch('https://api.ipify.org/?format=json')


        .then(response => response.json())


        .then(data => {


            ip = data.ip;
            console.log(ip);

        })


        .catch(error => console.error('Error fetching IP address:', error));

    //更新时间方法
    function updateTime2() {

        try {
            $.ajax({
                url: `http://localhost:14713/tv?ip=${ip}&search=${search}`,
                // url: "http://localhost:14713/Home/in",
                type: "get",
                datatype: "json",
                async: false,
                success: function (data) {
                    console.log('请求成功:', data);
                    const obj = JSON.parse(data);
                    title = obj.Title;
                    time = obj.Time;
                    truckCode = obj.TruckCode;
                    platform = obj.Platform;
                    category = obj.Category;
                    taskNo = obj.TaskNo;
                    remark = obj.Remark;
                    quantity = obj.Quantity;
                    pickQuantity = obj.PickQuantity;
                    document.getElementById('title').innerText = title;
                    document.getElementById('truckCode').innerText = truckCode;
                    document.getElementById('platform').innerText = platform;
                    document.getElementById('category').innerText = category;
                    document.getElementById('taskNo').innerText = taskNo;
                    document.getElementById('remark').innerText = remark;
                    document.getElementById('quantity').innerText = quantity;
                    document.getElementById('pickQuantity').innerText = pickQuantity;

                    t2 = setTimeout(updateTime2, 120000);
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {

                    console.log(XMLHttpRequest.status);
                    console.log(XMLHttpRequest.readyState);
                    console.log(textStatus);
                }
            })
        } catch (error) {
            location.reload();
        }
    }
    function updateTime() {
        clearTimeout(t);//清除定时器
        dt = new Date();
        var y = dt.getFullYear();
        var mt = dt.getMonth() + 1;
        var day = dt.getDate();
        var h = dt.getHours();
        var m = dt.getMinutes();
        var s = dt.getSeconds();
        $("#time").html(y + "/" + mt + "/" + day + " " + h + ":" + m + ":" + s + "")
        t = setTimeout(updateTime, 1000); //设定定时器，循环运行     
    }
})