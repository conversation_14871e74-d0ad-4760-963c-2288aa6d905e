﻿using AutoMapper;
using Kean.Domain.Shared;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Models;
using Kean.Domain.Task.Repositories;
using Kean.Infrastructure.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;

namespace Kean.Domain.Task.CommandHandlers
{
    /// <summary>
    /// 创建输送命令处理程序
    /// </summary>
    public sealed class ConveyCommandHandler(
        ILogger<ConveyCommandHandler> logger,       // 日志
        IServiceProvider serviceProvider,           // 服务供应商
        IMapper mapper,                             // 模型映射
        ICommandBus commandBus,                     // 命令总线
        INotification notification,                 // 总线通知
        ITaskRepository taskRepository,             // 任务仓库
        IWarehouseRepository warehouseRepository,   // 库房仓库
        BarcodeInterpreter barcodeInterpreter,      // 条码解释器
        BarcodeValidator barcodeValidator,          // 条码验证器
        WcsMode wcsMode,                            // WCS 模式
        IStockService stockService                  // 仓储域共享服务
    ) : CommandHandler<ConveyCommand>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(ConveyCommand command, CancellationToken cancellationToken)
        {
            if (command.ValidationResult.IsValid)
            {
                // 条码格式校验
                //if (!await barcodeValidator.Validate(command.Barcode, true))
                //{
                //    await commandBus.Notify(nameof(command.Barcode), ErrorEnum.条码格式不正确, command.Barcode,
                //            cancellationToken: cancellationToken);
                //    return;
                //}
                // 申请条码锁
                var @lock = await taskRepository.AcquireLock(command.Barcode);
                if (@lock == null)
                {
                    await commandBus.Notify(nameof(command.Barcode), ErrorEnum.载具操作繁忙, command.Barcode,
                            cancellationToken: cancellationToken);
                    return;
                }
                using (@lock)
                {
                    var @case = new Case();
                    // 任务校验
                    if (await taskRepository.HasTask(command.Barcode))
                    {
                        await commandBus.Notify(nameof(command.Barcode), ErrorEnum.载具正在执行任务, command.Barcode,
                                cancellationToken: cancellationToken);
                        return;
                    }
                    var cell = await stockService.GetCell(command.Barcode);
                    if (cell.HasValue && await warehouseRepository.IsCell(cell.Value) == true)
                    {
                        await commandBus.Notify(nameof(command.Barcode), ErrorEnum.不允许操作立库中的库存, command.Barcode,
                                cancellationToken: cancellationToken);
                        return;
                    }
                    // 起点校验
                    var original = command.Original switch
                    {
                        int i => await warehouseRepository.GetStationById(i),
                        long i => await warehouseRepository.GetStationById((int)i),
                        string s when s.StartsWith("name:") => await warehouseRepository.GetStationByName(s[5..], command.Warehouse),
                        string s when s.StartsWith("device:") => await warehouseRepository.GetStationByDevice(s[7..], command.Warehouse),
                        Station s => s,
                        _ => null
                    };
                    var pallet = await barcodeInterpreter.Interpret(command.Barcode);
                    if (@case.When(original == null, ErrorEnum.任务起点不合法)
                        || @case.When(original.Warehouse != command.Warehouse, ErrorEnum.任务起点不在当前库房)
                        || @case.When(original.AllowIn != true, ErrorEnum.任务起点不具备送入功能)
                        || @case.When(original.Pallet?.Contains(pallet) == false, ErrorEnum.任务起点不支持载具类型)
                        || @case.When(original.Spec?.Adaptive(command.Spec) == false, ErrorEnum.任务起点不支持载具规格))
                    {
                        await commandBus.Notify(nameof(command.Original), @case.Message, command.Original,
                                cancellationToken: cancellationToken);
                        return;
                    }
                    // 终点校验
                    var destination = command.Destination switch
                    {
                        int i => await warehouseRepository.GetStationById(i),
                        long i => await warehouseRepository.GetStationById((int)i),
                        string s when s.StartsWith("name:") => await warehouseRepository.GetStationByName(s[5..], command.Warehouse),
                        string s when s.StartsWith("device:") => await warehouseRepository.GetStationByDevice(s[7..], command.Warehouse),
                        Station s => s,
                        _ => null
                    };
                    if (@case.When(destination == null, ErrorEnum.任务终点不合法)
                        || @case.When(destination?.Warehouse != command.Warehouse, ErrorEnum.任务终点不在当前库房)
                        || @case.When(destination.AllowOut != true, ErrorEnum.任务终点不具备送出功能)
                        || @case.When(destination.Pallet?.Contains(pallet) == false, ErrorEnum.任务终点不支持载具类型)
                        || @case.When(destination.Spec?.Adaptive(command.Spec) == false, ErrorEnum.任务终点不支持载具规格)
                        || @case.When(destination.Edge?.Reachable(original.Edge) == false, ErrorEnum.起点到终点之间不存在可用路径))
                    {
                        await commandBus.Notify(nameof(command.Destination), @case.Message, command.Destination,
                            cancellationToken: cancellationToken);
                        return;
                    }
                    // 路径校验
                    //if (await (wcsMode == WcsMode.Classic ?
                    //    serviceProvider.GetRequiredService<IInterfaceService>().SyncOutput("WCS", "Route", null, $"{{\"Original\":\"{original.Device}\",\"Destination\":\"{destination.Device}\"}}", new { Original = original.Device, Destination = destination.Device }).ContinueWith(t => t.Result is null) :
                    //    serviceProvider.GetRequiredService<IDeviceService>().RouteWeight(original.Device, destination.Device).ContinueWith(t => t.Result is null)))
                    //{
                    //    await commandBus.Notify(nameof(command.Destination), ErrorEnum.起点到终点之间不存在可用路径, command.Destination,
                    //        cancellationToken: cancellationToken);
                    //    return;
                    //}
                    // 对向冲突
                    if (!command.Manual)
                    {
                        if ((original.AllowOut && destination.AllowIn && await taskRepository.HasTask(destination.Id, original.Id)) ||
                            (original.AllowOut && destination.Inner.HasValue && await taskRepository.HasTask(destination.Inner, original.Id)) ||
                            (original.Outer.HasValue && destination.AllowIn && await taskRepository.HasTask(destination.Id, original.Outer)) ||
                            (original.Outer.HasValue && destination.Inner.HasValue && await taskRepository.HasTask(destination.Inner, original.Outer)))
                        {
                            await commandBus.Notify($"{nameof(command.Original)},{nameof(command.Destination)}", ErrorEnum.存在对向任务, new object[] { command.Original, command.Destination },
                                cancellationToken: cancellationToken);
                            return;
                        }
                    }
                    // 时间戳
                    command.Timestamp ??= DateTime.Now;
                    // 切点事件
                    var event0 = mapper.Map<ConveyExecutingEvent>(command);
                    event0.Original = original;
                    event0.Destination = destination;
                    await commandBus.Trigger(event0, cancellationToken);
                    if (notification.Count == 0)
                    {
                        // 创建任务
                        var task = mapper.Map<Models.Task>(command);
                        task.Type = TaskType.Convey;
                        task.Original = original.Id;
                        task.Destination = destination.Id;
                        Output(nameof(command.Id), await taskRepository.CreateTask(task));
                        // 成功事件
                        var event1 = mapper.Map<ConveySuccessEvent>(command);
                        event1.Original = original;
                        event1.Destination = destination;
                        await commandBus.Trigger(event1, cancellationToken);
                        if (notification.Count == 0)
                        {
                            logger.LogInformation("创建输送任务 #{Id}：条码【{Barcode}】，起点【{Original}】，终点【{Destination}】，标签【{Tag}】。",
                                command.Id,
                                command.Barcode,
                                original.Id,
                                destination.Id,
                                command.Tag);
                            // 手动
                            if (command.Manual)
                            {
                                await commandBus.Execute(new CompleteCommand { Id = command.Id }, cancellationToken);
                            }
                        }
                    }
                }
            }
            else
            {
                await commandBus.Notify(command.ValidationResult,
                    cancellationToken: cancellationToken);
            }
        }
    }
}
