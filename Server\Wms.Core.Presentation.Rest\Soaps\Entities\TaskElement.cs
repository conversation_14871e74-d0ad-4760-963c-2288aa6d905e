﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{

    public class TaskElement
    {
        //[XmlArray("Element")]
        //[XmlArrayItem("HEADER", typeof(HEADER))]
        //[XmlArrayItem("DETAIL", typeof(DETAIL))]
        //public object[] objects { get; set; }
        [XmlElement("HEADER")]
        public List<HEADER> HEADER { get; set; } = new List<HEADER>();

        [XmlElement("DETAIL")]
        public List<DETAIL> DETAIL { get; set; } = new List<DETAIL>();
    }
}
