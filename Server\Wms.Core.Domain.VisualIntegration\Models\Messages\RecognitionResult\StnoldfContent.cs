using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Wms.Core.Domain.VisualIntegration.Models.Messages.RecognitionResult
{
    /// <summary>
    /// 辨识结果内容
    /// </summary>
    public class StnoldfContent
    {
        /// <summary>
        /// 入库站位，第一車間："1111","1101"，第二車間："1201","1204"
        /// </summary>
        [JsonPropertyName("Stno")]
        [Required]
        [StringLength(4, MinimumLength = 4)]
        public string Stno { get; set; } = string.Empty;

        /// <summary>
        /// 货板流水号，用以辨识每一棧板的唯一号码
        /// </summary>
        [JsonPropertyName("SER_ID")]
        [Required]
        [StringLength(16)]
        public string SER_ID { get; set; } = string.Empty;

        /// <summary>
        /// 工厂别
        /// </summary>
        [JsonPropertyName("Fact_No")]
        [Required]
        [StringLength(1)]
        public string Fact_No { get; set; } = string.Empty;

        /// <summary>
        /// 生产年月日，格式："YYYYMMDD"
        /// </summary>
        [JsonPropertyName("Prod_Date")]
        [Required]
        [StringLength(8, MinimumLength = 8)]
        [RegularExpression(@"^\d{8}$", ErrorMessage = "Prod_Date must be in format YYYYMMDD")]
        public string Prod_Date { get; set; } = string.Empty;

        /// <summary>
        /// 生产线别
        /// </summary>
        [JsonPropertyName("Prod_Line")]
        [Required]
        [StringLength(2)]
        public string Prod_Line { get; set; } = string.Empty;

        /// <summary>
        /// 生产时间，格式："HHMM"
        /// </summary>
        [JsonPropertyName("Prod_Time")]
        [Required]
        [StringLength(4, MinimumLength = 4)]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "Prod_Time must be in format HHMM")]
        public string Prod_Time { get; set; } = string.Empty;

        /// <summary>
        /// 线别生产箱数序号
        /// </summary>
        [JsonPropertyName("Line_CartonNo")]
        [Required]
        [StringLength(5)]
        public string Line_CartonNo { get; set; } = string.Empty;

        /// <summary>
        /// 识别码
        /// </summary>
        [JsonPropertyName("IDCode")]
        [Required]
        [StringLength(20)]  // 增加长度限制以支持测试数据
        public string IDCode { get; set; } = string.Empty;

        /// <summary>
        /// 69码
        /// </summary>
        [JsonPropertyName("BAR_CODE")]
        [Required]
        [StringLength(13)]
        public string BAR_CODE { get; set; } = string.Empty;

        /// <summary>
        /// 实际入库箱数。默认是满垛箱数给0，零散板给大于0。
        /// </summary>
        [JsonPropertyName("StoInQty")]
        [Required]
        [StringLength(3)]
        public string StoInQty { get; set; } = string.Empty;

        /// <summary>
        /// VIS对于该板识别有问题的异常码
        /// 0：没有异常
        /// >0：辨识系统对于该板识别有问题的异常码
        /// </summary>
        [JsonPropertyName("ErrCode")]
        [Required]
        [StringLength(4)]
        public string ErrCode { get; set; } = "0";

        /// <summary>
        /// VIS对于该板识别有问题的异常说明
        /// </summary>
        [JsonPropertyName("Code_Descb")]
        [StringLength(24)]
        public string Code_Descb { get; set; } = string.Empty;
    }
}