﻿using FluentValidation;
using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace Kean.Domain.Task.Commands
{
    /// <summary>
    /// 设备触发命令
    /// </summary>
    public class TriggerCommand : CommandValidator<TriggerCommand>, ICommand
    {
        /// <summary>
        /// 状态
        /// </summary>
        public TriggerState State { get; set; }

        /// <summary>
        /// 标识
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public int Warehouse { get; set; }

        /// <summary>
        /// 设备
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public JObject Parameter { get; set; }

        /// <summary>
        /// 执行记数
        /// </summary>
        public int? Executed { get; set; }

        /// <summary>
        /// 结果
        /// </summary>
        public TriggerResult Result { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        public string Parameter01 { get; set; }

        public string Parameter02 { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int? Timeout { get; set; }

        /// <summary>
        /// 验证项
        /// </summary>
        protected override void Validation()
        {
            RuleFor(r => r.Id).NotEmpty().When(r => r.State == TriggerState.Executing || r.State == TriggerState.Executed || r.State == TriggerState.Cancelled).WithMessage(ErrorEnum.标识不允许空);
        }

        /// <summary>
        /// 异步队列
        /// </summary>
        [Output]
        public IEnumerable<Trigger> Queue { get; private set; }

        /// <summary>
        /// 降级命令
        /// </summary>
        [Output]
        public TriggerCommand Fallback { get; private set; }

        /// <summary>
        /// 命令日志
        /// </summary>
        CommandLogger ICommand.Log() =>
            State switch
            {
                TriggerState.Executing => new("响应设备触发：标识【{Id}】，库房【{Warehouse}】，设备【{Device}】，类型【{Type}】，参数【{Parameter}】。", Id, Warehouse, Device, Type, Parameter.ToString(Formatting.None)),
                TriggerState.Cancelled => new("取消设备触发：标识【{Id}】。", Id),
                _ => null
            };
    }
}
