using Kean.Infrastructure.Hangfire;
using Kean.Infrastructure.SignalR;
using Kean.Infrastructure.Soap;
using Kean.Presentation.Rest.Backgrounds;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Wms.Core.Domain.VisualIntegration.Extensions;
using Kean.Presentation.Rest.Services;
using System;

namespace Kean.Presentation.Rest
{
    /// <summary>
    /// 应用程序启动类
    /// </summary>
    public class Startup(IConfiguration configuration)
    {
        /// <summary>
        /// 依赖注入
        /// </summary>
        private static IServiceCollection DependencyInject(IServiceCollection services) => services
            .Add<Infrastructure.Configuration.DependencyInjection>()
            .Add<Infrastructure.Connection.DependencyInjection>()
            .Add<Infrastructure.Database.DependencyInjection>()
            .Add<Infrastructure.Interface.DependencyInjection>()
            .Add<Infrastructure.NoSql.DependencyInjection>()
            .Add<Infrastructure.Repository.DependencyInjection>()
            .Add<Domain.Seedwork.DependencyInjection>()
            .Add<Domain.App.DependencyInjection>()
            .Add<Domain.Basic.DependencyInjection>()
            .Add<Domain.Device.DependencyInjection>()
            .Add<Domain.Identity.DependencyInjection>()
            .Add<Domain.Interface.DependencyInjection>()
            .Add<Domain.Message.DependencyInjection>()
            .Add<Domain.Material.DependencyInjection>()
            .Add<Domain.Order.DependencyInjection>()
            .Add<Domain.Stock.DependencyInjection>()
            .Add<Domain.Task.DependencyInjection>()
            .Add<Application.Command.DependencyInjection>()
            .Add<Application.Query.DependencyInjection>()
            .Add<Application.Utilities.DependencyInjection>();

        /// <summary>
        /// 将服务添加到容器
        /// </summary>
        public void ConfigureServices(IServiceCollection services) =>
            DependencyInject(services
                .AddSwaggerGen(options =>
                {
                    options.OperationFilter<SwaggerFilter>();
                    options.CustomSchemaIds(_ => Guid.NewGuid().ToString("N"));
                    options.OrderActionsBy(a => a.RelativePath);
                    //options.IncludeXmlComments("Swagger.xml", true);
                    options.SwaggerDoc(configuration["Swagger:Version"], configuration.GetSection("Swagger").Get<OpenApiInfo>());
                })
                .AddCors(options =>
                {
                    options.AddDefaultPolicy(policy =>
                    {
                        policy.WithOrigins(configuration["AllowedOrigins"].Split(';'))
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .AllowCredentials();
                    });
                })
                //.AddBackground(options =>
                //{
                //    options.Host<SiemensPlcBackground>();
                //})
                .AddHangfire(options =>
                {
                    //options.RecurringJobs.Add<Jobs.InstructionGenerateJob>();
                    //options.RecurringJobs.Add<Jobs.InstructionSendJob>();
                    //options.RecurringJobs.Add<Jobs.InstructionCompleteJob>();
                    options.RecurringJobs.Add<Jobs.WcsApplyJob>();
                    options.RecurringJobs.Add<Jobs.WcsFeedbackJob>();
                    options.RecurringJobs.Add<Jobs.TaskTriggerJob>();  // 任务触发作业 - 处理触发队列，确保TriggerExecutingEvent能被正确发布
                    //options.RecurringJobs.Add<Jobs.WmsLockJob>(); //自动锁定轮询
                    //options.RecurringJobs.Add<Jobs.WmsManageJob>(); //自动下架轮询
                    options.RecurringJobs.Add<Jobs.MiddlewareJob>("* * * 31 2 ?");  // 这是轮询中间媒介的例子，项目不用请删除！！！
                    options.RecurringJobs.Add<Jobs.ErpInputJob>("* * * 31 2 ?");    // 这是轮询异步输入的例子，项目不用请删除！！！
                    options.RecurringJobs.Add<Jobs.ErpOutputJob>("* * * 31 2 ?");   // 这是轮询异步输出的例子，项目不用请删除！！！
                })
                .AddSignalR(options =>
                {
                    options.Hubs.Add<Hubs.IdentityHub>();
                    options.Hubs.Add<Hubs.MessageHub>();
                    options.Hubs.Add<Hubs.MonitorHub>();
                })
                .AddSoap(options =>                                                                          // 这是发布 WebService 服务的例子，项目不用请删除！！！
                {
                    options.Services.Add<Soaps.Contracts.SAPServiceContract, Soaps.Services.SAPService>(); // 这是发布 WebService 服务的例子，项目不用请删除！！！
                    options.Services.Add<Soaps.Contracts.DemoServiceContract, Soaps.Services.DemoService>(); // 这是发布 WebService 服务的例子，项目不用请删除！！！
                })                                                                                           // 这是发布 WebService 服务的例子，项目不用请删除！！！
                .AddControllers(options =>
                {
                    options.Filters.Add<ActionFilter>();
                    options.Filters.Add<ExceptionFilter>();
                    options.InputFormatters.Add(new TextPlainInputFormatter());
                }))
            .AddVisualIntegrationServices()      // ע���Ӿ��������
            .AddHostedService<VisualIntegrationHostedService>() // ��3���е�������װ
            .Startup();

        /// <summary>
        /// 配置 HTTP 请求管道
        /// </summary>
        public void Configure(IApplicationBuilder app) => app
            .UseHangfire()
            .UseSwagger()
            .UseRouting()
            .UseCors()
            .UseBlacklist()
            .UseAuthentication()
            .UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHubs();
                endpoints.MapSoaps(); // 这是发布 WebService 服务的例子，项目不用请删除！！！
            });
    }
}
