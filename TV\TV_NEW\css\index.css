html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    background-color: black;
    /* 设置背景色 */

}

body div {
    height: 20%;

}

body .title {
    color: rgb(59, 162, 194);

}

.title .titleMain {
    height: 20%;
    width: 100%;
    float: left;
    text-align: center;
    position: absolute;
    z-index: 1;
    font-size: 100px;
}

.title .titleTime {
    height: 20%;
    width: 100%;
    float: right;
    position: absolute;
    font-size: xx-large;
    text-align: right;
    z-index: 99;
}

body .span2 {
    border: solid rgb(184, 181, 197);
    width: 25%;
    height: 100%;
    display: inline-block;
    text-align: center;
    font-size: 100px;
}

body .div2 {
    display: flex
}

body .span3 {
    border: solid rgb(184, 181, 197);
    width: 75%;
    height: 100%;
    display: inline-block;
    text-align: center;
    font-size: 100px;

}

body .colorS {
    color: rgb(226, 138, 36);
}

body .colorC {
    color: white;
}

body .span4 {
    border: solid rgb(184, 181, 197);
    width: 37.5%;
    height: 100%;
    display: inline-block;
    text-align: center;
    font-size: 100px;

}