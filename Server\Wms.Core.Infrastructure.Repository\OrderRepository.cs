﻿using AutoMapper;
using <PERSON>pper;
using Kean.Domain;
using Kean.Domain.Order;
using Kean.Domain.Order.Models;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Infrastructure.Repository
{
    /// <summary>
    /// 订单仓库
    /// </summary>
    public class OrderRepository(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : Domain.Order.Repositories.IOrderRepository
    {
        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetType 方法
         */
        public async Task<Domain.Order.Models.Type> GetType(int id)
        {
            return mapper.Map<Domain.Order.Models.Type>(await database.From<T_PLAN_TYPE>()
                .Where(t => t.TYPE_ID == id)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.IsSerialExist 方法
         */
        public async Task<bool> IsSerialExist(int id)
        {
            return (await database.From<T_PLAN_SERIAL>()
                .Where(s => s.SERIAL_ID == id)
                .Single(s => new { Count = Function.Count(s.SERIAL_ID) }))
                .Count > 0;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetSerialValue 方法
         */
        public async Task<int?> GetSerialValue(int id, DateTime timestamp, int timeout)
        {
            int? result = null;
            var guid = Guid.NewGuid().ToString("N");
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            while (!result.HasValue)
            {
                if (stopwatch.ElapsedMilliseconds >= timeout)
                {
                    stopwatch.Stop();
                    break;
                }
                else
                {
                    var serial = await database.From<T_PLAN_SERIAL>()
                        .Where(s => s.SERIAL_ID == id)
                        .Single();
                    var cycle = serial.SERIAL_CYCLE;
                    if (cycle == string.Empty)
                    {
                        serial.SERIAL_VALUE++;
                    }
                    else
                    {
                        cycle = timestamp.ToString("yyyyMMddHHmmss"[..cycle.Length]);
                        if (cycle == serial.SERIAL_CYCLE)
                        {
                            serial.SERIAL_VALUE++;
                        }
                        else
                        {
                            serial.SERIAL_VALUE = 1;
                        }
                    }
                    if (await database.From<T_PLAN_SERIAL>()
                        .Where(s => s.SERIAL_ID == id && s.SERIAL_VERSION == serial.SERIAL_VERSION)
                        .Update(new
                        {
                            SERIAL_CYCLE = cycle,
                            SERIAL_VALUE = serial.SERIAL_VALUE,
                            SERIAL_VERSION = guid,
                            UPDATE_TIME = DateTime.Now
                        }) > 0)
                    {
                        result = serial.SERIAL_VALUE;
                        stopwatch.Stop();
                        break;
                    }
                }
            }
            return result;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.CreateSerial 方法
         */
        public async Task CreateSerial(int id, string cycle)
        {
            var timestamp = DateTime.Now;
            await database.From<T_PLAN_SERIAL>().Add(new()
            {
                SERIAL_ID = id,
                SERIAL_CYCLE = cycle,
                SERIAL_VALUE = 1,
                SERIAL_VERSION = Guid.NewGuid().ToString("N"),
                CREATE_TIME = timestamp,
                UPDATE_TIME = timestamp
            });
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.IsOrderExist 方法
         */
        public async Task<bool> IsOrderExist(int id)
        {
            return (await database.From<T_PLAN_MAIN>()
                .Where(p => p.PLAN_ID == id)
                .Single(p => new { Count = Function.Count(p.PLAN_ID) }))
                .Count > 0;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.IsOrderExist 方法
         */
        public async Task<bool> IsOrderExist(string number)
        {
            return (await database.From<T_PLAN_MAIN>()
                .Where(p => p.PLAN_CODE == number)
                .Single(p => new { Count = Function.Count(p.PLAN_ID) }))
                .Count > 0;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.CreateOrder 方法
         */
        public async Task<int> CreateOrder(Domain.Order.Models.Order order)
        {
            var timestamp = DateTime.Now;
            var planMain = mapper.Map<T_PLAN_MAIN>(order);
            planMain.CREATE_TIME = timestamp;
            planMain.UPDATE_TIME = timestamp;
            await database.From<T_PLAN_MAIN>().Add(planMain);
            foreach (var item in order.Lines)
            {
                var planList = mapper.Map<T_PLAN_LIST>(item);
                planList.PLAN_ID = planMain.PLAN_ID;
                planList.CREATE_TIME = timestamp;
                planList.UPDATE_TIME = timestamp;
                if (planList.GOODS_ID == -1)
                {
                    var goodsMain = await database.From<T_GOODS_MAIN>().Where(g => g.GOODS_CODE == planList.PLAN_LIST_REMARK).Single();
                    if (goodsMain != null)
                    {
                        planList.GOODS_ID = goodsMain.GOODS_ID;
                    }
                }

                item.Id = Convert.ToInt32(await database.From<T_PLAN_LIST>().Add(planList));
            }
            return planMain.PLAN_ID;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.ModifyOrder 方法
         */
        public async Task ModifyOrder(int id, IEnumerable<OrderLine> lines)
        {
            var timestamp = DateTime.Now;
            var planLists = (await database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_ID == id)
                .Select())
                .ToDictionary(e => e.PLAN_LIST_ID, e => e);
            foreach (var item in lines)
            {
                if (item.Id == 0)
                {
                    var planList = mapper.Map<T_PLAN_LIST>(item);
                    planList.PLAN_ID = id;
                    planList.CREATE_TIME = timestamp;
                    planList.UPDATE_TIME = timestamp;
                    item.Id = Convert.ToInt32(await database.From<T_PLAN_LIST>().Add(planList));
                }
                else
                {
                    if (!planLists.TryGetValue(item.Id, out var planList))
                    {
                        throw new RepositoryException(ErrorEnum.订单行不存在, new(nameof(item.Id), item.Id));
                    }
                    if (planList.ORDERED_QUANTITY > 0 || planList.FINISHED_QUANTITY > 0)
                    {
                        throw new RepositoryException(ErrorEnum.已执行的订单行不允许修改, new(nameof(item.Id), item.Id));
                    }
                    planLists.Remove(item.Id);
                    planList = mapper.Map<T_PLAN_LIST>(item);
                    planList.PLAN_ID = id;
                    planList.UPDATE_TIME = timestamp;
                    await database.From<T_PLAN_LIST>().Update(planList, nameof(T_PLAN_LIST.CREATE_TIME));
                }
            }
            foreach (var item in planLists.Values)
            {
                if (item.ORDERED_QUANTITY > 0 || item.FINISHED_QUANTITY > 0)
                {
                    throw new RepositoryException(ErrorEnum.已执行的订单行不允许删除, new(nameof(OrderLine.Id), item.PLAN_LIST_ID));
                }
                await database.From<T_PLAN_LIST>().Delete(item);
            }
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.DeleteOrder 方法
         */
        public async Task DeleteOrder(int id)
        {
            //T_PLAN_MAIN tmp = await database.From<T_PLAN_MAIN>().Where(p => p.PLAN_ID == id).Single();
            //if (tmp != null && tmp.PLAN_TYPE == 1) { }
            //else
            //{
            await database.From<T_PLAN_MAIN>().Where(p => p.PLAN_ID == id).Delete();
            await database.From<T_PLAN_LIST>().Where(p => p.PLAN_ID == id).Delete();
            //}
        }
        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.UpdateOrder 方法
         */
        public async Task UpdateOrder(int id, int state)
        {
            await database.From<T_PLAN_MAIN>()
                .Where(p => p.PLAN_ID == id)
                .Update(new
                {
                    PLAN_STATUS = state,
                    UPDATE_TIME = DateTime.Now
                });
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.ArchiveOrder 方法
         */
        public async Task ArchiveOrder(Domain.Order.Models.Order order, int state)
        {

            var timestamp = DateTime.Now;
            var planMain = mapper.Map<T_PLAN_HIS_MAIN>(order);
            planMain.PLAN_STATUS = state;
            planMain.PLAN_FINAL_TIME = timestamp;
            planMain.CREATE_TIME = timestamp;
            planMain.UPDATE_TIME = timestamp;
            await database.From<T_PLAN_HIS_MAIN>().Add(planMain);
            foreach (var item in order.Lines)
            {
                var planList = mapper.Map<T_PLAN_HIS_LIST>(item);
                planList.PLAN_ID = planMain.PLAN_ID;
                planList.CREATE_TIME = timestamp;
                planList.UPDATE_TIME = timestamp;
                await database.From<T_PLAN_HIS_LIST>().Add(planList);
            }
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetOrder 方法
         */
        public async Task<Domain.Order.Models.Order> GetOrder(int id)
        {
            var order = mapper.Map<Domain.Order.Models.Order>(await database.From<T_PLAN_MAIN>()
                .Where(p => p.PLAN_ID == id)
                .Single());
            if (order != null)
            {
                order.Lines = await GetLines(id);
            }
            return order;
        }

        /*
          * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetOrder 方法
          */
        public async Task<Domain.Order.Models.Order> GetOrderByListId(int id)
        {
            var order = mapper.Map<Domain.Order.Models.Order>(await database.From<T_PLAN_MAIN>()
                .Where(p => p.PLAN_ID == id)
                .Single());
            if (order != null)
            {
                order.Lines = await GetLines(id);
            }
            return order;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetOrder 方法
         */
        public async Task<Domain.Order.Models.Order> GetOrder(OrderLine line)
        {
            var query = database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_LIST_ID == line.Id)
                .Query(p => p.PLAN_ID);
            var order = mapper.Map<Domain.Order.Models.Order>(await database.From<T_PLAN_MAIN>()
                .Where(p => query.Contains(p.PLAN_ID))
                .Single());
            if (order != null)
            {
                order.Lines = await GetLines(order.Id);
            }
            return order;
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetLines 方法
         */
        public async Task<IEnumerable<OrderLine>> GetLines(int id)
        {
            return mapper.Map<IEnumerable<OrderLine>>(await database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_ID == id)
                .Select());
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.GetLine 方法
         */
        public async Task<OrderLine> GetLine(int id)
        {
            return mapper.Map<OrderLine>(await database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_LIST_ID == id)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Order.Repositories.IOrderRepository.UpdateQuantity 方法
         */
        public async Task UpdateQuantity(int line, decimal executing, decimal finished)
        {
            T_PLAN_LIST planList = await database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_LIST_ID == line).Single();
            if (planList == null)
                return;

            planList.ORDERED_QUANTITY = executing;
            planList.FINISHED_QUANTITY = finished;
            planList.UPDATE_TIME = DateTime.Now;
            await database.From<T_PLAN_LIST>().Update(planList);
        }

        public async Task UpdateConfirm(int line, string userName, string Tag)
        {
            try
            {
                var planList = await GetLine(line);
                var repoO = planList.RepoConfirm;
                var prodO = planList.ProdConfirm;
                await database.From<T_PLAN_LIST>()
                .Where(p => p.PLAN_LIST_ID == line)
                .Update(new
                {
                    PLAN_LIST_REPOCONFIRM = Tag.Equals("repo", StringComparison.OrdinalIgnoreCase) ? userName : repoO,
                    PLAN_LIST_PRODCONFIRM = Tag.Equals("prod", StringComparison.OrdinalIgnoreCase) ? userName : prodO,
                    UPDATE_TIME = DateTime.Now
                });
            }
            catch (Exception)
            {

                throw;
            }
        }
    }
}
