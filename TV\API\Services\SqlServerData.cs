﻿using MySqlX.XDevAPI.Common;
using MySqlX.XDevAPI.Relational;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;


namespace API.Services
{
    public class SqlServerData : IData
    {
        private readonly string _conn;

        public SqlServerData(string conn)
        {
            _conn = conn;
        }

        private SqlSugarClient GetDb()
        {
            return new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = _conn,
                DbType = SqlSugar.DbType.SqlServer,
                InitKeyType = InitKeyType.Attribute,
                IsAutoCloseConnection = true, // 自动释放连接
            });
        }



        public dynamic GetInfo(string ip, string id)
        {
            dynamic result = null;
            using (var db = GetDb())
            {
                try
                {

                    //入库任务数
                    var sqlIn = $"SELECT   *    FROM   dbo.T_TV WITH (NOLOCK)  WHERE  Tv_id = '{id}' ";

                    var dbDataIn = ExecuteWithRetry(() => db.Ado.GetDataTable(sqlIn), $"{ip}");
                    foreach (DataRow item in dbDataIn.Rows)
                    {
                        result = new
                        {
                            Title = item["Title"].ToString(),
                            TruckCode = item["TruckCode"].ToString(),
                            Platform = item["Platform"].ToString(),
                            Category = item["Category"].ToString(),
                            TaskNo = item["TaskNo"].ToString(),
                            Remark = item["Remark"].ToString(),
                            Quantity = item["Quantity"].ToString(),
                            PickQuantity = item["PickQuantity"].ToString(),
                        };
                    }


                }
                catch (Exception ex)
                {
                    WriteLog($"{ip}", ex);
                    throw;
                }
            }
            return result;
        }


        private T ExecuteWithRetry<T>(Func<T> action, string tag)
        {
            int retryCount = 3;
            for (int i = 0; i < retryCount; i++)
            {
                try
                {
                    return action();
                }
                catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Number == 1205) // 死锁
                {
                    WriteLog(tag, ex);

                    if (i == retryCount - 1)
                        throw; // 已经重试3次，还是死锁，就抛出

                    System.Threading.Thread.Sleep(400 * (i + 1)); // 逐次增加等待时间：400ms, 800ms, 1600ms
                }
            }
            return default!;
        }

        private void WriteLog(string methodName, Exception ex)
        {
            var logDir = @"D:\WMS\HHQK_TV\log";
            if (!System.IO.Directory.Exists(logDir))
                System.IO.Directory.CreateDirectory(logDir);

            // 当前日志文件
            var logPath = System.IO.Path.Combine(logDir, $"HomeApiError_{DateTime.Now:yyyy-MM-dd}.log");
            var logText = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {methodName} 执行失败: {ex}\r\n";
            System.IO.File.AppendAllText(logPath, logText);

            // 清理 10 天前的日志
            try
            {
                var files = System.IO.Directory.GetFiles(logDir, "HomeApiError_*.log");
                foreach (var file in files)
                {
                    var fileName = System.IO.Path.GetFileNameWithoutExtension(file); // HomeApiError_2025-08-20
                    var datePart = fileName.Replace("HomeApiError_", "");
                    if (DateTime.TryParse(datePart, out var fileDate))
                    {
                        if (fileDate < DateTime.Now.AddDays(-10))
                        {
                            System.IO.File.Delete(file);
                        }
                    }
                }
            }
            catch (Exception cleanupEx)
            {
                // 如果清理失败，只记录，不影响主流程
                var cleanupPath = System.IO.Path.Combine(logDir, $"LogCleanupError_{DateTime.Now:yyyy-MM-dd}.log");
                System.IO.File.AppendAllText(cleanupPath,
                    $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 清理日志文件失败: {cleanupEx}\r\n");
            }
        }
    }
}

