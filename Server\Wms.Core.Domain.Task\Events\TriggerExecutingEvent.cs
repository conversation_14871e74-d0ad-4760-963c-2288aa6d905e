﻿using Newtonsoft.Json.Linq;
using System;

namespace Kean.Domain.Task.Events
{
    /// <summary>
    /// 设备触发命令执行时触发的事件
    /// </summary>
    public class TriggerExecutingEvent : IEvent
    {
        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public int Warehouse { get; set; }

        /// <summary>
        /// 设备
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public JObject Parameter { get; set; }
        public string Parameter01 { get; set; }
        public string Parameter02 { get; set; }
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 降级操作
        /// </summary>
        public Action<string> Fallback { get; set; }
    }
}
