{"format": 1, "restore": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Presentation.Rest\\Wms.Core.Presentation.Rest.csproj": {}}, "projects": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj", "projectName": "Domain_KSF", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj", "projectName": "Kean.Application.Command", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\Wms.Core.Application.Query.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\Wms.Core.Application.Query.csproj", "projectName": "Kean.Application.Query", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\Wms.Core.Application.Query.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\Wms.Core.Application.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\Wms.Core.Application.Utilities.csproj", "projectName": "Kean.Application.Utilities", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\Wms.Core.Application.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Pluralize.NET": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj", "projectName": "Kean.Domain.App", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj", "projectName": "Kean.Domain.Basic", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj", "projectName": "Kean.Domain.Device", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj", "projectName": "Kean.Domain.Identity", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj", "projectName": "Kean.Domain.Interface", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj", "projectName": "Kean.Domain.Material", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj", "projectName": "Kean.Domain.Message", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj", "projectName": "Kean.Domain.Order", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj", "projectName": "Kean.Domain.Seedwork", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj", "projectName": "Kean.Domain.Shared", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj", "projectName": "Kean.Domain.Stock", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj", "projectName": "Kean.Domain.Task", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\Wms.Core.Domain.VisualIntegration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\Wms.Core.Domain.VisualIntegration.csproj", "projectName": "Wms.Core.Domain.VisualIntegration", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\Wms.Core.Domain.VisualIntegration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj", "projectName": "Kean.Infrastructure.Configuration", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\Wms.Core.Infrastructure.Connection.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\Wms.Core.Infrastructure.Connection.csproj", "projectName": "Kean.Infrastructure.Connection", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\Wms.Core.Infrastructure.Connection.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj", "projectName": "Kean.Infrastructure.Database", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "FluentMigrator.Runner": {"target": "Package", "version": "[6.2.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "MySql.Data": {"target": "Package", "version": "[9.1.0, )"}, "Npgsql": {"target": "Package", "version": "[9.0.1, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\Wms.Core.Infrastructure.Hangfire.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\Wms.Core.Infrastructure.Hangfire.csproj", "projectName": "Kean.Infrastructure.Hangfire", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\Wms.Core.Infrastructure.Hangfire.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.15, )"}, "Hangfire.Redis.StackExchange": {"target": "Package", "version": "[1.9.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj", "projectName": "Kean.Infrastructure.Interface", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj", "projectName": "Kean.Infrastructure.Network", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MQTTnet": {"target": "Package", "version": "[4.3.7.1207, )"}, "OPCFoundation.NetStandard.Opc.Ua": {"target": "Package", "version": "[1.5.374.126, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj", "projectName": "Kean.Infrastructure.NoSql", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\Wms.Core.Infrastructure.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\Wms.Core.Infrastructure.Repository.csproj", "projectName": "Kean.Infrastructure.Repository", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\Wms.Core.Infrastructure.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Device\\Wms.Core.Domain.Device.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\Wms.Core.Infrastructure.SignalR.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\Wms.Core.Infrastructure.SignalR.csproj", "projectName": "Kean.Infrastructure.SignalR", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\Wms.Core.Infrastructure.SignalR.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj", "projectName": "Kean.Infrastructure.Soap", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SoapCore": {"target": "Package", "version": "[1.1.0.51, )"}, "System.Security.Cryptography.Xml": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj", "projectName": "Kean.Infrastructure.Utilities", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Presentation.Rest\\Wms.Core.Presentation.Rest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Presentation.Rest\\Wms.Core.Presentation.Rest.csproj", "projectName": "<PERSON>an.Presentation.Rest", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Presentation.Rest\\Wms.Core.Presentation.Rest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Presentation.Rest\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Command\\Wms.Core.Application.Command.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\Wms.Core.Application.Query.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Query\\Wms.Core.Application.Query.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\Wms.Core.Application.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Application.Utilities\\Wms.Core.Application.Utilities.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\Wms.Core.Domain.VisualIntegration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.VisualIntegration\\Wms.Core.Domain.VisualIntegration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\Wms.Core.Infrastructure.Connection.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Connection\\Wms.Core.Infrastructure.Connection.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\Wms.Core.Infrastructure.Hangfire.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Hangfire\\Wms.Core.Infrastructure.Hangfire.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\Wms.Core.Infrastructure.Repository.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Repository\\Wms.Core.Infrastructure.Repository.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\Wms.Core.Infrastructure.SignalR.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.SignalR\\Wms.Core.Infrastructure.SignalR.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[7.5.1, )"}, "Elastic.Serilog.Sinks": {"target": "Package", "version": "[8.12.2, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.11, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "Serilog.Expressions": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[2.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}