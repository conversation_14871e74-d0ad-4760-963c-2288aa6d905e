﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{

    public class HEADER
    {
        [DataMember] public string BSART { get; set; }  //收发类型	

        [DataMember] public string EBELN { get; set; }  //单据号码	

        [DataMember] public string LGPLA { get; set; }  //仓库代码	

        [DataMember] public string VTXTK { get; set; }  //货主代码	

        [DataMember] public string LIFNR { get; set; }  //供应商/送达方	

        [DataMember] public string NAME1 { get; set; }  //供应商/送达方名称	

        [DataMember] public string KUNAG { get; set; }  //售达方	

        [DataMember] public string KUNNR { get; set; }  //承运商	

        [DataMember] public string NAME2 { get; set; }  //承运商名称	

        [DataMember] public string HTEXT { get; set; }  //抬头文本	

        [DataMember] public string BUDAT { get; set; }  //抬头发货日期 （ETD）	

    }
}
