﻿using Kean.Domain.Shared;
using Kean.Domain.Task.Models;
using Kean.Domain.Task.Repositories;
using Kean.Infrastructure.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Domain.Task.Strategies
{
    /// <summary>
    /// 双深上架策略
    /// </summary>
    public sealed class InfeedStrategy_Dual(
        IServiceProvider serviceProvider,           // 服务供应商
        ITaskRepository taskRepository,             // 任务仓库
        IWarehouseRepository warehouseRepository,   // 库房仓库
        WcsMode wcsMode                             // WCS 模式
    ) : InfeedStrategy
    {
        /*
         * 实现 Kean.Domain.Task.Strategies.InfeedStrategy.AutoDestination 方法
         */
        public override async Task<Cell> AutoDestination(string pallet, Station original, int? spec)
        {
            var cells = (await warehouseRepository.GetFreeCells(original.Warehouse, original.Laneway, spec, pallet))
                .ToDictionary(c => c.Id, c => c);
            // 确保足够避让位
            var count = cells.Values.GroupBy(c => (c.Lane<PERSON>, c.Spec))
                .ToDictionary(l => l.Key, l => l.Count(c => c.Deep == 0 && c.Spec == l.Key.Spec && original.Edge?.Reachable(c.Edge) != false) - 1);
            if (!count.Values.Any(c => c > 0))
            {
                return null;
            }
            // 巷道路径状况
            var route = new Dictionary<string, int>();
            foreach (var item in original.Laneway)
            {
                if (await (wcsMode == WcsMode.Classic ?
                    serviceProvider.GetRequiredService<IInterfaceService>().SyncOutput("WCS", "Route", null, $"{{\"Original\":\"{original.Device}\",\"Destination\":\"{item}\"}}", new { Original = original.Device, Destination = item }).ContinueWith<int?>(t => t.Result is bool b ? Convert.ToInt32(!b) : null) :
                    serviceProvider.GetRequiredService<IDeviceService>().RouteWeight(original.Device, item)) is int i)
                {
                    route.Add(item, i);
                }
            }
            // 巷道任务负载
            var task = (await taskRepository.GetTasks(original.Warehouse))
                .SelectMany(t => new KeyValuePair<int, int>[] { new(t.Original, t.Id), new(t.Destination, t.Id) })
                .DistinctBy(c => c.Key)
                .ToDictionary();
            var load = original.Laneway.ToDictionary(l => l, _ => 0);
            if (load.Count > 1)
            {
                foreach (var item in task
                    .Join(await warehouseRepository.GetRunningCells(original.Warehouse, original.Laneway), t => t.Key, c => c.Id, (t, c) => new KeyValuePair<int, string>(t.Value, c.Laneway))
                    .Distinct())
                {
                    load[item.Value]++;
                }
            }
            // 选择最优货位
            foreach (var item in cells.Values
                .Where(c => route.ContainsKey(c.Laneway) && original.Edge?.Reachable(c.Edge) != false)
                .OrderBy(c => c.Inner.HasValue && task.ContainsKey(c.Inner.Value) || c.Outer.HasValue && task.ContainsKey(c.Outer.Value) && !cells.ContainsKey(c.Outer.Value))
                .ThenBy(c => route[c.Laneway])
                .ThenBy(c => load[c.Laneway])
                .ThenBy(c => c.Spec)
                .ThenBy(c => -c.Deep)
                .ThenBy(c => original.Column.HasValue ? Math.Abs(original.Column.Value - c.Column) : 0))
            {
                if (count[(item.Laneway, item.Spec)] > 0)
                {
                    if (await warehouseRepository.MonopolizeCell(item.Id, item.Version))
                    {
                        return item;
                    }
                    count[(item.Laneway, item.Spec)]--;
                }
            }
            return null;
        }
        public override async Task<Cell> AutoDestination(string pallet, string remark, int planListId, Station original, int? spec)
        {
            var cells = (await warehouseRepository.GetFreeCells(original.Warehouse, original.Laneway, spec, pallet))
                .ToDictionary(c => c.Id, c => c);
            // 确保足够避让位
            var count = cells.Values.GroupBy(c => (c.Laneway, c.Spec))
                .ToDictionary(l => l.Key, l => l.Count(c => c.Deep == 0 && c.Spec == l.Key.Spec && original.Edge?.Reachable(c.Edge) != false) - 1);
            if (!count.Values.Any(c => c > 0))
            {
                return null;
            }
            // 巷道路径状况
            var route = new Dictionary<string, int>();
            foreach (var item in original.Laneway)
            {
                if (await (wcsMode == WcsMode.Classic ?
                    serviceProvider.GetRequiredService<IInterfaceService>().SyncOutput("WCS", "Route", null, $"{{\"Original\":\"{original.Device}\",\"Destination\":\"{item}\"}}", new { Original = original.Device, Destination = item }).ContinueWith<int?>(t => t.Result is bool b ? Convert.ToInt32(!b) : null) :
                    serviceProvider.GetRequiredService<IDeviceService>().RouteWeight(original.Device, item)) is int i)
                {
                    route.Add(item, i);
                }
            }
            // 巷道任务负载
            var task = (await taskRepository.GetTasks(original.Warehouse))
                .SelectMany(t => new KeyValuePair<int, int>[] { new(t.Original, t.Id), new(t.Destination, t.Id) })
                .DistinctBy(c => c.Key)
                .ToDictionary();
            var load = original.Laneway.ToDictionary(l => l, _ => 0);
            if (load.Count > 1)
            {
                foreach (var item in task
                    .Join(await warehouseRepository.GetRunningCells(original.Warehouse, original.Laneway), t => t.Key, c => c.Id, (t, c) => new KeyValuePair<int, string>(t.Value, c.Laneway))
                    .Distinct())
                {
                    load[item.Value]++;
                }
            }
            // 选择最优货位
            foreach (var item in cells.Values
                .Where(c => route.ContainsKey(c.Laneway) && original.Edge?.Reachable(c.Edge) != false)
                .OrderBy(c => c.Inner.HasValue && task.ContainsKey(c.Inner.Value) || c.Outer.HasValue && task.ContainsKey(c.Outer.Value) && !cells.ContainsKey(c.Outer.Value))
                .ThenBy(c => route[c.Laneway])
                .ThenBy(c => load[c.Laneway])
                .ThenBy(c => c.Spec)
                .ThenBy(c => -c.Deep)
                .ThenBy(c => original.Column.HasValue ? Math.Abs(original.Column.Value - c.Column) : 0))
            {
                if (count[(item.Laneway, item.Spec)] > 0)
                {
                    if (await warehouseRepository.MonopolizeCell(item.Id, item.Version))
                    {
                        return item;
                    }
                    count[(item.Laneway, item.Spec)]--;
                }
            }
            return null;
        }
        public override async Task<Cell> AutoDestinationWithLaneway(string pallet, Station original, IEnumerable<string> sLaneway, int? spec)
        {
            
            // 选择最优货位
            foreach (var item in (await warehouseRepository.GetFreeCells(original.Warehouse, sLaneway, spec, pallet))
                .OrderBy(c => c.Column).ThenBy(c => c.Layer).ThenBy(c => c.Spec))
            //.ThenBy(c => original.Column.HasValue ? Math.Abs(original.Column.Value - c.Column) : 0))
            {
                if (await warehouseRepository.MonopolizeCell(item.Id, item.Version))
                {
                    return item;
                }
            }
            return null;
        }
    }
}
