﻿using FluentValidation;
using Kean.Domain.Order.Models;
using System;
using System.Collections.Generic;

namespace Kean.Domain.Order.Commands
{
    /// <summary>
    /// 创建命令
    /// </summary>
    public class CreateCommand : CommandValidator<CreateCommand>, ICommand
    {
        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Creater { get; set; }

        /// <summary>
        /// 制单时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 订单行
        /// </summary>
        public IEnumerable<OrderLine> Lines { get; set; }

        public string PlanCode40 { get; set; }


        /// <summary>
        /// 验证项
        /// </summary>
        protected override void Validation()
        {
            RuleFor(r => r.Type).NotEmpty().WithMessage(ErrorEnum.类型不允许空);
            RuleFor(r => r.Lines).NotEmpty().WithMessage(ErrorEnum.订单行不允许空);
        }

        /// <summary>
        /// 标识
        /// </summary>
        [Output]
        public int Id { get; private set; }
    }
}
