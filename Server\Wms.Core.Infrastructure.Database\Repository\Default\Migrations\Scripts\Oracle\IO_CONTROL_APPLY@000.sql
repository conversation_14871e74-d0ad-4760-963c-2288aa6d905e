﻿CREATE SEQUENCE IO_CONTROL_APPLY_SEQ START WITH 1 INCREMENT BY 1 NOMAXVALUE;
CREATE TABLE IO_CONTROL_APPLY (
	CONTROL_APPLY_ID integer PRIMARY KEY,
	CONTROL_ID integer,
	CONTROL_APPLY_TYPE varchar2(50),
	WAREHOUSE_CODE varchar2(50),
	DEVICE_CODE varchar2(50),
	STOCK_BARCODE varchar2(50),
	APPLY_TASK_STATUS integer,
	CREATE_TIME varchar2(20),
	CONTROL_APPLY_PARAMETER nvarchar2(500),
	CONTROL_APPLY_PARA01 varchar2(50),
	CONTROL_APPLY_PARA02 nvarchar2(500),
	CONTROL_APPLY_REMARK nvarchar2(500)
);