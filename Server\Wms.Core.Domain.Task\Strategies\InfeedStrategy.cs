﻿using Kean.Domain.Task.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Kean.Domain.Task.Strategies
{
    /// <summary>
    /// 上架策略
    /// </summary>
    public abstract class InfeedStrategy
    {
        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public abstract Task<Cell> AutoDestination(string pallet, Station original, int? spec);

        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public abstract Task<Cell> AutoDestinationWithLaneway(string pallet, Station original, IEnumerable<string> sLaneway, int? spec);

        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public abstract Task<Cell> AutoDestination(string pallet, string remark, int planlistid, Station original, int? spec);
    }
}
