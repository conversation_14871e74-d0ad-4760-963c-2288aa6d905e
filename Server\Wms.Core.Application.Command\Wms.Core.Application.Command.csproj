﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
		<AssemblyName>Kean.Application.Command</AssemblyName>
		<RootNamespace>Kean.Application.Command</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.9" />
		<PackageReference Include="ZXing.Net" Version="0.16.9" />
		<PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Domain_KSF\Domain_KSF.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.App\Wms.Core.Domain.App.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Basic\Wms.Core.Domain.Basic.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Device\Wms.Core.Domain.Device.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Identity\Wms.Core.Domain.Identity.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Interface\Wms.Core.Domain.Interface.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Material\Wms.Core.Domain.Material.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Message\Wms.Core.Domain.Message.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Order\Wms.Core.Domain.Order.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Stock\Wms.Core.Domain.Stock.csproj" />
		<ProjectReference Include="..\Wms.Core.Domain.Task\Wms.Core.Domain.Task.csproj" />
		<ProjectReference Include="..\Wms.Core.Infrastructure.Utilities\Wms.Core.Infrastructure.Utilities.csproj" />
	</ItemGroup>

</Project>
