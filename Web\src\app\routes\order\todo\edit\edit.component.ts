import { Component, Inject, ViewChild } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { DialogService } from "@app/core/services/dialog.service";
import { HttpService } from "@app/core/services/http.service";
import { I18nService } from "@app/core/services/i18n.service";
import { ToastService } from "@app/core/services/toast.service";
import { TodoEditLineComponent } from "./line/line.component";
import * as moment from "moment";

@Component({
  selector: "app-order-todo-edit",
  templateUrl: "./edit.component.html",
  styleUrls: ["./edit.component.scss"],
  queries: {
    _sort: new ViewChild(MatSort),
  },
})
export class TodoEditComponent {
  private _sort: MatSort;
  private _form: FormGroup;
  private _columns: any[];
  private _lines: MatTableDataSource<any>;
  private _types: any[];

  constructor(
    @Inject(MAT_DIALOG_DATA)
    private _data: any,
    private _formBuilder: FormBuilder,
    private _dialogService: DialogService,
    private _httpService: HttpService,
    private _i18nService: I18nService,
    private _toastService: ToastService,
    private _dialogRef: MatDialogRef<TodoEditComponent>
  ) {
    this._form = this._formBuilder.group({
      number: [{ value: null, disabled: _data.id }, [Validators.required]],
      type: [{ value: null, disabled: _data.type }, [Validators.required]],
      creater: [{ value: null, disabled: _data.id }],
      createTime: [{ value: null, disabled: _data.id }],
      remark: [{ value: null, disabled: _data.id }],
    });
    this._columns = [
      { id: "code", header: "routes.material.code" },
      { id: "name", header: "routes.material.name" },
      { id: "model", header: "routes.material.model" },
      { id: "quantity", header: "routes.material.qty" },
      { id: "unit", header: "routes.material.unit" },
      { id: "batch", header: "routes.material.batch" },
     /*
      { id: "bill", header: "routes.material.bill" },
      { id: "supplier", header: "routes.material.supplier" },
      { id: "brand", header: "routes.material.brand" },*/
      /*
      {
        id: "adjustQuantity",
        header: "routes.material.adjustQuantity",
      },
      {
        id: "adjustReason",
        header: "routes.material.adjustReason",
      },
      {
        id: "clientCode",
        header: "routes.material.clientCode",
      },
      {
        id: "effectivePeriod",
        header: "routes.material.effectivePeriod",
      },
      {
        id: "expirationPeriod",
        header: "routes.material.expirationPeriod",
      },
      {
        id: "financialPostLatestTime",
        header: "routes.material.financialPostLatestTime",
      },
      {
        id: "financialPostQuantity",
        header: "routes.material.financialPostQuantity",
      },
      {
        id: "financialPostStatus",
        header: "routes.material.financialPostStatus",
      },
      {
        id: "financialPostTime",
        header: "routes.material.financialPostTime",
      },
      {
        id: "financialPostUploadTime",
        header: "routes.material.financialPostUploadTime",
      },
      {
        id: "importTime",
        header: "routes.material.importTime",
      },*/
      {
        id: "productionLine",
        header: "routes.material.productionLine",
      },
      
      {
        id: "sapNo",
        header: "routes.material.sapNo",
      },
      /*
      {
        id: "transferType",
        header: "routes.material.transferType",
      },*/
      {
        id: "truckInfo",
        header: "routes.material.truckInfo",
      },
      {
        id: "warehouseCode",
        header: "routes.material.warehouseCode",
      },
      {
        id: "workGroup",
        header: "routes.material.workGroup",
      },
      { id: "operation" },
    ];
    this._lines = new MatTableDataSource<any>();
    if (_data.id) {
      this._form.controls.number.setValue(_data.number);
      this._form.controls.type.setValue(_data.type);
      this._form.controls.creater.setValue(_data.creater);
      this._form.controls.createTime.setValue(_data.createTime);
      this._form.controls.remark.setValue(_data.remark);
      this._httpService
        .get(`orders/${_data.id}/lines`)
        .then((res: any) => (this._lines.data = res.items));
    } else {
      this._form.controls.createTime.setValue(moment());
      this._lines.data = [];
      this._httpService
        .get("users/current")
        .then((res: any) => this._form.controls.creater.setValue(res.name));
    }
    this._httpService.get("orders/types").then((res: any) => {
      this._types = res.items;
      if (!_data.id) {
        const type =
          _data.type && this._types.find((item: any) => item.id == _data.type);
        if (type) {
          this._form.controls.type.setValue(type.id);
          this.type({ value: type });
        }
      }
    });
  }

  ngAfterViewInit(): void {
    this._lines.sort = this._sort;
  }

  public get data() {
    return this._data;
  }

  public get form() {
    return this._form;
  }

  public get columns() {
    return this._columns;
  }

  public get lines() {
    return this._lines;
  }

  public get types() {
    return this._types;
  }

  public type = (e: { value: any }) => {
    const type = Number.isInteger(e.value)
      ? this._types.find((item: any) => item.id == e.value)
      : e.value;
    // if (type?.number) {
    //   this._form.controls.number.setValue("AUTOGENERATE");
    //   this._form.controls.number.disable();
    // } else {
    //   this._form.controls.number.setValue(null);
    //   this._form.controls.number.enable();
    // }
    this._form.controls.number.setValue(null);
      this._form.controls.number.enable();
  };

  public edit = async (item?: any) => {
    const res: any = await this._dialogService.open(TodoEditLineComponent, {
      autoFocus: !item,
      data: item,
    });
    if (res) {
      if (item) {
        Object.assign(item, res);
      } else {
        this._lines.data = [...this._lines.data, res];
      }
    }
  };

  public delete = async (item: any) => {
    if (
      await this._dialogService.confirm(
        this._i18nService.translate("shared.notification.confirm")
      )
    ) {
      this._lines.data.splice(this._lines.data.indexOf(item), 1);
      this._lines.data = [...this._lines.data];
    }
  };

  public save = async () => {
    const data: any = {
      number: this.form.controls.number.value,
      type: this.form.controls.type.value,
      creater: this.form.controls.creater.value,
      createTime: this.form.controls.createTime.value,
      remark: this.form.controls.remark.value,
      lines: this._lines.data.map((item: any) =>
        Object.assign({}, item, { id: item.line })
      ),
    };
    this._dialogRef.close(
      this._data.id
        ? await this._httpService
            .put(`orders/${this._data.id}`, data)
            .catch((e: any) => {
              this._toastService.show(
                e.error?.errorMessage
                  ? `${this._i18nService.translate(
                      `server.${e.error.errorMessage}`
                    )} (${e.error.errorMessage})`
                  : this._i18nService.translate("shared.notification.unknown")
              );
            })
        : await this._httpService.post("orders", data).catch((e: any) => {
            this._toastService.show(
              e.error?.errorMessage
                ? `${this._i18nService.translate(
                    `server.${e.error.errorMessage}`
                  )} (${e.error.errorMessage})`
                : this._i18nService.translate("shared.notification.unknown")
            );
          })
    );
  };
}
