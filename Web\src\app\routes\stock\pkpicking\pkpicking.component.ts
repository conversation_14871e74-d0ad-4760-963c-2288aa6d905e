import { AfterViewInit, Component, TemplateRef, ViewChild } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { DialogService } from '@app/core/services/dialog.service';
import { HttpService } from '@app/core/services/http.service';
import { I18nService } from '@app/core/services/i18n.service';
import { ToastService } from '@app/core/services/toast.service';
import { CrudComponent } from '@app/routes/.templates/crud/crud.component';

@Component({
  selector: 'app-stock-pkpicking',
  templateUrl: './pkpicking.component.html',
  styleUrls: ['./pkpicking.component.scss'],
  animations: [
    trigger('focus', [
      state('focus', style({ opacity: '1' })),
      state('blur', style({ opacity: '0' })),
      transition('focus <=> blur', animate('200ms cubic-bezier(.4, 0, .2, 1)')),
    ]),
  ],
  queries: {
    _template: new ViewChild('$crud'),
    _qsColumn: new ViewChild('$qs'),
    _enabledColumn: new ViewChild('$enabled')
  }
})
export class PkPickingComponent implements AfterViewInit {

  private _template: CrudComponent;
  private _qsColumn: TemplateRef<any>;
  private _enabledColumn: TemplateRef<any>;
  private _defination: any;
  private _api: string;
  private _barcode: string;
  private _plancode: string;
  private _focus: boolean;

  constructor(
    private _dialogService: DialogService,
    private _httpService: HttpService,
    private _i18nService: I18nService,
    private _toastService: ToastService
  ) { }

  async ngAfterViewInit(): Promise<void> {
    setTimeout(() => {
      this.barcode='';
      this._template.loading = false;
      this._defination = [{
        id: 'code',
        header: 'routes.material.code'
      }, {
        id: 'name',
        header: 'routes.material.name'
      }, {
        id: 'model',
        header: 'routes.material.model'
      }, {
        id: 'lockedQuantity',
        header: 'routes.material.qty',
      }, {
        id: 'unit',
        header: 'routes.material.unit'
      }, {
        id: 'warehouse',
        header: 'routes.stock.pkpicking.warehouse'
      }, {
        id: 'cell',
        header: 'routes.stock.pkpicking.cell'
      }, {
        id: 'locker',
        header: 'routes.stock.pkpicking.lock'
      }, {
        id: 'batch',
        header: 'routes.material.batch'
      }, {
        id: 'bill',
        header: 'routes.material.bill'
      }, {
        id: 'supplier',
        header: 'routes.material.supplier'
      }, {
        id: 'brand',
        header: 'routes.material.brand'
      }, {
        id: 'qualityState',
        header: 'routes.material.qs',
        template: this._qsColumn
      }, {
        id: 'enabled',
        header: 'routes.material.enabled',
        template: this._enabledColumn
      }];
    });
  }

  public get defination() {
    return this._defination;
  }

  public get api() {
    return this._api;
  }

  public get barcode() {
    return this._barcode;
  }

  public get plancode() {
    return this._plancode;
  }
  public get focus() {
    return this._focus;
  }

  public set barcode(value: string) {
    this._barcode = value;
  }

  public set plancode(value: string) {
    this._plancode = value;
  }

  public set focus(value: boolean) {
    this._focus = value;
  }

  public search = () => {
    
    if (!!this._api) {
      this._template.refresh();
    }
    else {
      this._api = 'stocks/locks';
    }
  }

  public convertor = async (e: any) => {
    let task: any = await this._httpService.get(`tasks`);
    task = task.items.reduce((obj: any, item: any) => { return obj[item.barcode] = item, obj }, {});
    e.items.forEach((item: any) => {
      if (item.enabled == null) {
        if (item.qualityState != null && item.qualityState != 'ok') {
          item.enabled = false;
        }
        else if (item.overdue) {
          item.enabled = false;
        }
        else {
          item.enabled = true;
        }
      }
      if (task[item.barcode]) {
        item.cell = `${task[item.barcode].original} -> ${task[item.barcode].destination}`;
        item.available = false;
      }
      else if (item.position == 'Cell') {
        item.available = false;
      }
      else {
        item.available = true;
      }
    });
    return e;
  }

  public confirm = async (item: any) => {
    if (item.quantity) {
      const quantity = await this._dialogService.prompt(this._i18nService.translate('routes.stock.pkpicking.quantity'), item.lockedQuantity, {
        type: 'number',
        range: [0, item.lockedQuantity],
        required: true
      });
      if (quantity) {
        this._template.loading = true;
        const res = await this._httpService.post('stocks', {
          operation: 'outbound',
          stock: {
            tag:'outboundplan',
            barcode: item.barcode,
            lines: [Object.assign({}, item, { managelistquantity:-quantity })]
          }
        }).catch(e => {
          this._toastService.show(e.error?.errorMessage ?
            `${this._i18nService.translate(`server.${e.error.errorMessage}`)} (${e.error.errorMessage})` :
            this._i18nService.translate('shared.notification.unknown'));
        });
        if (res !== undefined) {
          this._toastService.show(this._i18nService.translate('shared.notification.success'));
          await this._template.refresh();
        }
        this._template.loading = false;
      }
    }
  }

  public cancel = async (item: any) => {
    if (await this._dialogService.confirm(this._i18nService.translate('shared.notification.confirm'))) {
      this._template.loading = true;
      const res = await this._httpService.post('stocks/locks', {
        stock: {
          barcode: item.barcode,
          lines: [Object.assign({}, item, { quantity: -item.lockedQuantity })]
        }
      }).catch(e => {
        this._toastService.show(e.error?.errorMessage ?
          `${this._i18nService.translate(`server.${e.error.errorMessage}`)} (${e.error.errorMessage})` :
          this._i18nService.translate('shared.notification.unknown'));
      });
      if (res !== undefined) {
        this._toastService.show(this._i18nService.translate('shared.notification.success'));
        this._template.refresh();
      }
      this._template.loading = false;
    }
  }
}
