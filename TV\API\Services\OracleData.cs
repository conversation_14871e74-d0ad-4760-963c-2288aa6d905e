﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace API.Services
{
    public class OracleData : IData
    {
        SqlSugarClient db;

        public OracleData(string conn)
        {
            db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = conn,
                DbType = SqlSugar.DbType.Oracle,
                InitKeyType = InitKeyType.Attribute,    //从特性读取主键和自增列信息
                IsAutoCloseConnection = true,           //开启自动释放模式
            });
        }

        /// <summary>
        /// 已实现
        /// </summary>
        /// <returns></returns>
        public dynamic HomeCellAvaliable()
        {
            var sql = @"select LANE_WAY, sum(ENABLE_NUM) ENABLE_COUNT, sum(ALL_NUM) ALL_COUNT
                        from(select LANE_WAY, (case when RUN_STATUS = 'Enable' then 1 else 0 end) ENABLE_NUM ,1 ALL_NUM
                             from WH_CELL where AREA_ID = 2) 
                        group by LANE_WAY 
                        order by LANE_WAY";

            var dbData = db.Ado.GetDataTable(sql);

            if(dbData ==null || dbData.Rows.Count < 1)
            {
                return null;
            }

            dynamic result = new
            {
                title = dbData.AsEnumerable().Select(t => $"{t["LANE_WAY"].ToString()}巷道").ToArray(),
                valdata = dbData.AsEnumerable().Select(t => int.Parse(t["ENABLE_COUNT"].ToString())).ToArray(),
                data = dbData.AsEnumerable().Select(t => int.Parse(t["ENABLE_COUNT"].ToString()) * 100 / int.Parse(t["ALL_COUNT"].ToString())).ToArray()
            };

            return result;
        }

        public List<string> HomeImageDir()
        {
            return null;
        }

        public dynamic HomeManageHis()
        {
            return null;
        }

        public List<dynamic> HomeManageList()
        {
            return null;
        }


        public List<dynamic> HomeManageinList()
        {
            return null;
        }

        public List<dynamic> HomeManageCurrentList()
        {

            return null;
        }

        public List<dynamic> HomeManageinCurrentList()
        {

            return null;
        }


        public string HomeRouteAvaliable()
        {
            return null;
        }

        public List<dynamic> HomeStorageList()
        {
            return null;
        }

        public List<int> HomeSummary()
        {
            return null;
        }

        public List<dynamic> ManageApplyList()
        {
            return null;
        }

        public dynamic ManageLaneway()
        {
            return null;
        }

        public List<dynamic> ManageList()
        {
            return null;
        }

        public List<dynamic> ManageStatus()
        {
            return null;
        }

        public List<dynamic> ManageSummary()
        {
            return null;
        }

        public List<dynamic> ManageType()
        {
            return null;
        }

        public dynamic StorageCellRate()
        {
            return null;
        }

        public List<dynamic> StorageList()
        {
            return null;
        }

        public dynamic StorageStockInfo()
        {
            return null;
        }

        public dynamic StorageStocklaneway()
        {
            return null;
        }
    }
}
