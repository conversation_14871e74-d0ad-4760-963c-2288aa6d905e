2025-08-15 09:03:00.646 +08:00 [INF] 创建StationManager实例
2025-08-15 09:03:00.678 +08:00 [INF] 创建StationMappingService实例，配置文件路径: D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Presentation.Rest\bin\Debug\station-mapping.json
2025-08-15 09:03:00.683 +08:00 [INF] 配置文件是否存在: true
2025-08-15 09:03:00.730 +08:00 [INF] 站位映射配置已加载，版本: 1.0.0, 物理站位数: 12, 逻辑站位数: 4
2025-08-15 09:03:00.737 +08:00 [INF] 检查配置验证设置: EnableValidation=true
2025-08-15 09:03:00.737 +08:00 [INF] 开始执行配置验证
2025-08-15 09:03:00.738 +08:00 [INF] 配置验证完成，结果: IsValid=true, ErrorCount=0
2025-08-15 09:03:00.739 +08:00 [INF] 配置验证通过
2025-08-15 09:03:00.740 +08:00 [INF] 检查热重载配置: Configuration=true, EnableHotReload=true
2025-08-15 09:03:00.740 +08:00 [INF] 热重载启用，检查间隔: 30秒
2025-08-15 09:03:00.742 +08:00 [INF] 开始异步初始化站位状态
2025-08-15 09:03:00.743 +08:00 [INF] 站位状态初始化开始
2025-08-15 09:03:00.838 +08:00 [INF] 已初始化 4 个逻辑站位的棧板到达状态
2025-08-15 09:03:00.842 +08:00 [INF] 已注册消息处理器: FuncID=Stnoldf, MessageType=StnoldfRequest
2025-08-15 09:03:00.916 +08:00 [INF] 服务状态变更: "Stopped" -> "Starting", 原因: 开始启动服务
2025-08-15 09:03:00.919 +08:00 [INF] 首次加载配置
2025-08-15 09:03:00.920 +08:00 [INF] 开始加载视觉集成配置，配置文件路径: appsettings.visualintegration.json
2025-08-15 09:03:00.934 +08:00 [INF] 成功从配置文件加载配置
2025-08-15 09:03:00.959 +08:00 [INF] 配置加载完成
2025-08-15 09:03:00.960 +08:00 [INF] 配置加载完成
2025-08-15 09:03:00.960 +08:00 [INF] 正在连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:03:00.963 +08:00 [INF] 开始连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:03:00.978 +08:00 [INF] 成功连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:03:00.980 +08:00 [INF] 启动网络接收循环
2025-08-15 09:03:00.982 +08:00 [INF] 连接已建立，开始心跳监控
2025-08-15 09:03:00.984 +08:00 [INF] 开始心跳监控，间隔: 5秒
2025-08-15 09:03:00.987 +08:00 [INF] 连接状态变更: true, 服务器: 127.0.0.1:8080
2025-08-15 09:03:00.989 +08:00 [INF] TCP连接建立成功
2025-08-15 09:03:00.990 +08:00 [INF] 发送 ASRSOnline 上线请求: FuncSeqNo=1
2025-08-15 09:03:01.067 +08:00 [INF] 收到 ASRSOnline 响应: FuncSeqNo=1, Result=true
2025-08-15 09:03:01.068 +08:00 [INF] 启动心跳监控
2025-08-15 09:03:01.068 +08:00 [WRN] 心跳监控已经在运行中
2025-08-15 09:03:01.069 +08:00 [INF] 消息处理器注册完成
2025-08-15 09:03:01.069 +08:00 [INF] 消息处理器注册完成
2025-08-15 09:03:01.069 +08:00 [INF] 服务状态变更: "Starting" -> "Running", 原因: 服务启动成功
2025-08-15 09:03:01.071 +08:00 [INF] 视觉集成服务启动成功
2025-08-15 09:03:35.367 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=4
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:46.145 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=7
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:46.144 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=5
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:46.151 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=6
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:50.698 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=8
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:46.156 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 09:03:50.710 +08:00 [ERR] 连续5次心跳失败，连接可能已断开
2025-08-15 09:04:00.440 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:04:00.443 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:03:50.709 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=9
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:03:46.157 +08:00 [ERR] 连续4次心跳失败，连接可能已断开
2025-08-15 09:04:00.455 +08:00 [WRN] 棧板未到达站位: 1111
2025-08-15 09:04:00.464 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: false
2025-08-15 09:04:27.387 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:04:36.768 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:04:35.553 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=11
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:04:36.786 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=12
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 09:04:49.301 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 09:05:25.065 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:05:29.896 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=13
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:05:32.500 +08:00 [ERR] 连续4次心跳失败，连接可能已断开
2025-08-15 09:05:35.861 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:05:35.876 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=15
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:05:35.861 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=14
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:05:37.475 +08:00 [ERR] 连续5次心跳失败，连接可能已断开
2025-08-15 09:05:47.406 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=16
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:05:47.431 +08:00 [ERR] 连续7次心跳失败，连接可能已断开
2025-08-15 09:05:37.477 +08:00 [ERR] 连续6次心跳失败，连接可能已断开
2025-08-15 09:05:53.896 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:18.722 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=25
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:18.730 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:06:23.447 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=26
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:24.374 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=27
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:24.394 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 09:06:27.365 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508158244
2025-08-15 09:06:27.377 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508158244, 条码=6932966774243
2025-08-15 09:06:27.430 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=28
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:27.432 +08:00 [ERR] 连续4次心跳失败，连接可能已断开
2025-08-15 09:06:36.689 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=29
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:37.052 +08:00 [ERR] 连续5次心跳失败，连接可能已断开
2025-08-15 09:06:37.636 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96533562","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96533562","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96533562","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:b29ac2c5-e6ad-4e66-a51c-d3da0e55528b
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:06:37.672 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=30
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:38.477 +08:00 [ERR] 连续6次心跳失败，连接可能已断开
2025-08-15 09:06:39.784 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:06:42.506 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:06:43.224 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=31
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:43.913 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:06:43.914 +08:00 [ERR] 连续7次心跳失败，连接可能已断开
2025-08-15 09:06:47.746 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:06:48.985 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=32
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs)
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state)
2025-08-15 09:06:48.992 +08:00 [ERR] 连续8次心跳失败，连接可能已断开
2025-08-15 09:06:50.531 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:06:52.208 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:53.741 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:06:55.154 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:06:55.568 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:06:55.574 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:06:55.854 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:06:55.860 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508154559
2025-08-15 09:06:55.862 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508154559, 条码=6983704795948
2025-08-15 09:06:55.990 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"65088585","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"65088585","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"65088585","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:b29ac2c5-e6ad-4e66-a51c-d3da0e55528b
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:06:56.026 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:06:56.032 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:56.033 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:06:56.922 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:06:57.139 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:06:57.142 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:06:57.326 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:06:57.339 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:57.339 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:06:58.101 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:06:58.295 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:06:58.298 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:06:58.501 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508153693
2025-08-15 09:06:58.503 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:06:58.504 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508153693, 条码=6956880189017
2025-08-15 09:06:58.630 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"81933884","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"81933884","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"81933884","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:b29ac2c5-e6ad-4e66-a51c-d3da0e55528b
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:06:58.666 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:06:58.671 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:58.672 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:06:59.097 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:06:59.271 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:06:59.275 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:06:59.491 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:06:59.493 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508158606
2025-08-15 09:06:59.497 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508158606, 条码=6944030052655
2025-08-15 09:06:59.633 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"50334231","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"50334231","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"50334231","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:b29ac2c5-e6ad-4e66-a51c-d3da0e55528b
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:06:59.668 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:06:59.674 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:06:59.674 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:07:02.640 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508154926
2025-08-15 09:07:02.640 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508154926, 条码=6914228986168
2025-08-15 09:07:02.831 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"84030227","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"84030227","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"84030227","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:ca2af6ac-f2b5-474c-b16c-9204d6b0766d
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:07:02.869 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:07:03.576 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508156575
2025-08-15 09:07:03.577 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508156575, 条码=6911455622586
2025-08-15 09:07:03.700 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96189216","Conten”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96189216","Conten”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CONTROL_APPLY_PARA02”中被截断。截断值:“{"FuncID":"Stnoldf","FuncSeqNo":"96189216","Conten”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:ca2af6ac-f2b5-474c-b16c-9204d6b0766d
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo)
2025-08-15 09:07:03.735 +08:00 [ERR] 发送识别结果确认消息失败: 消息验证失败 (Parameter 'request')
System.ArgumentException: 消息验证失败 (Parameter 'request')
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageWithoutResponseAsync[TRequest](TRequest request)
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.SendStnoldfReplyAsync(String stationNo, String seqNo, Boolean success, String message)
2025-08-15 09:10:18.075 +08:00 [INF] 服务状态变更: "Running" -> "Stopping", 原因: 开始停止服务
2025-08-15 09:10:18.077 +08:00 [INF] 正在停止视觉集成服务
2025-08-15 09:10:18.078 +08:00 [INF] 停止心跳监控
2025-08-15 09:10:18.078 +08:00 [INF] 心跳监控已停止
2025-08-15 09:10:18.080 +08:00 [INF] 断开与视觉识别系统的连接: 127.0.0.1:8080
2025-08-15 09:10:18.081 +08:00 [INF] 已停止网络接收循环
2025-08-15 09:10:18.082 +08:00 [INF] 连接已断开，停止心跳监控
2025-08-15 09:10:18.083 +08:00 [WRN] 心跳监控未在运行
2025-08-15 09:10:18.083 +08:00 [INF] 连接状态变更: false, 服务器: 127.0.0.1:8080
2025-08-15 09:10:18.098 +08:00 [ERR] 接收循环异常
System.IO.IOException: Unable to read data from the transport connection: 由于线程退出或应用程序请求，已中止 I/O 操作。.
 ---> System.Net.Sockets.SocketException (995): 由于线程退出或应用程序请求，已中止 I/O 操作。
   --- End of inner exception stack trace ---
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Threading.Tasks.ValueTask`1.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct)
2025-08-15 09:10:18.098 +08:00 [INF] TCP连接已断开
2025-08-15 09:10:18.102 +08:00 [INF] 服务状态变更: "Stopping" -> "Stopped", 原因: 服务停止成功
2025-08-15 09:10:18.115 +08:00 [INF] 视觉集成服务停止成功
2025-08-15 09:10:18.124 +08:00 [INF] 服务已停止或正在停止，当前状态: "Stopped"
2025-08-15 09:10:18.125 +08:00 [WRN] 心跳监控未在运行
2025-08-15 09:44:10.348 +08:00 [INF] 创建StationManager实例
2025-08-15 09:44:10.374 +08:00 [INF] 创建StationMappingService实例，配置文件路径: D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Presentation.Rest\bin\Debug\station-mapping.json
2025-08-15 09:44:10.376 +08:00 [INF] 配置文件是否存在: true
2025-08-15 09:44:10.414 +08:00 [INF] 站位映射配置已加载，版本: 1.0.0, 物理站位数: 12, 逻辑站位数: 4
2025-08-15 09:44:10.416 +08:00 [INF] 检查配置验证设置: EnableValidation=true
2025-08-15 09:44:10.416 +08:00 [INF] 开始执行配置验证
2025-08-15 09:44:10.417 +08:00 [INF] 配置验证完成，结果: IsValid=true, ErrorCount=0
2025-08-15 09:44:10.418 +08:00 [INF] 配置验证通过
2025-08-15 09:44:10.418 +08:00 [INF] 检查热重载配置: Configuration=true, EnableHotReload=true
2025-08-15 09:44:10.419 +08:00 [INF] 热重载启用，检查间隔: 30秒
2025-08-15 09:44:10.419 +08:00 [INF] 开始异步初始化站位状态
2025-08-15 09:44:10.420 +08:00 [INF] 站位状态初始化开始
2025-08-15 09:44:10.424 +08:00 [INF] 已初始化 4 个逻辑站位的棧板到达状态
2025-08-15 09:44:10.425 +08:00 [INF] 已注册消息处理器: FuncID=Stnoldf, MessageType=StnoldfRequest
2025-08-15 09:44:10.488 +08:00 [INF] 服务状态变更: "Stopped" -> "Starting", 原因: 开始启动服务
2025-08-15 09:44:10.490 +08:00 [INF] 首次加载配置
2025-08-15 09:44:10.491 +08:00 [INF] 开始加载视觉集成配置，配置文件路径: appsettings.visualintegration.json
2025-08-15 09:44:10.502 +08:00 [INF] 成功从配置文件加载配置
2025-08-15 09:44:10.507 +08:00 [INF] 配置加载完成
2025-08-15 09:44:10.508 +08:00 [INF] 配置加载完成
2025-08-15 09:44:10.508 +08:00 [INF] 正在连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:44:10.510 +08:00 [INF] 开始连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:44:10.513 +08:00 [INF] 成功连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 09:44:10.516 +08:00 [INF] 启动网络接收循环
2025-08-15 09:44:10.517 +08:00 [INF] 连接已建立，开始心跳监控
2025-08-15 09:44:10.517 +08:00 [INF] 开始心跳监控，间隔: 5秒
2025-08-15 09:44:10.520 +08:00 [INF] 连接状态变更: true, 服务器: 127.0.0.1:8080
2025-08-15 09:44:10.522 +08:00 [INF] TCP连接建立成功
2025-08-15 09:44:10.522 +08:00 [INF] 发送 ASRSOnline 上线请求: FuncSeqNo=1
2025-08-15 09:44:10.549 +08:00 [INF] 收到 ASRSOnline 响应: FuncSeqNo=1, Result=true
2025-08-15 09:44:10.550 +08:00 [INF] 启动心跳监控
2025-08-15 09:44:10.550 +08:00 [WRN] 心跳监控已经在运行中
2025-08-15 09:44:10.551 +08:00 [INF] 消息处理器注册完成
2025-08-15 09:44:10.551 +08:00 [INF] 消息处理器注册完成
2025-08-15 09:44:10.551 +08:00 [INF] 服务状态变更: "Starting" -> "Running", 原因: 服务启动成功
2025-08-15 09:44:10.553 +08:00 [INF] 视觉集成服务启动成功
2025-08-15 09:51:25.774 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 09:51:27.491 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 09:51:27.496 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 09:51:28.896 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 09:51:28.912 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 09:51:28.913 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 09:51:31.575 +08:00 [ERR] 消息处理器执行异常: FuncID=Stnoldf
System.Text.Json.JsonException: The JSON value could not be converted to Wms.Core.Domain.VisualIntegration.Models.Messages.RecognitionResult.StnoldfRequest. Path: $ | LineNumber: 0 | BytePositionInLine: 8.
   at System.Text.Json.ThrowHelper.ThrowJsonException_DeserializeUnableToConvertValue(Type propertyType)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at Wms.Core.Domain.VisualIntegration.Services.DataTransformer.DeserializeMessage[T](String json) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\DataTransformer.cs:line 69
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.<>c__DisplayClass23_0`1.<<RegisterMessageHandler>b__0>d.MoveNext()
2025-08-15 10:03:17.388 +08:00 [INF] 创建StationManager实例
2025-08-15 10:03:17.410 +08:00 [INF] 创建StationMappingService实例，配置文件路径: D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Presentation.Rest\bin\Debug\station-mapping.json
2025-08-15 10:03:17.412 +08:00 [INF] 配置文件是否存在: true
2025-08-15 10:03:17.448 +08:00 [INF] 站位映射配置已加载，版本: 1.0.0, 物理站位数: 12, 逻辑站位数: 4
2025-08-15 10:03:17.451 +08:00 [INF] 检查配置验证设置: EnableValidation=true
2025-08-15 10:03:17.452 +08:00 [INF] 开始执行配置验证
2025-08-15 10:03:17.455 +08:00 [INF] 配置验证完成，结果: IsValid=true, ErrorCount=0
2025-08-15 10:03:17.455 +08:00 [INF] 配置验证通过
2025-08-15 10:03:17.456 +08:00 [INF] 检查热重载配置: Configuration=true, EnableHotReload=true
2025-08-15 10:03:17.457 +08:00 [INF] 热重载启用，检查间隔: 30秒
2025-08-15 10:03:17.458 +08:00 [INF] 开始异步初始化站位状态
2025-08-15 10:03:17.459 +08:00 [INF] 站位状态初始化开始
2025-08-15 10:03:17.464 +08:00 [INF] 已初始化 4 个逻辑站位的棧板到达状态
2025-08-15 10:03:17.465 +08:00 [INF] 已注册消息处理器: FuncID=Stnoldf, MessageType=StnoldfRequest
2025-08-15 10:03:17.524 +08:00 [INF] 服务状态变更: "Stopped" -> "Starting", 原因: 开始启动服务
2025-08-15 10:03:17.527 +08:00 [INF] 首次加载配置
2025-08-15 10:03:17.528 +08:00 [INF] 开始加载视觉集成配置，配置文件路径: appsettings.visualintegration.json
2025-08-15 10:03:17.541 +08:00 [INF] 成功从配置文件加载配置
2025-08-15 10:03:17.548 +08:00 [INF] 配置加载完成
2025-08-15 10:03:17.548 +08:00 [INF] 配置加载完成
2025-08-15 10:03:17.549 +08:00 [INF] 正在连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:03:17.553 +08:00 [INF] 开始连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:03:17.557 +08:00 [INF] 成功连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:03:17.559 +08:00 [INF] 启动网络接收循环
2025-08-15 10:03:17.560 +08:00 [INF] 连接已建立，开始心跳监控
2025-08-15 10:03:17.561 +08:00 [INF] 开始心跳监控，间隔: 5秒
2025-08-15 10:03:17.564 +08:00 [INF] 连接状态变更: true, 服务器: 127.0.0.1:8080
2025-08-15 10:03:17.566 +08:00 [INF] TCP连接建立成功
2025-08-15 10:03:17.567 +08:00 [INF] 发送 ASRSOnline 上线请求: FuncSeqNo=1
2025-08-15 10:03:17.626 +08:00 [INF] 收到 ASRSOnline 响应: FuncSeqNo=1, Result=true
2025-08-15 10:03:17.626 +08:00 [INF] 启动心跳监控
2025-08-15 10:03:17.627 +08:00 [WRN] 心跳监控已经在运行中
2025-08-15 10:03:17.627 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:03:17.628 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:03:17.628 +08:00 [INF] 服务状态变更: "Starting" -> "Running", 原因: 服务启动成功
2025-08-15 10:03:17.630 +08:00 [INF] 视觉集成服务启动成功
2025-08-15 10:03:46.605 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 10:03:47.019 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 10:03:47.024 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 10:03:47.458 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 10:03:47.477 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 10:03:47.477 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 10:04:19.545 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508154513
2025-08-15 10:04:33.632 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=10
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:04:33.632 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=11
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:04:39.278 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508154513, 条码=6993931508107
2025-08-15 10:04:37.128 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=12
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:04:41.602 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 10:05:56.604 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:04:4”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:04:4”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:04:4”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:f01d6b65-aeb2-4597-b2a9-71eb2bb1f99b
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\RecognitionResultProcessor.cs:line 196
2025-08-15 10:05:56.740 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=14
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:05:56.736 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=13
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:05:56.820 +08:00 [ERR] 连续4次心跳失败，连接可能已断开
2025-08-15 10:05:56.825 +08:00 [ERR] 连续5次心跳失败，连接可能已断开
2025-08-15 10:05:56.830 +08:00 [INF] 已发送识别结果确认消息: 站位=1111, 结果=False
2025-08-15 10:05:56.893 +08:00 [ERR] 解析消息帧失败
System.ArgumentException: 无效的消息帧格式 (Parameter 'messageFrame')
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame, Int32 offset, Int32 count) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 118
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 85
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 509
2025-08-15 10:06:24.399 +08:00 [INF] 服务状态变更: "Running" -> "Stopping", 原因: 开始停止服务
2025-08-15 10:06:24.400 +08:00 [INF] 正在停止视觉集成服务
2025-08-15 10:06:24.401 +08:00 [INF] 停止心跳监控
2025-08-15 10:06:24.402 +08:00 [INF] 心跳监控已停止
2025-08-15 10:06:24.403 +08:00 [INF] 断开与视觉识别系统的连接: 127.0.0.1:8080
2025-08-15 10:06:24.405 +08:00 [INF] 已停止网络接收循环
2025-08-15 10:06:24.405 +08:00 [INF] 连接已断开，停止心跳监控
2025-08-15 10:06:24.406 +08:00 [WRN] 心跳监控未在运行
2025-08-15 10:06:24.407 +08:00 [INF] 连接状态变更: false, 服务器: 127.0.0.1:8080
2025-08-15 10:06:24.416 +08:00 [ERR] 接收循环异常
System.IO.IOException: Unable to read data from the transport connection: 由于线程退出或应用程序请求，已中止 I/O 操作。.
 ---> System.Net.Sockets.SocketException (995): 由于线程退出或应用程序请求，已中止 I/O 操作。
   --- End of inner exception stack trace ---
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask`1.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 481
2025-08-15 10:06:24.446 +08:00 [INF] TCP连接已断开
2025-08-15 10:06:24.452 +08:00 [INF] 服务状态变更: "Stopping" -> "Stopped", 原因: 服务停止成功
2025-08-15 10:06:24.476 +08:00 [INF] 视觉集成服务停止成功
2025-08-15 10:06:28.333 +08:00 [INF] 服务已停止或正在停止，当前状态: "Stopped"
2025-08-15 10:06:28.334 +08:00 [WRN] 心跳监控未在运行
2025-08-15 10:14:27.892 +08:00 [INF] 创建StationManager实例
2025-08-15 10:14:27.920 +08:00 [INF] 创建StationMappingService实例，配置文件路径: D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Presentation.Rest\bin\Debug\station-mapping.json
2025-08-15 10:14:27.922 +08:00 [INF] 配置文件是否存在: true
2025-08-15 10:14:27.962 +08:00 [INF] 站位映射配置已加载，版本: 1.0.0, 物理站位数: 12, 逻辑站位数: 4
2025-08-15 10:14:27.964 +08:00 [INF] 检查配置验证设置: EnableValidation=true
2025-08-15 10:14:27.964 +08:00 [INF] 开始执行配置验证
2025-08-15 10:14:27.965 +08:00 [INF] 配置验证完成，结果: IsValid=true, ErrorCount=0
2025-08-15 10:14:27.966 +08:00 [INF] 配置验证通过
2025-08-15 10:14:27.966 +08:00 [INF] 检查热重载配置: Configuration=true, EnableHotReload=true
2025-08-15 10:14:27.967 +08:00 [INF] 热重载启用，检查间隔: 30秒
2025-08-15 10:14:27.969 +08:00 [INF] 开始异步初始化站位状态
2025-08-15 10:14:27.970 +08:00 [INF] 站位状态初始化开始
2025-08-15 10:14:27.973 +08:00 [INF] 已初始化 4 个逻辑站位的棧板到达状态
2025-08-15 10:14:27.974 +08:00 [INF] 已注册消息处理器: FuncID=Stnoldf, MessageType=StnoldfRequest
2025-08-15 10:14:28.033 +08:00 [INF] 服务状态变更: "Stopped" -> "Starting", 原因: 开始启动服务
2025-08-15 10:14:28.037 +08:00 [INF] 首次加载配置
2025-08-15 10:14:28.038 +08:00 [INF] 开始加载视觉集成配置，配置文件路径: appsettings.visualintegration.json
2025-08-15 10:14:28.053 +08:00 [INF] 成功从配置文件加载配置
2025-08-15 10:14:28.062 +08:00 [INF] 配置加载完成
2025-08-15 10:14:28.062 +08:00 [INF] 配置加载完成
2025-08-15 10:14:28.063 +08:00 [INF] 正在连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:14:28.065 +08:00 [INF] 开始连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:14:28.073 +08:00 [INF] 成功连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:14:28.075 +08:00 [INF] 启动网络接收循环
2025-08-15 10:14:28.075 +08:00 [INF] 连接已建立，开始心跳监控
2025-08-15 10:14:28.076 +08:00 [INF] 开始心跳监控，间隔: 5秒
2025-08-15 10:14:28.079 +08:00 [INF] 连接状态变更: true, 服务器: 127.0.0.1:8080
2025-08-15 10:14:28.081 +08:00 [INF] TCP连接建立成功
2025-08-15 10:14:28.081 +08:00 [INF] 发送 ASRSOnline 上线请求: FuncSeqNo=1
2025-08-15 10:14:28.103 +08:00 [INF] 收到 ASRSOnline 响应: FuncSeqNo=1, Result=true
2025-08-15 10:14:28.104 +08:00 [INF] 启动心跳监控
2025-08-15 10:14:28.104 +08:00 [WRN] 心跳监控已经在运行中
2025-08-15 10:14:28.105 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:14:28.105 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:14:28.106 +08:00 [INF] 服务状态变更: "Starting" -> "Running", 原因: 服务启动成功
2025-08-15 10:14:28.107 +08:00 [INF] 视觉集成服务启动成功
2025-08-15 10:14:54.908 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 10:14:55.198 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 10:14:55.202 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 10:14:55.464 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 10:14:55.479 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 10:14:55.484 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 10:15:05.628 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508156775
2025-08-15 10:15:09.590 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=10
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:15:09.614 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508156775, 条码=6961960312417
2025-08-15 10:15:12.161 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=11
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:15:23.617 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:15:1”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:15:1”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:15:1”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:3d737365-1910-4c4f-9909-2c9b636228f2
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\RecognitionResultProcessor.cs:line 196
2025-08-15 10:15:23.661 +08:00 [INF] 已发送识别结果确认消息: 站位=1111, 结果=False
2025-08-15 10:15:23.720 +08:00 [ERR] 解析消息帧失败
System.ArgumentException: 无效的消息帧格式 (Parameter 'messageFrame')
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame, Int32 offset, Int32 count) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 118
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 85
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 509
2025-08-15 10:15:35.416 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 10:15:35.584 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 10:15:35.588 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 10:15:35.763 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 10:15:35.775 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 10:15:35.776 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 10:15:40.442 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508159305
2025-08-15 10:15:54.620 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=18
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:15:54.640 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508159305, 条码=6995924827912
2025-08-15 10:15:58.898 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=19
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:16:11.423 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=20
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:16:11.509 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 10:16:22.104 +08:00 [ERR] 处理识别结果异常: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:16:1”。)
System.AggregateException: One or more errors occurred. (字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:16:1”。)
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): 字符串或二进制数据将在表“KSF_WMS_BS.dbo.IO_CONTROL_APPLY”，列“CREATE_TIME”中被截断。截断值:“25/8/15 周五 10:16:1”。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.ExecuteScalarImplAsync[T](IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1241
   at Kean.Infrastructure.Database.MssqlDapperSchema`1.Add(T entity) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Database\Seedwork\MssqlDapperSchema.cs:line 183
   at Kean.Infrastructure.Repository.IoRepository.CreateApply(Apply Apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Infrastructure.Repository\IoRepository.cs:line 25
   at Kean.Domain.Basic.CommandHandlers.CreateApplyCommandHandler.Handle(CreateApplyCommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Basic\CommandHandlers\CreateApplyCommandHandler.cs:line 33
   at Kean.Domain.CommandHandler`1.MediatR.IRequestHandler<T>.Handle(T request, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandHandler.cs:line 28
   at MediatR.Wrappers.RequestHandlerWrapperImpl`1.<>c__DisplayClass1_0.<<Handle>g__Handler|0>d.MoveNext()
ClientConnectionId:3d737365-1910-4c4f-9909-2c9b636228f2
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Kean.Domain.CommandBus.Execute(ICommand command, CancellationToken cancellationToken) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.Seedwork\CommandBus.cs:line 102
   at Kean.Application.Command.Implements.BasicService.CreateApply(Apply apply) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Application.Command\Implements\BasicService.cs:line 300
   at Wms.Core.Domain.VisualIntegration.Services.RecognitionResultProcessor.ProcessRecognitionResultAsync(RecognitionResult result, String funcSeqNo) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\RecognitionResultProcessor.cs:line 196
2025-08-15 10:16:22.141 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=22
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:16:22.154 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=21
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:16:22.160 +08:00 [ERR] 连续4次心跳失败，连接可能已断开
2025-08-15 10:16:22.161 +08:00 [ERR] 连续5次心跳失败，连接可能已断开
2025-08-15 10:16:22.175 +08:00 [INF] 已发送识别结果确认消息: 站位=1111, 结果=False
2025-08-15 10:16:22.249 +08:00 [ERR] 解析消息帧失败
System.ArgumentException: 无效的消息帧格式 (Parameter 'messageFrame')
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame, Int32 offset, Int32 count) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 118
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 85
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 509
2025-08-15 10:50:04.406 +08:00 [INF] 创建StationManager实例
2025-08-15 10:50:04.431 +08:00 [INF] 创建StationMappingService实例，配置文件路径: D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Presentation.Rest\bin\Debug\station-mapping.json
2025-08-15 10:50:04.432 +08:00 [INF] 配置文件是否存在: true
2025-08-15 10:50:04.467 +08:00 [INF] 站位映射配置已加载，版本: 1.0.0, 物理站位数: 12, 逻辑站位数: 4
2025-08-15 10:50:04.469 +08:00 [INF] 检查配置验证设置: EnableValidation=true
2025-08-15 10:50:04.470 +08:00 [INF] 开始执行配置验证
2025-08-15 10:50:04.471 +08:00 [INF] 配置验证完成，结果: IsValid=true, ErrorCount=0
2025-08-15 10:50:04.472 +08:00 [INF] 配置验证通过
2025-08-15 10:50:04.473 +08:00 [INF] 检查热重载配置: Configuration=true, EnableHotReload=true
2025-08-15 10:50:04.474 +08:00 [INF] 热重载启用，检查间隔: 30秒
2025-08-15 10:50:04.475 +08:00 [INF] 开始异步初始化站位状态
2025-08-15 10:50:04.476 +08:00 [INF] 站位状态初始化开始
2025-08-15 10:50:04.483 +08:00 [INF] 已初始化 4 个逻辑站位的棧板到达状态
2025-08-15 10:50:04.485 +08:00 [INF] 已注册消息处理器: FuncID=Stnoldf, MessageType=StnoldfRequest
2025-08-15 10:50:04.547 +08:00 [INF] 服务状态变更: "Stopped" -> "Starting", 原因: 开始启动服务
2025-08-15 10:50:04.551 +08:00 [INF] 首次加载配置
2025-08-15 10:50:04.553 +08:00 [INF] 开始加载视觉集成配置，配置文件路径: appsettings.visualintegration.json
2025-08-15 10:50:04.566 +08:00 [INF] 成功从配置文件加载配置
2025-08-15 10:50:04.574 +08:00 [INF] 配置加载完成
2025-08-15 10:50:04.576 +08:00 [INF] 配置加载完成
2025-08-15 10:50:04.577 +08:00 [INF] 正在连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:50:04.579 +08:00 [INF] 开始连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:50:04.584 +08:00 [INF] 成功连接到视觉识别系统: 127.0.0.1:8080
2025-08-15 10:50:04.586 +08:00 [INF] 启动网络接收循环
2025-08-15 10:50:04.587 +08:00 [INF] 连接已建立，开始心跳监控
2025-08-15 10:50:04.589 +08:00 [INF] 开始心跳监控，间隔: 5秒
2025-08-15 10:50:04.594 +08:00 [INF] 连接状态变更: true, 服务器: 127.0.0.1:8080
2025-08-15 10:50:04.597 +08:00 [INF] TCP连接建立成功
2025-08-15 10:50:04.598 +08:00 [INF] 发送 ASRSOnline 上线请求: FuncSeqNo=1
2025-08-15 10:50:04.648 +08:00 [INF] 收到 ASRSOnline 响应: FuncSeqNo=1, Result=true
2025-08-15 10:50:04.649 +08:00 [INF] 启动心跳监控
2025-08-15 10:50:04.649 +08:00 [WRN] 心跳监控已经在运行中
2025-08-15 10:50:04.650 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:50:04.651 +08:00 [INF] 消息处理器注册完成
2025-08-15 10:50:04.651 +08:00 [INF] 服务状态变更: "Starting" -> "Running", 原因: 服务启动成功
2025-08-15 10:50:04.658 +08:00 [INF] 视觉集成服务启动成功
2025-08-15 10:50:49.424 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=9
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:50:50.511 +08:00 [INF] 适配器处理棧板到达事件，站位: 1111
2025-08-15 10:50:50.934 +08:00 [INF] 处理棧板到达事件，站位: 1111
2025-08-15 10:50:50.938 +08:00 [INF] 识别设备已启用，发送识别请求，站位: 1111
2025-08-15 10:50:51.398 +08:00 [INF] 发送站位 1111 的识别请求
2025-08-15 10:50:51.425 +08:00 [INF] 站位 1111 的识别请求已接受
2025-08-15 10:50:51.426 +08:00 [INF] 适配器处理棧板到达事件完成，站位: 1111, 结果: true
2025-08-15 10:50:57.247 +08:00 [INF] 收到识别结果消息: 站位=1111, 序列号=PLT202508159955
2025-08-15 10:51:01.232 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=13
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:51:01.800 +08:00 [INF] 准备创建Apply记录: 站位=1111, 序列号=PLT202508159955, 条码=6991628543617
2025-08-15 10:51:05.023 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=14
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:51:07.311 +08:00 [INF] 成功处理识别结果并创建Apply记录: ID=1036, 站位=1111, 序列号=PLT202508159955
2025-08-15 10:51:10.075 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=15
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:51:10.078 +08:00 [ERR] 连续3次心跳失败，连接可能已断开
2025-08-15 10:51:10.097 +08:00 [INF] 已发送识别结果确认消息: 站位=1111, 结果=True
2025-08-15 10:51:11.013 +08:00 [ERR] 解析消息帧失败
System.ArgumentException: 无效的消息帧格式 (Parameter 'messageFrame')
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame, Int32 offset, Int32 count) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 118
   at Wms.Core.Domain.VisualIntegration.Services.MessageFrameProcessor.ExtractMessage(Byte[] messageFrame) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageFrameProcessor.cs:line 85
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.ReceiveLoopAsync(CancellationToken ct) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 509
2025-08-15 10:51:47.660 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=18
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:51:52.672 +08:00 [ERR] 发送心跳消息失败
System.TimeoutException: 等待响应超时: FuncID=CheckAlive, FuncSeqNo=17
   at Wms.Core.Domain.VisualIntegration.Services.MessageProcessor.SendMessageAsync[TRequest,TResponse](TRequest request, Int32 timeoutMs) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\MessageProcessor.cs:line 179
   at Wms.Core.Domain.VisualIntegration.Services.HeartbeatMonitor.SendHeartbeat(Object state) in D:\MyProject\K\康师傅\代码\SIASUN_KSF_BS\Server\Wms.Core.Domain.VisualIntegration\Services\HeartbeatMonitor.cs:line 204
2025-08-15 10:52:18.443 +08:00 [INF] 服务状态变更: "Running" -> "Stopping", 原因: 开始停止服务
2025-08-15 10:52:18.446 +08:00 [INF] 正在停止视觉集成服务
2025-08-15 10:52:18.448 +08:00 [INF] 停止心跳监控
2025-08-15 10:52:18.449 +08:00 [INF] 心跳监控已停止
2025-08-15 10:52:18.454 +08:00 [INF] 断开与视觉识别系统的连接: 127.0.0.1:8080
2025-08-15 10:52:18.456 +08:00 [INF] 已停止网络接收循环
2025-08-15 10:52:18.457 +08:00 [INF] 连接已断开，停止心跳监控
2025-08-15 10:52:18.458 +08:00 [WRN] 心跳监控未在运行
2025-08-15 10:52:18.459 +08:00 [INF] 连接状态变更: false, 服务器: 127.0.0.1:8080
2025-08-15 10:52:18.462 +08:00 [INF] TCP连接已断开
2025-08-15 10:52:18.463 +08:00 [INF] 服务状态变更: "Stopping" -> "Stopped", 原因: 服务停止成功
2025-08-15 10:52:18.469 +08:00 [INF] 视觉集成服务停止成功
2025-08-15 10:52:18.471 +08:00 [INF] 网络接收循环退出
2025-08-15 10:52:18.774 +08:00 [INF] 服务已停止或正在停止，当前状态: "Stopped"
2025-08-15 10:52:18.776 +08:00 [WRN] 心跳监控未在运行
