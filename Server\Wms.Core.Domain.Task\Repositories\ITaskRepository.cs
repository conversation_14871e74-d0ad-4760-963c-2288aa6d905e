﻿using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Kean.Domain.Task.Repositories
{
    /// <summary>
    /// 表示任务仓库
    /// </summary>
    public interface ITaskRepository
    {
        /// <summary>
        /// 申请条码锁
        /// </summary>
        /// <param name="barcode">条码</param>
        Task<IDisposable> AcquireLock(string barcode);

        /// <summary>
        /// 是否有任务
        /// </summary>
        /// <param name="cell">位置</param>
        /// <returns>如果有任务，为 true，否则为 false</returns>
        Task<bool> HasTask(int[] cell);

        /// <summary>
        /// 是否有任务
        /// </summary>
        /// <param name="original">操作位置</param>
        /// <param name="destination">目标位置</param>
        /// <param name="type">任务类型</param>
        /// <returns>如果有任务，为 true，否则为 false</returns>
        Task<bool> HasTask(int? original, int? destination, TaskType? type = null);

        /// <summary>
        /// 是否有任务
        /// </summary>
        /// <param name="barcode">条码</param>
        /// <param name="originalGroup">操作位置组</param>
        /// <param name="destinationGroup">目标位置组</param>
        /// <returns>如果有任务，为 true，否则为 false</returns>
        Task<bool> HasTask(string barcode, string originalGroup = null, string destinationGroup = null);

        /// <summary>
        /// 任务是否存在
        /// </summary>
        /// <param name="id">标识</param>
        /// <returns>如果存在，为 true，否则为 false</returns>
        Task<bool> IsExist(int id);

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="warehouse">库房</param>
        /// <returns>任务</returns>
        Task<IEnumerable<Models.Task>> GetTasks(int? warehouse);

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="original">操作位置</param>
        /// <param name="destination">目标位置</param>
        /// <param name="type">任务类型</param>
        /// <returns>任务</returns>
        Task<IEnumerable<Models.Task>> GetTasks(int? original, int? destination, TaskType? type = null);

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="id">标识</param>
        /// <returns>任务</returns>
        Task<Models.Task> GetTask(int id);

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="cell">位置</param>
        /// <returns>任务</returns>
        Task<Models.Task> GetTask(Cell cell);

        /// <summary>
        /// 获取后续任务
        /// </summary>
        /// <param name="id">标识</param>
        /// <returns>后续任务</returns>
        Task<IEnumerable<Models.Task>> GetFollows(int id);

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="task">任务</param>
        /// <returns>标识</returns>
        Task<int> CreateTask(Models.Task task);

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">标识</param>
        System.Threading.Tasks.Task DeleteTask(int id);

        /// <summary>
        /// 更新前续连接
        /// </summary>
        /// <param name="old">先前连接</param>
        /// <param name="new">当前连接</param>
        System.Threading.Tasks.Task UpdatePrevious(int old, int @new);

        /// <summary>
        /// 更新计划下达数量
        /// </summary>
        /// <param name="id">标识</param>
        //System.Threading.Tasks.Task UpdateOrderQuantity(string manageRemark);
        System.Threading.Tasks.Task UpdateOrderQuantity(string manageRemark, bool isComplete);
        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="id">标识</param>l
        /// <param name="status">状态</param>
        /// <param name="message">消息</param>
        System.Threading.Tasks.Task UpdateStatus(int id, TaskState state, string message = null);

        /// <summary>
        /// 更新优先级
        /// </summary>
        /// <param name="id">标识</param>
        /// <param name="priority">优先级</param>
        System.Threading.Tasks.Task UpdatePriority(int id, int priority);

        /// <summary>
        /// 获取触发
        /// </summary>
        /// <param name="warehouse">库房</param>
        /// <param name="device">设备</param>
        /// <returns>触发</returns>
        Task<IEnumerable<Trigger>> GetTriggers(int? warehouse = null, string device = null);

        /// <summary>
        /// 获取触发
        /// </summary>
        /// <param name="id">标识</param>
        /// <returns>触发</returns>
        Task<Trigger> GetTrigger(int id);

        /// <summary>
        /// 创建触发
        /// </summary>
        /// <param name="trigger">触发</param>
        /// <returns>标识</returns>
        Task<int> CreateTrigger(Trigger trigger);

        /// <summary>
        /// 删除触发
        /// </summary>
        /// <param name="id">标识</param>
        System.Threading.Tasks.Task DeleteTrigger(int id);

        /// <summary>
        /// 更新触发
        /// </summary>
        /// <param name="id">标识</param>
        /// <param name="count">计数</param>
        /// <param name="remark">备注</param>
        System.Threading.Tasks.Task UpdateTrigger(int id, int count, string remark);

        /// <summary>
        /// 记录触发
        /// </summary>
        /// <param name="trigger">触发</param>
        System.Threading.Tasks.Task LogTrigger(Trigger trigger);

        public Dictionary<string, decimal> GetMaterialCountLaneway(int planListId);
    }
}
