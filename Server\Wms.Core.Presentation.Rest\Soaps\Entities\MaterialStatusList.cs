﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    [XmlRoot("SAPPushInventoryStatus", Namespace = "http://tempuri.org/")]
    public class MaterialStatusList
    {
        public string sig { get; set; }
        [XmlArray("input")]
        [XmlArrayItem("SAPPushInventoryStatusData")]
        public List<MaterialContract> lstMaterial { get; set; } = new List<MaterialContract>();
    }
}
