﻿using AutoMapper;
using Kean.Domain.Order.Commands;
using Kean.Domain.Order.Models;
using Kean.Domain.Shared;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Domain.Order
{
    /// <summary>
    /// 共享服务
    /// </summary>
    public class SharedService(
        ICommandBus commandBus,  // 命令总线
        IDefaultDb database,
        IMapper mapper
    ) : IOrderService
    {
        public bool checkParemeter(string parameter, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string[] parameters = parameter.Split("|");
            if (parameters.Length != 8)
            {
                bResult = false;
                sResult = $"参数:{parameter} 数量:{parameters.Length}不正确";
            }

            return bResult;
        }



        public bool PlanSelectAndUpdate(string parameter, out int PLAN_ID, out int PLAN_LIST_ID)
        {
            string sResult = string.Empty;
            if (!checkParemeter(parameter, out sResult))
            {
                PLAN_ID = -1;
                PLAN_LIST_ID = -1;
                return false;
            }
            string[] parameters = parameter.Split("|");
            //获取69码及识别码
            string str69 = parameters[6];
            string strID = parameters[7];
            //根据69码找GOODS_MAIN
            ISchema<T_GOODS_MAIN> schGoods = database.From<T_GOODS_MAIN>().Where(g => g.GOODS_CODE_69 == str69);
            if (!string.IsNullOrEmpty(strID))
            {
                schGoods.Where(i => i.GOODS_CODE_ID == strID);
            }
            T_GOODS_MAIN mGoodsMain = schGoods.Single().Result;
            if (mGoodsMain == null)
            {
                sResult = $"通过69码:{str69}识别码:{strID}未找到对应物料";
                PLAN_ID = -1;
                PLAN_LIST_ID = -1;
                return false;
            }
            //获取产线号
            string lineNo = parameters[3];
            //根据产线获取班别及早晚班时间
            T_WORK_GROUP wordGroup = database.From<T_WORK_GROUP>().Where(l => l.PRODUCTION_LINE == lineNo).Single().Result;
            //获取当前系统日期
            //若为次日凌晨夜班,当前日期-1
            int iTimeNow = Convert.ToInt32(DateTime.Now.ToString("HHmm"));
            int iStartTime = Convert.ToInt32(wordGroup.WORK_START_TIME);
            DateTime dtNow = DateTime.Today;
            if (iTimeNow >= 0 && iTimeNow < iStartTime)
            {
                dtNow = dtNow.AddDays(-1);

            }
            string a = dtNow.ToString("yyyyMMddHHmmss");
            string sBatch = dtNow.ToString("yyyyMMdd");
            IEnumerable<V_PLAN_LIST> planList = database.From<V_PLAN_LIST>().Where(p => p.CREATE_TIME >= dtNow && p.CREATE_TIME < dtNow.AddDays(1) && p.GOODS_ID == mGoodsMain.GOODS_ID && p.PLAN_TYPE == 1).OrderBy(k => k.CREATE_TIME, Infrastructure.Database.Order.Descending).Select().Result;

            if (planList.Any())
            {
                IEnumerable<V_PLAN_LIST> tmp = planList.Where(k => k.PLAN_SOURCE == "SAP");
                if (tmp.Any())
                {
                    //V_PLAN_LIST vTmp = tmp.OrderByDescending(t => t.PLAN_ID).Single();
                    V_PLAN_LIST vPlanList = planList.OrderByDescending(t => t.PLAN_ID).Single();
                    PLAN_ID = vPlanList.PLAN_ID;
                    T_PLAN_LIST mPlanList = database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == vPlanList.PLAN_LIST_ID).Single().Result;
                    if (mPlanList.GOODS_BATCH_NO != parameters[2])
                    {
                        mPlanList.GOODS_BATCH_NO = parameters[2];
                    }
                    //mPlanList.ORDERED_QUANTITY += Convert.ToDecimal(parameters[0]);
                    mPlanList.GOODS_MFG = DateTime.Parse($"{parameters[2][..4]}-{parameters[2][4..6]}-{parameters[2][6..8]}");
                    database.From<T_PLAN_LIST>().Update(mPlanList);
                    PLAN_ID = mPlanList.PLAN_ID;
                    PLAN_LIST_ID = mPlanList.PLAN_LIST_ID;
                    //PLAN_ID = vTmp.PLAN_ID;
                    //PLAN_LIST_ID = vTmp.PLAN_LIST_ID;
                    return true;
                }
                else
                {
                    V_PLAN_LIST vPlanList = planList.OrderByDescending(t => t.PLAN_ID).Single();
                    PLAN_ID = vPlanList.PLAN_ID;
                    T_PLAN_LIST mPlanList = database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == vPlanList.PLAN_LIST_ID).Single().Result;
                    if (mPlanList.GOODS_BATCH_NO != parameters[2])
                    {
                        mPlanList.GOODS_BATCH_NO = parameters[2];
                    }
                    //mPlanList.ORDERED_QUANTITY += Convert.ToDecimal(parameters[0]);
                    mPlanList.GOODS_MFG = DateTime.Parse($"{parameters[2][..4]}-{parameters[2][4..6]}-{parameters[2][6..8]}");
                    database.From<T_PLAN_LIST>().Update(mPlanList);
                    PLAN_ID = mPlanList.PLAN_ID;
                    PLAN_LIST_ID = mPlanList.PLAN_LIST_ID;
                    return true;
                }
            }
            else
            {
                sResult = $"未找到满足当前物料:{mGoodsMain.GOODS_NAME},创建日期为:{sBatch}的生产订单";
                PLAN_ID = -1;
                PLAN_LIST_ID = -1;
                return false;
            }


            //throw new System.NotImplementedException();

        }

        /*
        * 实现 Kean.Domain.Shared.IOrderService.Progress 方法
        */
        public Task Progress(IEnumerable<KeyValuePair<int, decimal>> lines, bool buffered) =>
            commandBus.Execute(new ProgressCommand
            {
                Buffered = buffered,
                Lines = lines
            });

        public async Task<JArray> GetList(int planId)
        {
            var planList = await database.From<T_PLAN_LIST>().Where(c => c.PLAN_ID == planId).Select();
            var orderLine = mapper.Map<IEnumerable<OrderLine>>(planList);
            return JArray.FromObject(orderLine);
        }

        public async Task<JArray> GetOrderLine(int planListId)
        {
            var planList = await database.From<T_PLAN_LIST>().Where(c => c.PLAN_LIST_ID == planListId).Select();
            var orderLine = mapper.Map<IEnumerable<OrderLine>>(planList);
            return JArray.FromObject(orderLine);
        }


    }
}
