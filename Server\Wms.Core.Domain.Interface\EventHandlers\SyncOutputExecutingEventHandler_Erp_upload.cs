﻿using Kean.Domain.Interface.Events;
using Kean.Domain.Interface.RemoteClients;
using Newtonsoft.Json.Linq;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Interface.EventHandlers
{
    /// <summary>
    /// 同步输出命令执行时，处理向 WCS 获取路径的接口
    /// </summary>
    public sealed class SyncOutputExecutingEventHandler_Sap_upload(
        ICommandBus commandBus, // 命令总线
        ISapClient sapClient    // SAP 接口
    ) : EventHandler<SyncOutputExecutingEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(SyncOutputExecutingEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Scope == "sap" && @event.Function == "UploadTask")//@event.Scope == "WCS" && @event.Function == "Route")
            {
                try
                {
                    var data = @event.Data as string;
                    @event.Respond(await sapClient.TaskUpload(@event.Data.ToString()));
                }
                catch (Exception ex)
                {
                    await commandBus.Notify(nameof(Exception), ErrorEnum.调用WCS接口失败, ex.Message,
                        cancellationToken: cancellationToken);
                }
            }
        }
    }
}
