﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Engineering;
using System;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    public class MaterialContractStatus
    {
        [DataMember] public string MBLNR { get; set; }  // 物料凭证编号
        [DataMember] public string MJAHR { get; set; }  // 物料凭证年度
        [DataMember] public string BLART { get; set; }  // 凭证类型
        [DataMember] public string BLDAT { get; set; }  // 凭证中的凭证日期
        [DataMember] public string BUDAT { get; set; }  // 凭证中的过账日期
        [DataMember] public string CPUDT { get; set; }  // 会计凭证录入日期
        [DataMember] public string CPUTM { get; set; }  // 输入时间
        [DataMember] public string USNAM { get; set; }  // 用户名
        [DataMember] public string BKTXT { get; set; }  // 凭证抬头文本
        [DataMember] public string LGPLA { get; set; }  // 仓库代码
        [DataMember] public string VTXTK { get; set; }  // 货主代码
        [DataMember] public string MTART { get; set; }  // 物料类型
        [DataMember] public string MATKL { get; set; }  // 物料组
        [DataMember] public string XAUTO { get; set; }  // 项目自动创建
        [DataMember] public string KZBEW { get; set; }  // 移动标识
        [DataMember] public string KZZUG { get; set; }  // 收货标识
        [DataMember] public string ZEILE { get; set; }  // 物料凭证中的项目
        [DataMember] public string BWART { get; set; }  // 移动类型
        [DataMember] public string MATNR { get; set; }  // 物料号
        [DataMember] public string MAKTX { get; set; }  // 物料描述
        [DataMember] public string WERKS { get; set; }  // 工厂
        [DataMember] public string WNAME1 { get; set; }  // 工厂名称
        [DataMember] public string LGORT { get; set; }  // 库存地点
        [DataMember] public string LGOBE { get; set; }  // 库存地点描述
        [DataMember] public string CHARG { get; set; }  // 批号
        [DataMember] public string INSMK { get; set; }  // 库存类型
        [DataMember] public string SOBKZ { get; set; }  // 特殊库存标识
        [DataMember] public string MENGE { get; set; }  // 数量
        [DataMember] public string MEINS { get; set; }  // 基本计量单位
        [DataMember] public string ERFMG { get; set; }  // 以输入单位计的数量
        [DataMember] public string ERFME { get; set; }  // 条目单位
        [DataMember] public string DMBTR { get; set; }  // 按本位币计的金额
        [DataMember] public string WAERS { get; set; }  // 货币码
        [DataMember] public string LIFNR { get; set; }  // 供应商帐户号
        [DataMember] public string LNAME1 { get; set; }  // 供应商名称
        [DataMember] public string EBELN { get; set; }  // 采购订单编号
        [DataMember] public string EBELP { get; set; }  // 采购凭证的项目编号
        [DataMember] public string KUNNR { get; set; }  // 客户的帐户号
        [DataMember] public string KNAME1 { get; set; }  // 客户名称
        [DataMember] public string WEMPF { get; set; }  // 收货方/运达方
        [DataMember] public string KDAUF { get; set; }  //  销售订单数
        [DataMember] public string KDPOS { get; set; }  //  销售订单中的项目编号
        [DataMember] public string XBLNR { get; set; }  // 参考凭证号
        [DataMember] public string BUKRS { get; set; }  //  公司代码
        [DataMember] public string BUTXT { get; set; }  //  公司代码或公司的名称
        [DataMember] public string BELNR { get; set; }  //  会计凭证编号
        [DataMember] public string SAKTO { get; set; }  //  总帐科目编号
        [DataMember] public string KOSTL { get; set; }  //  成本中心
        [DataMember] public string AUFNR { get; set; }  //  订单号
        [DataMember] public string ANLN1 { get; set; }  //   主资产号
        [DataMember] public string VFDAT { get; set; }  //  货架寿命到期日
        [DataMember] public string HSDAT { get; set; }  //   生产日期
        [DataMember] public string UMMAT { get; set; }  //   收货/发货物料
        [DataMember] public string UMWRK { get; set; }  // 收货/发货工厂
        [DataMember] public string UMLGO { get; set; }  // 收货/发货库存地点
        [DataMember] public string UMCHA { get; set; }  // 收货/发货批量
        [DataMember] public string GRUND { get; set; }  // 移动原因
        [DataMember] public string SGTXT { get; set; }  // 项目文本（申请用途）

    }
}
