using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Wms.Core.Domain.VisualIntegration.Interfaces;
using Wms.Core.Domain.VisualIntegration.Models;
using Wms.Core.Domain.VisualIntegration.Models.Messages.RecognitionResult;
using Domain_KSF.Models;
using Kean.Application.Command.Interfaces;
using Kean.Application.Command.ViewModels;

namespace Wms.Core.Domain.VisualIntegration.Services
{
    /// <summary>
    /// 识别结果处理器，负责处理视觉识别系统返回的识别结果
    /// </summary>
    public class RecognitionResultProcessor : IRecognitionResultProcessor
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IMessageProcessor _messageProcessor;
        private readonly ILogger<RecognitionResultProcessor> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="basicService">基础服务接口，用于创建Apply记录</param>
        /// <param name="messageProcessor">消息处理器接口，用于发送确认消息</param>
        /// <param name="logger">日志记录器</param>
        public RecognitionResultProcessor(
            IServiceScopeFactory scopeFactory,
            IMessageProcessor messageProcessor,
            ILogger<RecognitionResultProcessor> logger)
        {
            _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
            _messageProcessor = messageProcessor ?? throw new ArgumentNullException(nameof(messageProcessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 注册Stnoldf消息处理器
            _messageProcessor.RegisterMessageHandler<StnoldfRequest>(
                "Stnoldf", 
                async (message, seqNo) => await HandleStnoldfMessageAsync(message, seqNo));
        }

        /// <summary>
        /// 处理Stnoldf消息
        /// </summary>
        /// <param name="message">Stnoldf消息</param>
        /// <param name="seqNo">消息序列号</param>
        /// <returns>处理结果</returns>
        private async Task<bool> HandleStnoldfMessageAsync(StnoldfRequest message, string seqNo)
        {
            try
            {
                _logger.LogInformation($"收到识别结果消息: 站位={message.Content.Stno}, 序列号={message.Content.SER_ID}");

                // 验证消息
                if (!message.Validate())
                {
                    _logger.LogWarning($"识别结果消息验证失败: {seqNo}");
                    await SendStnoldfReplyAsync(message.Content.Stno, seqNo, false, "消息格式验证失败");
                    return false;
                }

                // 转换为RecognitionResult对象
                var recognitionResult = ConvertToRecognitionResult(message.Content);

                // 处理识别结果
                bool success;
                if (recognitionResult.IsSuccessful)
                {
                    success = await ProcessRecognitionResultAsync(recognitionResult, seqNo);
                }
                else
                {
                    success = await HandleRecognitionErrorAsync(recognitionResult);
                }

                // 发送确认消息
                await SendStnoldfReplyAsync(
                    message.Content.Stno,
                    seqNo,
                    success,
                    success ? string.Empty : "处理识别结果失败");

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理识别结果消息异常: {ex.Message}");
                await SendStnoldfReplyAsync(
                    message.Content.Stno,
                    seqNo,
                    false,
                    $"处理异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送StnoldfReply确认消息
        /// </summary>
        /// <param name="stationNo">站位编号</param>
        /// <param name="seqNo">消息序列号</param>
        /// <param name="success">处理结果</param>
        /// <param name="message">消息内容</param>
        /// <returns>发送任务</returns>
        private async Task<bool> SendStnoldfReplyAsync(string stationNo, string seqNo, bool success, string message)
        {
            try
            {
                var response = new StnoldfResponse
                {
                    FuncSeqNo = seqNo,
                    Content = new StnoldfResponseContent
                    {
                        Result = success,
                        Message = message,
                        Stno = stationNo
                    }
                };

                await _messageProcessor.SendMessageWithoutResponseAsync(response);
                _logger.LogInformation($"已发送识别结果确认消息: 站位={stationNo}, 结果={success}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发送识别结果确认消息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将StnoldfContent转换为RecognitionResult
        /// </summary>
        /// <param name="content">Stnoldf消息内容</param>
        /// <returns>识别结果对象</returns>
        private RecognitionResult ConvertToRecognitionResult(StnoldfContent content)
        {
            return new RecognitionResult
            {
                StationNo = content.Stno,
                SerialId = content.SER_ID,
                FactoryNo = content.Fact_No,
                ProductionDate = content.Prod_Date,
                ProductionLine = content.Prod_Line,
                ProductionTime = content.Prod_Time,
                LineCartonNo = content.Line_CartonNo,
                IdCode = content.IDCode,
                BarCode = content.BAR_CODE,
                StorageQuantity = int.TryParse(content.StoInQty, out int qty) ? qty : 0,
                ErrorCode = content.ErrCode,
                ErrorDescription = content.Code_Descb,
                RecognitionTime = DateTime.Now
            };
        }

        /// <summary>
        /// 处理识别结果
        /// </summary>
        /// <param name="result">识别结果</param>
        /// <param name="funcSeqNo">消息序列号</param>
        /// <returns>处理是否成功</returns>
        public async Task<bool> ProcessRecognitionResultAsync(RecognitionResult result, string funcSeqNo)
        {
            try
            {
                // 验证识别结果
                if (!result.Validate())
                {
                    _logger.LogWarning($"识别结果验证失败: {result.SerialId}");
                    return false;
                }

                // 构建Apply对象，按照需求4.2, 4.3, 4.4进行字段映射
                var apply = new Apply
                {
                    DeviceCode = result.StationNo,                    // DEVICE_CODE存储Stno
                    Stockbarcode = result.BarCode,                    // STOCK_BARCODE存储BAR_CODE
                    WarehouseCode = result.FactoryNo,                 // WAREHOUSE_CODE存储工厂别
                    Para01 = $"{result.LineCartonNo}|{1}",            // CONTROL_APPLY_PARA01存储Line_CartonNo | 追溯标识{0：不追溯，1：追溯}
                    Para02 = funcSeqNo,                               // CONTROL_APPLY_PARA02存储FuncSeqNo
                    Parameter = BuildParameterString(result),         // 使用"|"分隔存储字段
                    Status = result.IsSuccessful ? 1 : -1,            // 根据识别结果设置状态
                    Type = GetStationType(result.StationNo),          // 根据站位确定类型
                    Remark = result.ErrorCode != "0" ? result.ErrorDescription : null,
                    CreateTime = DateTime.Now.ToString("yyyyMMddHHmmss")
                };

                _logger.LogInformation($"准备创建Apply记录: 站位={result.StationNo}, 序列号={result.SerialId}, 条码={result.BarCode}");
                var createdId = 0;
                // 通过作用域解析 Scoped 服务，避免 Singleton->Scoped 直接依赖
                using (var scope = _scopeFactory.CreateScope())
                {
                    var basicService = scope.ServiceProvider.GetRequiredService<IBasicService>();
                    var (id, failure) = await basicService.CreateApply(apply);
                    if (failure != null)
                    {
                        _logger.LogWarning($"创建Apply记录失败: {failure.ErrorMessage}");
                        return false;
                    }
                    createdId = id;
                }

                _logger.LogInformation($"成功处理识别结果并创建Apply记录: ID={createdId}, 站位={result.StationNo}, 序列号={result.SerialId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理识别结果异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 构建参数字符串，使用"|"分隔各字段
        /// </summary>
        /// <param name="result">识别结果</param>
        /// <returns>参数字符串</returns>
        private string BuildParameterString(RecognitionResult result)
        {
            // 构建CONTROL_APPLY_PARAMETER字段，使用"|"分隔
            var parameterFields = new[]
            {
                result.StorageQuantity.ToString(), // StoInQty
                result.FactoryNo,                  // Fact_No
                result.SerialId,                   // SER_ID
                result.ProductionLine,             // Prod_Line
                result.ProductionDate,             // Prod_Date
                result.LineCartonNo,               // Line_CartonNo
                result.BarCode,                    // BAR_CODE
                result.IdCode                      // IDCode
            };
            
            return string.Join("|", parameterFields);
        }

        /// <summary>
        /// 处理识别错误
        /// </summary>
        /// <param name="result">包含错误信息的识别结果</param>
        /// <returns>处理是否成功</returns>
        public async Task<bool> HandleRecognitionErrorAsync(RecognitionResult result)
        {
            try
            {
                _logger.LogWarning($"识别错误: 站位={result.StationNo}, 序列号={result.SerialId}, 错误码={result.ErrorCode}, 错误描述={result.ErrorDescription}");
                
                // 构建错误Apply对象
                var apply = new Apply
                {
                    DeviceCode = result.StationNo,
                    Stockbarcode = result.BarCode,
                    WarehouseCode = result.FactoryNo,
                    Para01 = result.LineCartonNo,
                    Para02 = result.ErrorCode,
                    Parameter = BuildParameterString(result),
                    Status = -1,  // 错误状态
                    Type = GetStationType(result.StationNo),
                    Remark = $"识别错误: {result.ErrorDescription}",
                    CreateTime = DateTime.Now.ToString("yyyyMMddHHmmss")
                };
                // 创建错误记录（在作用域内解析 Scoped 服务）
                using (var scope = _scopeFactory.CreateScope())
                {
                    var basicService = scope.ServiceProvider.GetRequiredService<IBasicService>();
                    var (id, failure) = await basicService.CreateApply(apply);
                    if (failure != null)
                    {
                        _logger.LogWarning($"创建Apply记录失败: {failure.ErrorMessage}");
                        return false;
                    }
                }

                if (false) // 占位保持后续结构（无意义分支仅为对齐原结构）
                {
                    //_logger.LogWarning($"创建错误Apply记录失败: {failure.ErrorMessage}");
                    return false;
                }

                _logger.LogInformation($"成功记录识别错误： 站位={result.StationNo}, 错误码={result.ErrorCode}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理识别错误异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据站位号确定类型
        /// </summary>
        /// <param name="stationNo">站位号</param>
        /// <returns>类型字符串</returns>
        private string GetStationType(string stationNo)
        {
            // 根据站位号确定类型
            return stationNo switch
            {
                "1111" or "1204" => "1", // 自动入库站
                "1101" or "1201" => "2", // 人工入库站
                _ => "0"
            };
        }
    }
}