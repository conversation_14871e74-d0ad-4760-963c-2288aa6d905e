{"theme": {"default": "<PERSON><PERSON><PERSON>", "colorful": "Colorful", "light": "Light", "dark": "Dark"}, "shared": {"notification": {"disconnected": "Cannot connect to server.", "server": "Internal server error.", "deadlock": "Server is busy, please try again.", "unauthorized": "No permission.", "timeout": "Session timeout.", "kickout": "You have been disconnected as someone has signed in with your ID in elsewhere.", "confirm": "Are you sure you want to continue?", "success": "The operation completed successfully.", "fail": "The operation failed.", "unknown": "Unknown Exception."}, "operation": {"add": "Add", "edit": "Edit", "delete": "Delete", "deletes": "Delete selected item(s)", "filter": "Filter", "dyeing": "Dyeing", "reset": "Reset", "reload": "Reload", "forward": "Forward", "back": "Back", "cancel": "Cancel", "personalize": "Personalize"}, "dialog": {"title": "Infomation", "edit": "Edit", "ok": "Ok", "cancel": "Cancel", "yes": "Yes", "no": "No", "close": "Close"}, "data": {"empty": "No records found", "selected": "{len} item(s) selected", "page": {"first": "First page", "previous": "Previous page", "next": "Next page", "last": "Last page", "size": "Item(s) per page:", "range": "{from} - {to} of {total} item(s)."}}}, "layouts": {"auth": {"login": {"language": "Language", "account": "Account", "password": "Password", "submit": "<PERSON><PERSON>", "fail": "Invalid account or password.", "frozen": "Account has been locked for security purposes."}, "expire": {"title": "Password Expired", "subtitle": "Please select a new password to continue.", "current": "Current Password", "replacement": "New Password", "confirm": "Congirm Password", "continue": "Continue", "success": "The operation completed successfully. Now you can login with new password."}, "initial": {"title": "Create Password", "subtitle": "The first time you login, please create a new password.", "replacement": "New Password", "confirm": "Congirm Password", "continue": "Continue", "success": "The operation completed successfully. Now you can login with new password."}}, "admin": {"fixed": "Fix menu", "folded": "Fold menu", "fullscreen": "Fullscreen", "fullscreen_exit": "Exit fullscreen", "language": "Language", "logout": "Logout", "theme": "Theme", "profile": "Profile", "password": {"title": "Modify Password", "current": "Current Password", "replacement": "New Password", "confirm": "Congirm Password", "success": "The operation completed successfully. Now you can login with new password."}, "message": {"tag": "Message", "title": "Site Messages", "empty": "No site message is received.", "more": "View More", "notify": "You have a new message.", "exception": "Cannot open message.", "back": "Back to list", "subject": "Subject", "source": "Source", "time": "Time", "flag": "Status", "read": "Read", "unread": "Unread", "markas": "<PERSON> as ", "receive": "Receive"}}}, "routes": {"basic": {"role": {"title": "Role", "name": "Name", "remark": "Remark", "menu": "Permission"}, "user": {"title": "User", "name": "Name", "account": "Account", "role": "Role", "password": "Reset password"}, "log": {"title": "Log", "time": "Time", "message": "Message", "request": "Request", "data": "Data", "ip": "IP Address", "operator": "Operator", "copy": "Copy content"}}, "visual": {"scene": {"title": "Visual", "empty": "Empty", "have": "Material", "pallet": "<PERSON><PERSON><PERSON>", "perspective": "Perspective", "orthographic": "Orthographic", "row": "Row {row}", "stock": "Stock", "record": "Record"}}, "material": {"code": "Code", "name": "Name", "cat": "Category", "group": "Group", "alias": "<PERSON><PERSON>", "model": "Model", "unit": "Unit", "brand": "Brand", "price": "Price", "weight": "Weight", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "facade": "Facade", "eff": "Effective period (days)", "exp": "Expiration period (days)", "qc": "QC", "mintime": "Minimum storage period (days)", "maxtime": "Maximum storage period (days)", "batch": "Batch No", "bill": "Bill No", "supplier": "Supplier", "mfg": "MFG", "qs": "QS", "qty": "Quantity", "enabled": "State", "remark": "Remark", "enum": {"qc": {"true": "Yes", "false": "No", "null": ""}, "qs": {"na": "NA", "ok": "OK", "ng": "NG", "null": ""}, "enabled": {"true": "Enabled", "false": "Disabled", "null": ""}}, "main": {"title": "Main"}, "category": {"title": "Category", "parent": "Parent", "code": "Code", "name": "Name"}, "safety": {"title": "Safety", "lower": "Lower limit", "upper": "Upper limit", "warehouse": "Warehouse", "error": {"material": "Material does not exist."}}}, "stock": {"inbound": {"title": "No-order", "name": "Inbound", "target": "Destination", "area": "Area", "barcode": "Barcode", "add": "Add stock line", "quantity": "Inbound quantity", "reset": "Reset", "empty": "Please add stock line", "remaining": "Remaining", "planned": "Planned", "executing": "Executing", "finished": "Finished", "back": "Back", "error": {"material": "Material not exists.", "warehouse": "Container in another warehouse."}}, "outbound": {"title": "No-order", "name": "Outbound", "warehouse": "Warehouse", "area": "Area", "barcode": "Barcode", "cell": "Cell", "total": "Total", "locked": "Locked", "task": "Outfeed", "target": "Outbound target", "quantity": "Quantity", "station": "Station", "priority": "Prior", "available": "Ready", "unavailable": "Untouchable", "remaining": "Remaining", "back": "Back"}, "picking": {"title": "Confirmation", "name": "Picking", "barcode": "Barcode", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "lock": "Locked", "confirm": "Confirm", "cancel": "Cancel", "quantity": "Confirmed quantity"}, "pkpicking": {"title": "出库确认", "name": "拣选", "barcode": "载具条码", "warehouse": "仓库", "area": "库区", "cell": "货位", "lock": "锁定信息", "confirm": "确认", "cancel": "取消", "quantity": "确认数量"}, "combine": {"title": "Combine", "name": "Combine", "barcode": "Barcode", "total": "Total", "locked": "Locked", "quantity": "Combine quantity", "target": "Destination", "area": "Area", "error": {"warehouse": "Container in another warehouse."}}, "palletize": {"title": "Palletization", "name": "<PERSON><PERSON><PERSON><PERSON>", "warehouse": "Warehouse", "area": "Area", "barcode": "Barcode", "cell": "Cell", "total": "Total", "locked": "Locked", "create": "Created", "update": "Updated", "quantity": "Palletized quantity", "target": "Destination", "error": {"warehouse": "Container in another warehouse.", "warehouse2": "Cannot operate items from different warehouses."}}, "depalletize": {"title": "Depalletizion", "name": "Depalletizion", "barcode": "Barcode", "total": "Total", "locked": "Locked", "quantity": "Depalletized quantity", "target": "Destination"}, "block": {"title": "Block", "auto": "Auto", "overdue": "Overdue", "day": "{value}d ", "hour": "{value}h ", "moment": "Less than 1h", "barcode": "Barcode", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "create": "Created", "update": "Updated", "locked": "Locked quantity：{locked}", "setting": "Set selected item(s)", "disabled": "Disabled", "info": "Settings", "result": "Operation completed. Succeed: {succeed}; Failed: {failed}."}, "inventory": {"title": "No-plan", "name": "Inventory", "warehouse": "Warehouse", "area": "Area", "barcode": "Barcode", "cell": "Cell", "locked": "Locked quantity：{locked}", "task": "Outfeed", "target": "Inventory target", "quantity": "Quantity", "lock": "If quantity is less than locked ({locked}), unlock it first.", "station": "Station", "priority": "Prior", "available": "Ready", "unavailable": "Untouchable"}}, "task": {"id": "ID", "barcode": "Barcode", "warehouse": "Warehouse", "area": "Area", "original": "Original", "destination": "Destination", "timestamp": "Timestamp", "operator": "Operator", "tag": "Tag", "message": "Remark", "infeed": "Infeed", "outfeed": "Outfeed", "transfer": "Transfer", "convey": "<PERSON><PERSON>", "priority": "Priority", "station": "Station", "target": "Destination", "main": {"title": "Task", "manual": "Manual", "delete": "Delete", "cancel": "Cancel task", "complete": "Complete task", "priority": "Prior", "unknown": "Unknown reason", "follow": "Execute after #{previous} completes", "state": {"created": "Waiting", "waiting": "Waiting", "running": "Running", "blocked": "Blocked"}, "error": {"stock": "Stock not exists"}}, "pallet": {"title": "<PERSON><PERSON><PERSON>", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "barcode": "Barcode", "feature": "Feature", "outfeed": "Outfeed", "batch": "<PERSON><PERSON>", "quantity": "Quantity", "station": "Station", "priority": "Prior", "result": "Operation completed. Succeed: {succeed}.", "result2": "Operation completed. Succeed: {succeed}; Failed: {failed}.", "error": {"warehouse": "Cannot operate items from different warehouses"}}, "disablement": {"title": "Disablement", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "time": "Duration", "day": "{value}d ", "hour": "{value}h ", "moment": "Less than 1h", "remark": "Remark", "enum": "Enum", "range": "Range", "xmin": "Min column", "xmax": "Max column", "ymin": "Min layer", "ymax": "Max layer", "zmin": "Min row", "zmax": "Max row", "result": "Operation completed. Succeed: {succeed}; Failed: {failed}.", "result2": "Operation completed. Succeed: {succeed}; Failed: {failed}; Invalid: {invalid}.", "error": {"warehouse": "Cannot operate items from different warehouses"}}, "trigger": {"title": "<PERSON><PERSON>", "timestamp": "Timestamp", "type": "Type", "warehouse": "Warehouse", "device": "<PERSON><PERSON>", "param": "Parameter", "remark": "Remark", "executed": "Exec {count} Time(s). ", "fail": "Fail: ", "result": "Exec Result", "results": {"null": "Executing", "success": "Success", "fail": "Fail", "fallback": "Fallback", "timeout": "Timeout", "break": "Break"}, "cancel": "Cancel", "retry": "Retry"}}, "device": {"types": {"stacker": {"name": "<PERSON><PERSON><PERSON>", "instructions": {"home": "Return to origin", "prePickup": "To pickup", "preSend": "To send", "pickup": "Pickup", "send": "Send", "pickupAndSend": "Pickup and send", "move": "Move"}, "state": {"state": "Flag", "instruction": "Instruction", "xCoord": "X-Coordinate", "yCoord": "Y-Coordinate", "zCoord": "Z-Coordinate", "sensor": "Sensor"}, "error": {"30": "Manual mode", "31": "Emergency stop", "32": "Move servo / inverter failure", "33": "Lift servo / inverter failure", "34": "Fork servo / inverter failure", "35": "Nothing in near-fork after picking", "36": "Something in near-fork shelf when sending", "37": "Something in near-fork after sending", "38": "Something in near-fork when picking", "39": "Nothing in near-fork when sending", "40": "Source address error", "41": "Target address error", "42": "High cargo with low delivery", "43": "Over height", "44": "Over width", "45": "Over bounds", "46": "Fork not centered", "47": "Left fork detection signal lost", "48": "Right fork detection signal lost", "49": "Left fork in-place signal lost", "50": "Right fork in-place signal lost", "51": "Fork operation timeout", "52": "Fork pickup timeout when picking", "53": "Fork send timeout when sending", "54": "Overloaded", "55": "Loose rope", "56": "Stop at upper limit", "57": "Stop at lower limit", "58": "Stop at front limit", "59": "Stop at back limit", "60": "Target address not set", "61": "Move range error", "62": "Lift range error", "63": "Move stop overrun", "64": "Lift stop overrun", "65": "Lift lose speed", "66": "Ranging value unchanged when lifting", "67": "Ranging value unchanged when moving", "68": "Lift target overrun", "69": "Move target overrun", "70": "Lift speed anomaly", "71": "Move speed anomaly", "72": "Lift not within normal range", "73": "Move not within normal range", "74": "Optical communication disconnect", "75": "Station not ready when picking", "76": "Station not ready when sending", "77": "Braking resistor overheated", "78": "Switching mode when running", "79": "Move validation error", "80": "Lift validation error", "81": "Nothing in mid-fork after picking", "82": "Something in mid-fork shelf when sending", "83": "Something in mid-fork after sending", "84": "Something in mid-fork when picking", "85": "Nothing in mid-fork when sending", "86": "Nothing in far-fork after picking", "87": "Something in far-fork shelf when sending", "88": "Something in far-fork after sending", "89": "Something in far-fork when picking", "90": "Nothing in far-fork when sending", "91": "Lift counter error", "92": "Move counter error", "93": "Toggle fork timeout", "94": "Toggle fork not stop in correct position", "95": "Something in near-side when picking far-side", "96": "Something in near-side when sending far-side", "97": "Container type error when picking", "98": "Container type error when sending", "99": "Mid-fork servo / inverter error", "100": "Far-fork servo / inverter error", "101": "Emergency button pressed", "102": "Safety-door openned", "103": "Fork panning inverter alarm", "104": "Shutter-door not in high position", "105": "Shutter-door operation timeout", "106": "Shutter-door inverter error", "107": "Right overbounds on left forkback", "108": "Left overbounds on right forkback"}}, "conveyor": {"name": "Conveyor", "instructions": {"forward": "Forward", "reverse": "Reverse", "send": "Send"}, "state": {"state": "Flag", "instruction": "Instruction", "sensor": "Sensor", "action": "Action"}, "error": {"30": "Manual mode", "31": "Emergency stop", "32": "Inverter alarm", "33": "Thermal relay alarm", "34": "Drum motor alarm", "35": "Transport timeout", "36": "Jacking timeout", "37": "Jacking high & low level error", "38": "Upper computer command error", "39": "Contour detection over height", "40": "Contour detection over width (left)", "41": "Contour detection over width (right)", "42": "Contour detection over length (front)", "43": "Contour detection over length (back)", "44": "Overweight ", "45": "Out of bounds", "46": "Safety-door openned", "47": "Rotary position error", "48": "Jacking upper limit alarm", "49": "Jacking lower limit alarm", "60": "Lift inverter alarm", "61": "Transfer inverter alarm", "62": "Upper limit alarm", "63": "Lower limit alarm", "64": "Transfer timeout", "65": "Lift timeout", "66": "Lift position anomaly", "67": "Out of bounds", "68": "Lift speed anomaly", "69": "Code not update when lifting", "70": "Left weight alarm", "71": "Right weight alarm", "72": "Left chain break alarm", "73": "Right chain break alarm", "74": "Lift lose speed", "75": "Conveying direction out of bounds", "90": "Palletizer lift inverter alarm", "91": "Palletizer side pusher motor overheat", "92": "Palletizer upper limit alarm", "93": "Palle<PERSON>zer lower limit alarm", "94": "Palletizer side push not in position", "95": "<PERSON><PERSON><PERSON><PERSON> fork not in position", "96": "Pa<PERSON><PERSON>zer lift position abnormal", "97": "<PERSON><PERSON><PERSON><PERSON> over height", "98": "<PERSON><PERSON><PERSON><PERSON> lift timeout", "99": "Palletizer fork timeout", "100": "Palletizer side pusher timeout", "101": "Palletizer lift thermal relay alarm", "102": "Palletizer fork thermal relay alarm", "103": "Palle<PERSON>zer blocker position error"}}, "jacking": {"name": "Jacking"}, "elevator": {"name": "Elevator"}, "scanner": {"name": "Scanner", "instructions": {"write": "Write"}}}, "monitor": {"title": "Monitor", "search": "Search device", "notfound": "Device not found.", "overview": "Overview", "scopes": {"default": "Default scope"}, "layers": {"1f": "1st layer", "2f": "2nd layer", "3f": "3rd layer"}, "states": {"locked": "Idle", "idle": "Idle", "running": "Running", "finished": "Finishing", "error": "Error", "offline": "Offline", "disabled": "Disabled"}, "device": "<PERSON><PERSON>", "load": "Loaded", "state": "State", "error": "Error", "instruction": "Instruction", "number": "No.", "action": "Action", "forward": "Forward", "barcode": "Barcode", "original": "Original", "destination": "Destination", "task": "Task", "start": "Start Time", "end": "End Time", "duration": "Duration", "hour": "{value}h ", "minute": "{value}min ", "second": "{value}s ", "active": "Active Items", "history": "History Items", "refresh": "Refresh", "reset": "Reset", "ack": "Ack", "enable": "Enable", "disable": "Disable device", "manual": "Manual instruction", "more": "More"}, "instruction": {"title": "Instruction", "task": "Task No.", "number": "Instruction No.", "device": "<PERSON><PERSON>", "type": "Type", "forward": "Target", "priority": "Priority", "barcode": "Barcode", "original": "Original", "destination": "Destination", "generated": "Generated Time", "started": "Started Time", "completed": "Completed Time", "custom": "Custom", "create": "Add", "send": "New Instruction", "resend": "Resend instruction", "delete": "Delete instruction", "deletes": "Delete instruction group", "complete": "Complete instruction", "completes": "Complete instruction group", "confirm": {"delete": "{percent}% of the task has been progressed, is the deletion confirmed?", "complete": "Only {percent}% of the task has been progressed, is the completion confirmed?"}, "state": {"created": "Created", "ready": "Ready", "sent": "<PERSON><PERSON>", "running": "Running", "error": "Error", "complete": "Completed"}}}, "order": {"number": "Order No.", "type": "Type", "creater": "Creater", "created": "Created", "remark": "Remark", "line": "Lines", "executing": "Executing", "finished": "Finished", "create": "created in", "back": "Back", "add": "Add order line", "empty": "Please add order line", "action": "Actions", "actions": {"start": "Start", "edit": "Edit", "cancel": "Cancel", "complete": "Complete", "inbound": "Inbound", "outbound": "Outbound"}, "types": {"inbound": "Inbound order", "outbound": "Outbound order"}, "title": {"inbound": "Order", "outbound": "Order"}, "error": {"material": "Material does not exist."}}, "query": {"stock": {"title": "Stock", "warehouse": "Warehouse", "area": "Area", "barcode": "Barcode", "empty": "Empty Pallet", "cell": "Cell", "locked": "Locked", "unlocked": "Unlocked", "overdue": "Overdue", "day": "{value}d ", "hour": "{value}h ", "moment": "Less than 1h", "disabled": "Disabled", "create": "Created", "update": "Updated", "collapse": "Collapse to pallets", "expand": "Expand stock lines", "back": "Back", "export": "Export", "exporting": "Exporting...please wait patiently."}, "record": {"title": "<PERSON><PERSON><PERSON>", "trans": "Transaction", "barcode": "Barcode", "empty": "Empty Pallet", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "original": "Original", "destination": "Destination", "begin": "<PERSON><PERSON>", "end": "End", "operator": "Operator", "tag": "Tag", "remark": "Remark", "inbound": "Inbound", "outbound": "Outbound", "update": "Update", "infeed": "Infeed", "outfeed": "Outfeed", "transfer": "Transfer", "convey": "<PERSON><PERSON>", "increase": "Stock increase", "decrease": "Stock decrease", "collapse": "Collapse to pallets", "expand": "Expand stock lines", "back": "Back", "export": "Export", "exporting": "Exporting...please wait patiently."}, "blocking": {"title": "Blocking", "mode": "Mode", "auto": "Auto", "force": "Forced", "overdue": "Overdue", "day": "{value}d ", "hour": "{value}h ", "moment": "Less than 1h", "barcode": "Barcode", "warehouse": "Warehouse", "area": "Area", "cell": "Cell", "create": "Created", "update": "Updated", "export": "Export", "exporting": "Exporting...please wait patiently."}, "overdue": {"title": "Overdue", "area": "Area", "barcode": "Barcode", "warehouse": "Warehouse", "cell": "Cell", "create": "Created", "update": "Updated", "overdue": "Overdue", "day": "{value}d ", "hour": "{value}h ", "moment": "Less than 1h", "export": "Export", "exporting": "Exporting...please wait patiently."}, "safety": {"title": "Overflow", "warehouse": "Warehouse", "range": "Safety", "type": "Warning", "shortage": "Shortage", "excess": "Excess", "export": "Export", "exporting": "Exporting...please wait patiently."}, "order": {"title": "Order", "number": "Order No.", "type": "Type", "creater": "Creater", "created": "Created", "final": "Archived", "remark": "Remark", "finished": "Finished", "complete": "Completed", "cancel": "Cancelled", "expand": "Expand order lines", "back": "Back", "export": "Export", "exporting": "Exporting...please wait patiently."}, "interface": {"title": "Interface", "scope": "System", "type": "Type", "types": {"input": "Input", "output": "Output"}, "timestamp": "Time", "function": "Event", "message": "Message", "result": "Result", "results": {"true": "Success", "false": "Fail"}, "remark": "Remark", "retry": "Retry {index} .", "logged": "Logged", "copy": "Copy content", "export": "Export", "exporting": "Exporting...please wait patiently."}}}, "navi": {"material": "Material", "inbound": "Inbound", "stock": "Inventory", "palletize": "Palletization", "inventory": "Stocktaking", "outbound": "Outbound", "asrs": "AS/RS", "dataview": "Query", "workflow": "Workflow", "setting": "System"}, "server": {"00001": "Key cannot be null.", "00002": "Parameter is not operable.", "00003": "Incorrect value.", "01001": "Token cannot be null.", "01002": "Connection ID cannot be null.", "01003": "Account cannot be null.", "01004": "Password cannot be null.", "01005": "Incorrect password format.", "01006": "Password is initialized.", "01007": "Content too large.", "01008": "Current password cannot be null.", "01009": "Current password is incorrect.", "01010": "New password cannot be null.", "01011": "Incorrect password format.", "01012": "URL cannot be null.", "01013": "Password initialization policy causes navigation abort.", "01014": "Password expiration policy causes navigation abort.", "01015": "No URL access.", "02001": "Token cannot be null.", "02002": "Connection ID cannot be null.", "02003": "Subject cannot be null.", "02004": "Target cannot be null.", "02005": "Unable to send message.", "02006": "ID is invalid.", "03001": "ID cannot be null.", "03002": "Name cannot be null.", "03003": "Name already exists.", "03004": "Role does not exist.", "03005": "Role cannot be null.", "03006": "Account cannot be null.", "03007": "Name already exists.", "03008": "Account already exists.", "03009": "User does not exist.", "04001": "<PERSON><PERSON> cannot be null.", "04002": "Function cannot be null.", "04003": "ID cannot be null.", "04004": "Invoking WCS interface fail.", "05001": "Name cannot be null.", "05002": "Parent does not exist.", "05003": "Name already exists.", "05004": "ID cannot be null.", "05005": "Parent cannot set to itself.", "05006": "Category does not exist.", "05007": "Code cannot be null.", "05008": "Code already exists.", "05009": "Name already exists.", "05010": "Material does not exist.", "05011": "Material cannot be null.", "05012": "Warehouse cannot be null.", "05013": "Limit cannot be null.", "05014": "Upper limit must greater than lower limit.", "05015": "Material & warehouse already exists.", "05016": "Safety does not exist.", "06001": "Stock line cannot be null.", "06002": "Inbound quantity must be positive.", "06003": "Outbound quantity must be negative.", "06004": "Some stock lines donot have ID.", "06005": "Cannot find stock line.", "06006": "Outbound quantity out of range.", "06007": "Outbound quantity out of available range.", "06008": "Incorrect barcode format.", "06009": "Container operation is busy, please try again.", "06010": "Container does not exist.", "06011": "Container has task.", "06012": "Destination is incorrect.", "06013": "Destination unavailable.", "06014": "Destination not specified.", "06015": "Some stock lines have incorrect remarks.", "06016": "Property not found.", "06017": "Lock quantity must be positive.", "06018": "Lock quantity is greater than available quantity.", "06019": "Unlock quantity is greater than locked quantity.", "07001": "Warehouse cannot be null.", "07002": "Barcode cannot be null.", "07003": "Original cannot be null.", "07004": "Destination cannot be null.", "07005": "Incorrect barcode format.", "07006": "Container operation is busy, please try again.", "07007": "Container has task.", "07008": "Cannot operate container in ASRS.", "07009": "Original is invalid.", "07010": "Original is not in current warehouse.", "07011": "Original is not available.", "07012": "Original cannot be used as starting point.", "07013": "Original does not match carrier type.", "07014": "Original does not match carrier model.", "07015": "Original has outfeed task.", "07016": "Destination is invalid.", "07017": "Destination is not in current warehouse.", "07018": "Destination is not free.", "07019": "Destination is not available.", "07020": "Destination cannot be used as ending point.", "07021": "Destination does not match carrier type.", "07022": "Destination does not match carrier model.", "07023": "Destination has infeed task.", "07024": "No available route between original and destination.", "07025": "Existence of opposing task.", "07026": "No destination specified.", "07027": "No free cells.", "07028": "Container does not exist.", "07029": "ID cannot be null.", "07030": "Cell cannot be null.", "07031": "Behavior cannot be null.", "07032": "Unable to generate avoidance tasks.", "07033": "Unknown avoidance behavior.", "07034": "Task does not exist.", "07035": "Priority cannot be null.", "07036": "Task is not allowed to be prioritized.", "07037": "Warehouse cannot be null.", "07038": "Cell cannot be null.", "07039": "WCS refused.", "07040": "Refuse to operate.", "08001": "ID cannot be null.", "08002": "Number cannot be null.", "08003": "Type cannot be null.", "08004": "Order line cannot be null.", "08005": "Increment cannot be null.", "08006": "Order does not exist.", "08007": "Type does not exist.", "08008": "Order line does not exist.", "08009": "Number already exists.", "08010": "Failure to generate Number.", "08011": "Executing order lines are not allowed to modify.", "08012": "Executing order lines are not allowed to delete.", "09001": "Type cannot be null.", "09002": "Device cannot be null.", "09003": "No available route.", "09004": "No instruction generated.", "09005": "Device is not enabled.", "09006": "Device is not idle.", "09007": "<PERSON><PERSON> is booked.", "09008": "<PERSON><PERSON> state invalid.", "09009": "opposing instruction conflict.", "09010": "Failed to write data.", "09011": "Failed to read data.", "09012": "Type cannot be null.", "09013": "Device does not exist.", "09014": "Forward does not exist.", "09015": "Instruction does not exist.", "09016": "Instruction state invalid.", "09017": "ID cannot be null.", "09018": "Task cannot be null."}}