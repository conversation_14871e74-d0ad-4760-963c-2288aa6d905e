{"theme": {"default": "默认", "colorful": "彩色", "light": "浅色", "dark": "深色"}, "shared": {"notification": {"disconnected": "无法连接到服务。", "server": "服务器内部错误。", "deadlock": "服务器繁忙，请重试。", "unauthorized": "您没有当前资源的操作权限。", "timeout": "会话过期，请重新登陆。", "kickout": "您的帐号在其它地方登录，您已被迫下线。", "confirm": "是否确定要执行此操作？", "success": "操作成功。", "fail": "操作失败。", "print": {"failure": "无法连接到打印机，请检查打印机状态"}, "unknown": "未知错误。", "tasknofirst": "请先输入单号。", "notfound": "未找到符合条件的库存。", "pknochoose": "未选中平置仓库存", "noprinter": "未找到打印机", "lknochoose": "未选中立库库存", "failedwithsameposition": "所选区间不合法【起始位置与终止位置重合】，请重新选择区间。"}, "operation": {"hotdegree": "配置货位热度区间", "printerconfirm": "打印机确认", "printerIp": "当前选中的打印机", "confirm": "确定", "add": "新建", "edit": "编辑", "print": "打印", "delete": "删除", "deletes": "删除选中项", "filter": "筛选", "filterrules": {"button": "按条件筛选库存", "title": "筛选条件"}, "dyeing": "着色", "reset": "重置", "reload": "重新加载", "forward": "前进", "back": "后退", "cancel": "取消", "personalize": "个性化"}, "dialog": {"title": "消息", "edit": "编辑", "ok": "确定", "cancel": "取消", "yes": "是", "no": "否", "close": "关闭", "reset": "重置"}, "data": {"empty": "没有记录", "selected": "{len} 条记录被选中", "page": {"first": "第一页", "previous": "上一页", "next": "下一页", "last": "最后一页", "size": "每页记录：", "range": "第 {from} - {to} 条记录，共 {total} 条。"}}}, "layouts": {"auth": {"login": {"language": "语言", "account": "账号", "password": "密码", "submit": "登录", "fail": "账号不存在或密码错误。", "frozen": "账号已被冻结，请与管理员联系。"}, "expire": {"title": "密码已过期", "subtitle": "您的密码已过期。请确认您的当前密码，然后设置新的密码。", "current": "当前密码", "replacement": "新密码", "confirm": "确认新密码", "continue": "继续", "success": "操作成功。请使用新的密码重新登录。"}, "initial": {"title": "创建新密码", "subtitle": "您首次登录系统，请创建一个新的密码。", "replacement": "新密码", "confirm": "确认新密码", "continue": "继续", "success": "操作成功。请使用新的密码重新登录。"}}, "admin": {"fixed": "固定菜单", "folded": "折叠菜单", "fullscreen": "全屏", "fullscreen_exit": "退出全屏", "language": "语言", "logout": "注销", "theme": "主题", "profile": "设置", "password": {"title": "修改密码", "current": "当前密码", "replacement": "新密码", "confirm": "确认新密码", "success": "操作成功。请使用新的密码重新登录。"}, "message": {"tag": "消息", "title": "站内消息通知", "empty": "您暂时没有站内消息", "more": "查看更多", "notify": "您有一条新消息。", "exception": "无法打开消息。", "back": "返回列表", "subject": "主题内容", "source": "消息源", "time": "时间", "flag": "状态", "read": "已读", "unread": "未读", "markas": "标记为", "receive": "接收"}}}, "routes": {"basic": {"role": {"title": "角色管理", "name": "角色名", "remark": "备注", "menu": "权限"}, "user": {"title": "用户管理", "name": "用户名", "account": "账号", "role": "角色", "password": "重置密码"}, "log": {"title": "用户日志", "time": "时间", "message": "操作内容", "request": "交互信息", "data": "交互数据", "ip": "IP 地址", "operator": "操作者", "copy": "复制内容"}, "warehouse": {"title": "库房维护", "code": "库房编码", "name": "库房名称", "remark": "备注"}, "area": {"title": "库区维护", "code": "库区编码", "name": "库区名称", "warehousename": "绑定库房名称", "remark": "备注"}, "clientinfo": {"title": "客户资料维护", "code": "客户代号", "name": "客户名称", "shortname": "客户简称", "clientcate": "客户类别", "begindatecount": "允收起始天数", "finaldatecount": "允收截止天数", "model": "先/后进先出"}, "clientcategory": {"title": "客户类别维护", "code": "客户类别", "name": "类别名称", "begindatecount": "允收起始天数", "finaldatecount": "允收截止天数", "model": "先/后进先出"}, "moondoor": {"title": "月台信息维护", "code": "月台编码", "name": "月台名称", "status": "月台状态", "type": "车型"}, "operation": {"title": "操作人员操作记录", "operator": "操作者", "station": "工作站", "time": "操作异动时间", "opprocess": "操作程序", "opfunction": "操作功能", "opmessage": "操作记录", "IO": "I/O", "orderseq": "命令序号"}}, "visual": {"scene": {"title": "可视化", "hotdegree": "热度", "hotdegreetochoose": "请选择货位热度", "empty": "空", "have": "有货", "pallet": "空容器", "perspective": "透视视图", "orthographic": "正交视图", "row": "第 {row} 排", "stock": "库存", "full": "满托", "record": "历史"}}, "material": {"code": "料号", "name": "名称", "cat": "品类", "group": "物料组", "alias": "别名", "model": "规格型号", "unit": "计量单位", "brand": "品牌", "manageId": "管理任务ID", "plantCode": "plantCode", "manageListREPOConfirm": "仓库确认", "manageListPRODConfirm": "生产确认", "manageListFlag": "manageListFlag", "goodsQCState": "QC状态", "manageListREPOConfirmTime": "仓库确认时间", "manageListPRODConfirmTime": "生产确认时间", "adjustQuantity": "调整数量", "manageListQuantity": "管理任务详情数量", "adjustReason": "调整原因", "clientCode": "客户编码", "effectivePeriod": "起效时间", "expirationPeriod": "失效时间", "financialPostLatestTime": "最后上报时间", "financialPostQuantity": "最后上报数量", "financialPostStatus": "SAP过账状态", "financialPostTime": "发送时间", "financialPostUploadTime": "上报时间", "importTime": "导入时间", "productionLine": "生产线编码", "sapNo": "SAP序号", "transferType": "交货类型", "truckInfo": "车辆信息", "warehouseCode": "库房编码", "workGroup": "班别", "price": "价格", "weight": "重量", "length": "长度", "width": "宽度", "height": "高度", "facade": "外观特性", "eff": "起效期（天）", "exp": "失效期（天）", "qc": "质检要求", "mintime": "最小库存时间（天）", "maxtime": "最大库存时间（天）", "lowerlimit": "最小安全库存数", "upperlimit": "最大安全库存数", "palletQuantity": "满盘数", "batch": "批次号", "bill": "运单号", "supplier": "供应商", "mfg": "生产日期", "qs": "质量状态", "qty": "数量", "enabled": "库存状态", "remark": "备注", "enum": {"qc": {"true": "是", "false": "否", "null": ""}, "qs": {"0": "待检", "1": "合格", "2": "管控", "null": ""}, "enabled": {"true": "可用", "false": "不可用", "null": ""}}, "main": {"title": "主数据"}, "category": {"title": "品类", "parent": "父类", "code": "编码", "name": "品类名"}, "safety": {"title": "安全库存", "lower": "库存下限", "upper": "库存上限", "warehouse": "库房", "error": {"material": "物料不存在。"}}}, "stock": {"inbound": {"title": "无订单入库", "name": "入库", "target": "入库目标", "original": "入库起点", "area": "库区", "barcode": "载具条码", "quantity": "入库数量", "quantityE": "输入数量非法，请重新输入", "add": "添加库存行", "reset": "重置", "empty": "请添加库存行", "emptytodo": "请通过计划单号进行搜索", "remaining": "待办数量", "planned": "计划量", "executing": "执行中", "finished": "已完成", "back": "返回", "error": {"material": "物料不存在。", "warehouse": "载具已存在于另一个库房。"}}, "todooutbound": {"title": {"placeholder": "请输入20/40/99单单号", "half": "零头出库", "full": "整托出库", "ignoreNullStorage": "省略无订单库存", "overFullPallet": ">=满板量", "recieveTime": "指定允收天数", "platform": "月台", "truck": "车号", "crane": "指定出库吊车"}, "function": {"count": "输入数量", "prepare": "备货", "batchprepare": "波次备货", "platform": {"title": "选定月台", "platformbind": "月台", "truckbind": "车号"}, "outbound": "出库"}, "notify": {"chooseorderfirst": "请先选择订单行"}, "sheet": {"planlist": "挑选出库单项目", "storagelist": "预约出库设定"}}, "outbound": {"title": "无订单出库", "hebing": "99合单", "devided": "拆单", "transfer": "人工单转系统单", "batch": "选定库存", "distribute": "智能分配数量", "name": "出库", "warehouse": "仓库", "area": "库区", "barcode": "载具条码", "cell": "货位", "total": "库存量", "locked": "锁定", "task": "下架", "target": "出库目标", "quantity": "数量", "station": "站台", "priority": "优先", "available": "可操作位置", "unavailable": "货架内", "remaining": "待办数量", "taskauto": "自动下架", "back": "返回"}, "pkpick": {"title": "零头区出库", "name": "拣选", "barcode": "载具条码", "material": "物料标签", "warehouse": "仓库", "area": "库区", "cell": "货位", "lock": "锁定信息", "confirm": "确认", "cancel": "取消", "quantity": "确认数量", "lockedquantity": "拣选数量"}, "picking": {"title": "出库确认", "name": "拣选", "barcode": "载具条码", "warehouse": "仓库", "area": "库区", "cell": "货位", "lock": "锁定信息", "confirm": "确认", "cancel": "取消", "quantity": "确认数量"}, "pkpicking": {"title": "出库确认", "name": "拣选", "barcode": "载具条码", "warehouse": "仓库", "area": "库区", "cell": "货位", "lock": "锁定信息", "confirm": "确认", "cancel": "取消", "plancode": "计划单号", "quantity": "确认数量"}, "combine": {"title": "调整", "name": "调整", "barcode": "载具条码", "total": "库存量", "locked": "锁定", "quantity": "调整数量", "target": "调整目标", "area": "库区", "error": {"warehouse": "载具已存在于另一个库房。"}}, "palletize": {"title": "组盘", "name": "组盘", "warehouse": "仓库", "area": "库区", "barcode": "载具条码", "cell": "货位", "total": "库存量", "locked": "锁定", "create": "创建时间", "update": "更新时间", "quantity": "组盘数量", "target": "组盘目标", "error": {"warehouse": "载具已存在于另一个库房。", "warehouse2": "不允许批量操作不同仓库的项目。"}}, "depalletize": {"title": "解盘", "name": "解盘", "barcode": "载具条码", "total": "库存量", "locked": "锁定", "quantity": "解盘数量", "target": "解盘目标"}, "block": {"title": "冻结管理", "auto": "自动", "overdue": "过期时间", "day": "{value}天", "hour": "{value}小时", "moment": "小于1小时", "barcode": "载具条码", "warehouse": "仓库", "area": "库区", "cell": "货位", "create": "创建时间", "update": "更新时间", "locked": "锁定库存量：{locked}", "setting": "设置选中项", "disabled": "不可用", "info": "设置信息", "result": "操作完成：成功 {succeed} 个；失败 {failed} 个。"}, "inventory": {"title": "无计划盘点", "name": "盘点", "warehouse": "仓库", "area": "库区", "barcode": "载具条码", "cell": "货位", "locked": "锁定库存量：{locked}", "task": "下架", "target": "盘点目标", "quantity": "实际数量", "lock": "若实际数量低于锁定数({locked})，请先解锁。", "station": "下架站台", "priority": "优先", "available": "可操作位置", "unavailable": "货架内"}}, "task": {"id": "任务号", "barcode": "栈板条码", "plancode": "任务单号", "warehouse": "仓库", "area": "库区", "original": "起始位置", "destination": "目标位置", "timestamp": "创建时间", "operator": "操作者", "tag": "标签", "message": "备注", "infeed": "上架", "outfeed": "下架", "transfer": "移库", "convey": "输送", "priority": "优先级", "station": "站台", "target": "目标货位", "main": {"title": "任务队列", "manual": "手动", "delete": "删除", "cancel": "取消任务", "complete": "完成任务", "priority": "优先", "unknown": "未知原因", "follow": "在任务 {previous} 完成后执行", "state": {"created": "等待", "waiting": "等待", "running": "运行", "blocked": "故障"}, "error": {"stock": "库存不存在"}}, "list": {"title": "任务详情", "manual": "手动", "delete": "删除", "cancel": "取消任务", "complete": "完成任务", "priority": "优先", "unknown": "未知原因", "follow": "在任务 {previous} 完成后执行", "state": {"waiting": "等待", "running": "运行", "blocked": "故障"}, "error": {"stock": "库存不存在"}}, "pallet": {"title": "空载具下架", "warehouse": "仓库", "area": "库区", "cell": "货位", "barcode": "载具条码", "feature": "载具特征", "outfeed": "下架", "batch": "批量下架", "quantity": "需求数量", "station": "站台", "priority": "优先", "result": "操作完成：成功 {succeed} 个。", "result2": "操作完成：成功 {succeed} 个；失败 {failed}。", "error": {"warehouse": "不允许批量操作不同仓库的项目"}}, "disablement": {"title": "禁用货位", "warehouse": "仓库", "area": "库区", "cell": "货位", "time": "禁用时长", "day": "{value}天", "hour": "{value}小时", "moment": "小于1小时", "remark": "备注", "enum": "枚举", "range": "范围", "xmin": "起始列", "xmax": "终止列", "ymin": "起始层", "ymax": "终止层", "zmin": "起始排", "zmax": "终止排", "result": "操作完成：成功 {succeed} 个；失败 {failed} 个。", "result2": "操作完成：成功 {succeed} 个；失败 {failed} 个；无效项 {invalid} 个。", "error": {"warehouse": "不允许批量操作不同仓库的项目"}}, "trigger": {"title": "WCS设备触发维护", "timestamp": "触发时间", "type": "类型", "warehouse": "库房", "device": "设备", "param": "参数", "remark": "备注", "executed": "执行 {count} 次。", "fail": "失败：", "result": "运行结果", "results": {"null": "运行中", "success": "成功", "fail": "失败", "fallback": "降级", "timeout": "超时", "break": "中断"}, "cancel": "取消", "retry": "重试"}}, "device": {"types": {"stacker": {"name": "堆垛机", "instructions": {"home": "回原点", "prePickup": "将取", "preSend": "将送", "pickup": "取货", "send": "送货", "pickupAndSend": "取送货", "move": "移动"}, "state": {"state": "标志", "instruction": "指令", "xCoord": "X 坐标", "yCoord": "Y 坐标", "zCoord": "Z 坐标", "sensor": "开关"}, "error": {"30": "手动", "31": "急停", "32": "行走伺服或变频器故障", "33": "升降伺服或变频器故障", "34": "货叉伺服或变频器故障", "35": "取货后堆垛机近叉无货", "36": "放货时近叉货架有货", "37": "放货后堆垛机近叉有货", "38": "取货时堆垛机近叉有货", "39": "放货时堆垛机近叉无货", "40": "源地址错误", "41": "目地址错误", "42": "高货低送报警", "43": "货物超高", "44": "货物超宽", "45": "货物超界", "46": "叉不居中", "47": "货叉左侧运行时检测信号丢失", "48": "货叉右侧运行时检测信号丢失", "49": "货叉左到位丢失", "50": "货叉右到位丢失", "51": "货叉运行超时", "52": "取货时货叉提货超时", "53": "送货时货叉放货超时", "54": "超载", "55": "松绳", "56": "上限位停车", "57": "下限位停车", "58": "前限位停车", "59": "后限位停车", "60": "目标地址未设", "61": "行走测距故障", "62": "升降测距故障", "63": "行走停过界", "64": "升降停过界", "65": "升降失速", "66": "升降时测距值未变", "67": "行走时测距值未变", "68": "升降目标超限", "69": "行走目标超限", "70": "升降速度异常", "71": "行走速度异常", "72": "升降未在正常范围值内", "73": "行走未在正常范围值内", "74": "光通讯断", "75": "取货时站台未就绪", "76": "放货时站台未就绪", "77": "制动电阻过热", "78": "运动时切换模式", "79": "行走校验错", "80": "升降校验错", "81": "取货后中叉无货", "82": "放货时中叉货架有货", "83": "取货时中叉有货", "84": "放货时中叉无货", "85": "放货时中叉货架有货", "86": "取货后远叉无货", "87": "放货后远叉有货", "88": "取货时远叉有货", "89": "放货时远叉无货", "90": "放货时远叉货架有货", "91": "升降计数器错", "92": "行走计数器错", "93": "拨叉动作超时", "94": "拨叉未停在正确位置", "95": "取远端货时近端有货", "96": "放远端货时近端有货", "97": "取货时托盘类型不符", "98": "放货时托盘类型不符", "99": "中叉伺服或变频器故障", "100": "远叉伺服或变频器故障", "101": "应急按钮按下", "102": "安全门打开", "103": "货叉平移变频报警", "104": "卷帘门不在高位", "105": "卷帘门运行超时", "106": "卷帘门变频故障", "107": "左回叉时右超界", "108": "右回叉时左超界"}}, "conveyor": {"name": "输送机", "instructions": {"forward": "正转", "reverse": "反转", "send": "送出"}, "state": {"state": "标志", "instruction": "指令", "sensor": "开关", "action": "动作"}, "error": {"30": "手动", "31": "急停", "32": "变频器报警", "33": "热继电器报警", "34": "电滚筒报警", "35": "输送超时报警", "36": "升降超时报警", "37": "顶升高低位异常", "38": "上位命令错", "39": "外形检测超高", "40": "外形检测左超宽", "41": "外形检测右超宽", "42": "外形检测前超长", "43": "外形检测后超长", "44": "超重", "45": "输送机货物超界", "46": "输送线安全门打开", "47": "旋转台位置异常", "48": "顶升上限位报警", "49": "顶升下限位报警", "60": "提升机提升变频器报警", "61": "提升机移载变频器报警", "62": "提升机上限位报警", "63": "提升机下限位报警", "64": "提升机移载超时报警", "65": "提升机升降超时报警", "66": "提升机升降位置异常", "67": "提升机货物超界报警", "68": "升降速度异常", "69": "升降时码值未变", "70": "提升机左配重检测报警", "71": "提升机右配重检测报警", "72": "提升机左断链检测报警", "73": "提升机右断链检测报警", "74": "提升机升降失速", "75": "提升机输送方向超界", "90": "叠拆盘机升降变频报警", "91": "叠拆盘机侧推电机过热", "92": "叠拆盘机升降上限位报警", "93": "叠拆盘机升降下限位报警", "94": "叠拆盘机侧推机构未就位", "95": "叠拆盘机拨叉机构未就位", "96": "叠拆盘机升降位置异常", "97": "叠拆盘机超高报警", "98": "叠拆盘机升降超时", "99": "叠拆盘机拨叉超时", "100": "叠拆盘机侧推超时", "101": "叠拆盘机升降热继电器报警", "102": "叠拆盘机拨叉热继电器报警", "103": "叠拆盘机阻挡器位置错"}}, "jacking": {"name": "顶升机"}, "elevator": {"name": "升降机"}, "scanner": {"name": "扫码器", "instructions": {"write": "补码"}}}, "monitor": {"title": "设备监控", "search": "查找设备", "notfound": "未找到设备。", "overview": "透视视图", "scopes": {"default": "默认设备组"}, "layers": {"1f": "一层视图", "2f": "二层视图", "3f": "三层视图"}, "states": {"locked": "空闲", "idle": "空闲", "running": "运行", "finished": "完成", "error": "故障", "offline": "离线", "disabled": "停用"}, "device": "设备", "load": "载物", "state": "状态", "error": "故障", "instruction": "指令", "number": "指令号", "action": "动作", "forward": "目标", "barcode": "条码", "original": "起始坐标", "destination": "目标坐标", "task": "任务", "start": "开始时间", "end": "结束时间", "duration": "持续时长", "hour": "{value}小时", "minute": "{value}分", "second": "{value}秒", "active": "活动项目", "history": "历史项目", "refresh": "刷新", "reset": "复位", "ack": "应答", "enable": "启用", "disable": "停用设备", "manual": "人工指令", "more": "更多"}, "instruction": {"title": "设备指令", "task": "任务号", "number": "指令号", "device": "设备", "type": "指令", "forward": "目标", "priority": "优先级", "barcode": "条码", "original": "起始坐标", "destination": "目标坐标", "generated": "生成时间", "started": "开始时间", "completed": "完成时间", "custom": "自定义", "create": "新建", "send": "新建指令", "resend": "重发指令", "delete": "删除指令", "deletes": "删除指令组", "complete": "完成指令", "completes": "完成指令组", "confirm": {"delete": "指令组已进展 {percent}% ，是否确认删除？", "complete": "指令组仅进展 {percent}% ，是否确认完成？"}, "state": {"created": "创建", "ready": "就绪", "sent": "下发", "running": "运行", "error": "错误", "complete": "完成"}}}, "order": {"number": "单据号码", "plancode40": "40单号", "plancode99": "99单号", "platform": "月台", "state": "订单状态", "type": "单据类型", "creater": "单据来源", "created": "单据创建时间", "remark": "备注", "line": "订单行", "executing": "进行中", "finished": "已完成", "create": "创建于", "back": "返回", "add": "添加订单行", "empty": "请添加订单行", "action": "操作", "print": "打印", "actions": {"start": "开始", "edit": "编辑", "cancel": "作废", "complete": "结束", "inbound": "入库", "outbound": "出库", "platform": "选定月台", "pk": "零头区出库"}, "types": {"inbound": "入库单", "outbound": "出库单"}, "states": {"exec": "执行中", "finish": "完成"}, "title": {"inbound": "入库订单", "outbound": "出库订单", "outbound1": "出库订单维护"}, "error": {"material": "物料不存在。"}, "repoconfirm": "仓库确认", "prodconfirm": "生产确认"}, "query": {"stock": {"title": "当前库存", "warehouse": "仓库", "area": "库区", "barcode": "载具条码", "empty": "空载具", "cell": "货位", "locked": "锁定数", "unlocked": "未锁数", "overdue": "过期时间", "day": "{value}天", "hour": "{value}小时", "moment": "小于1小时", "disabled": "不可用", "create": "创建时间", "update": "更新时间", "collapse": "折叠载具", "expand": "展开库存", "back": "返回", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "record": {"title": "历史记录", "trans": "事务", "barcode": "载具条码", "empty": "空载具", "warehouse": "仓库", "area": "库区", "cell": "货位", "original": "起始位置", "destination": "目标位置", "begin": "开始时间", "end": "结束时间", "operator": "操作者", "tag": "标签", "remark": "备注", "inbound": "入库", "outbound": "出库", "update": "更新", "infeed": "上架", "outfeed": "下架", "transfer": "移库", "convey": "输送", "increase": "库存量增加", "decrease": "库存量减少", "collapse": "折叠载具", "expand": "展开库存", "back": "返回", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "blocking": {"title": "冻结库存", "mode": "模式", "auto": "自动", "force": "强制", "overdue": "过期时间", "day": "{value}天", "hour": "{value}小时", "moment": "小于1小时", "barcode": "载具条码", "warehouse": "仓库", "area": "库区", "cell": "货位", "create": "创建时间", "update": "更新时间", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "overdue": {"title": "过期库存", "warehouse": "仓库", "area": "库区", "barcode": "载具条码", "cell": "货位", "create": "创建时间", "update": "更新时间", "overdue": "过期时间", "day": "{value}天", "hour": "{value}小时", "moment": "小于1小时", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "safety": {"title": "存量预警", "warehouse": "仓库", "range": "安全库存", "type": "预警", "shortage": "短缺", "excess": "过量", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "order": {"title": "归档订单", "number": "单号", "type": "类型", "creater": "制单人", "created": "创建时间", "final": "归档时间", "remark": "备注", "finished": "完成数", "complete": "完成订单", "cancel": "作废订单", "expand": "展开订单行", "back": "返回", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}, "interface": {"title": "接口日志", "scope": "系统", "type": "类型", "types": {"input": "输入", "output": "输出"}, "timestamp": "时间", "function": "事件", "message": "报文", "result": "结果", "results": {"true": "成功", "false": "失败"}, "remark": "备注", "retry": "重试第 {index} 次。", "logged": "日志时间", "copy": "复制内容", "export": "导出 EXCEL", "exporting": "导出中，请耐心等待。"}}, "maintenance": {"outboundbill": {"title": "合单管理"}, "control": {"title": "WCS任务数据维护", "id": "控制任务标识", "manageid": "关联管理任务标识", "prestatus": "前置状态", "status": "当前状态", "stockbarcode": "托盘条码", "type": "控制任务类型", "startwarehousecode": "起始库房编码", "endwarehousecode": "终止库房编码", "startdevicecode": "起始设备编码", "enddevicecode": "终止设备编码", "relativecontrolid": "关联控制任务标识", "errortext": "异常信息", "level": "任务等级", "remark": "备注", "begintime": "任务开始时间", "endtime": "任务结束时间"}, "apply": {"title": "WCS申请数据维护", "id": "申请任务标识", "controlid": "关联控制任务标识", "devicecode": "申请设备编码", "stockbarcode": "托盘条码", "status": "申请状态", "type": "申请任务类型", "warehousecode": "库房编码", "parameter": "参数", "remark": "备注", "para01": "预留参数1", "para02": "预留参数2", "createtime": "申请时间"}}}, "navi": {"material": "物料信息", "inbound": "入库操作", "stock": "库存管理", "palletize": "组盘管理", "inventory": "盘点管理", "outbound": "出库操作", "asrs": "立体仓库", "dataview": "数据查询", "workflow": "工作流", "setting": "系统管理", "maintenance": "WCS数据维护"}, "server": {"00001": "参数键不允许空。", "00002": "参数不允许操作。", "00003": "参数内容不正确。", "01001": "令牌不允许空。", "01002": "连接码不允许空。", "01003": "账号不允许空。", "01004": "密码不允许空。", "01005": "密码格式不正确。", "01006": "密码已经初始化。", "01007": "内容过大。", "01008": "当前密码不允许空。", "01009": "当前密码不正确。", "01010": "新密码不允许空。", "01011": "新密码格式不正确。", "01012": "URL 不允许空。", "01013": "用户密码初始化策略致导航异常中止。", "01014": "用户密码过期策略致导航异常中止。", "01015": "没有 URL 的访问权限。", "02001": "令牌不允许空。", "02002": "连接码不允许空。", "02003": "主题不允许为空。", "02004": "目标不允许为空。", "02005": "无法发送消息。", "02006": "消息 ID 不合法。", "03001": "标识不允许空。", "03002": "名称不允许空。", "03003": "角色名重复。", "03004": "角色不存在。", "03005": "角色不允许空。", "03006": "账号不允许空。", "03007": "用户名重复。", "03008": "用户账号重复。", "03009": "用户不存在。", "04001": "作用域不允许空。", "04002": "功能码不允许空。", "04003": "标识不允许空。", "04004": "调用 WCS 接口失败。", "05001": "名称不允许空。", "05002": "父类不存在。", "05003": "品类名重复。", "05004": "标识不允许空。", "05005": "父类不允许设置为自身。", "05006": "品类不存在。", "05007": "料号不允许空。", "05008": "料号重复。", "05009": "名称重复。", "05010": "物料不存在。", "05011": "物料不允许空。", "05012": "库房不允许空。", "05013": "安全限制不允许空。", "05014": "上限必须大于下限。", "05015": "物料仓库信息重复。", "05016": "安全库存不存在。", "06001": "库存行不允许空。", "06002": "入库数量必须表达为正数。", "06003": "出库数量必须表达为负数。", "06004": "存在未指定 ID 的库存行。", "06005": "找不到库存行记录。", "06006": "出库数量大于库存数量。", "06007": "出库数量大于可用库存数量。", "06008": "条码格式不正确。", "06009": "容器操作繁忙，请重试。", "06010": "容器不存在。", "06011": "容器有任务。", "06012": "位置不正确。", "06013": "位置不可用。", "06014": "位置未指定。", "06015": "存在备注不正确的库存行。", "06016": "找不到属性。", "06017": "锁定数量必须表达为正数。", "06018": "锁定数量大于可用数量。", "06019": "解锁数量大于锁定数量。", "07001": "库房不允许空。", "07002": "载具条码不允许空。", "07003": "操作位置不允许空。", "07004": "目标位置不允许空。", "07005": "条码格式不正确。", "07006": "载具操作繁忙，请重试。", "07007": "载具正在执行任务。", "07008": "不允许操作立库中的库存。", "07009": "任务起点不合法。", "07010": "任务起点不在当前库房。", "07011": "任务起点不可用。", "07012": "任务起点不具备送入功能。", "07013": "任务起点不支持载具类型。", "07014": "任务起点不支持载具规格。", "07015": "任务起点存在出库任务。", "07016": "任务终点不合法。", "07017": "任务终点不在当前库房。", "07018": "任务终点非空。", "07019": "任务终点不可用。", "07020": "任务终点不具备送出功能。", "07021": "任务终点不支持载具类型。", "07022": "任务终点不支持载具规格。", "07023": "任务终点存在入库任务。", "07024": "起点到终点之间不存在可用路径。", "07025": "存在对向任务。", "07026": "未指定目标位置。", "07027": "没有可用空位。", "07028": "库存不存在。", "07029": "标识不允许空。", "07030": "位置不允许空。", "07031": "行为不允许空。", "07032": "无法生成避让任务。", "07033": "未知的避让行为。", "07034": "任务不存在。", "07035": "优先级不允许空。", "07036": "任务不允许优先处理。", "07037": "仓库不允许空。", "07038": "货位不允许空。", "07039": "WCS 拒绝操作。", "07040": "拒绝操作。", "08001": "标识不允许空。", "08002": "单号不允许为空", "08003": "类型不允许空", "08004": "订单行不允许空", "08005": "增量不允许空", "08006": "订单不存在", "08007": "类型不存在", "08008": "订单行不存在", "08009": "单号已存在", "08010": "单号生成失败", "08011": "已执行的订单行不允许修改", "08012": "已执行的订单行不允许删除", "08013": "订单行操作者不能为空", "08014": "更新失败计划单已合单", "08015": "更新失败计划单已选定月台", "08016": "未找到指定月台", "08017": "月台状态不可用", "09001": "类型不允许空。", "09002": "设备不允许空。", "09003": "没有可用路径。", "09004": "未生成任何指令。", "09005": "设备未启用。", "09006": "设备不空闲。", "09007": "设备已预约。", "09008": "设备状态不合法。", "09009": "对向指令冲突。", "09010": "数据写入失败。", "09011": "数据读取失败。", "09012": "指令类型不允许空。", "09013": "设备不存在。", "09014": "目标设备不存在。", "09015": "指令不存在。", "09016": "指令状态不合法。", "09017": "标识不允许空。", "09018": "任务号不允许空。", "03010": "库房编码不允许空", "03011": "库房名称不允许空", "03012": "库区编码不允许空", "03013": "库区名称不允许空", "03014": "库房名称重复", "03015": "库房编码重复", "03016": "库区名称重复", "03017": "库区编码重复", "03018": "库房不存在", "03019": "库区不存在", "03020": "客户类别代码不允许空", "03021": "客户类别名称不允许空", "03022": "允收起始天数不允许空", "03023": "允收截止天数不允许空", "03024": "进出标准不允许空", "03025": "客户类别编码重复", "03026": "客户类别名称重复", "03027": "客户资料代码不允许空", "03028": "客户资料名称不允许空", "03029": "绑定客户类别不允许空", "03030": "客户资料编码重复", "03031": "客户资料名称重复", "03032": "客户类别不存在", "03033": "客户资料不存在", "03034": "月台代码不允许空", "03035": "月台名称不允许空", "03036": "月台状态不允许空", "03037": "月台编码重复", "03038": "月台名称重复", "03039": "月台不存在", "03040": "申请任务类型不允许空", "03041": "申请设备编码不允许空", "03042": "托盘条码不允许空", "03043": "申请状态不允许空", "03044": "申请不存在", "03045": "控制不存在", "03046": "控制任务类型不允许空", "03047": "控制任务等级不允许空", "03048": "起始库房编码不允许空", "03049": "起始设备不允许空", "03050": "终止库房编码不允许空", "03051": "终止设备编码不允许空"}}