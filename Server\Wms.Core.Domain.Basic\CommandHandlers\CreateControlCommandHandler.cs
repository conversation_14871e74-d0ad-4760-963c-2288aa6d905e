using AutoMapper;
using Domain_KSF.Models;
using Kean.Domain.Basic.Commands;
using Kean.Domain.Basic.Events;
using Kean.Domain.Basic.Models;
using Kean.Domain.Basic.Repositories;
using Kean.Domain.Shared;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Basic.CommandHandlers
{
   /// <summary>
   /// 创建申请命令处理程序
   /// </summary>
    public sealed class CreateControlCommandHandler(
        IMapper mapper,                     // 模型映射
        ICommandBus commandBus,             // 命令总线
        IioRepository ioRepository    // 申请控制仓库
    ) : CommandHandler<CreateControlCommand>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(CreateControlCommand command, CancellationToken cancellationToken)
        {
            if (command.ValidationResult.IsValid)
            {
                var control = mapper.Map<Control>(command);
                control.BeginTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                control.Status = control.PreStatus = 0;
                Output(nameof(command.Id), await ioRepository.CreateControl(control));
            }
            else
            {
                await commandBus.Notify(command.ValidationResult,
                    cancellationToken: cancellationToken);
            }
        }
    }
}
