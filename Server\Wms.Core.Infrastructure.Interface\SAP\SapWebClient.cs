﻿/*
 * 这是一个例子，描述了对 WMS 调用外部接口的具体实现
 * 所有对与 WMS 调用接口的具体操作，都建议在这个项目中做
 */

using Kean.Infrastructure.Utilities;
//using microsoft.lobservices.sap._2007._03.Rfc;
//using microsoft.lobservices.sap._2007._03.Types.Rfc;
using System.Collections.Generic;
using System.Net.Security;
using System.ServiceModel;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Kean.Infrastructure.Interface
{
    // 假设调用目标是 ERP。注意：Kean.Infrastructure.Interface.DependencyInjection 中定义了此类的依赖注入
    public partial class SapWebClient : ClientBase<ISapService>, Domain.Interface.RemoteClients.ISapClient
    {
        // 根据实际情况注入
        public SapWebClient() : base(new BasicHttpBinding(), new EndpointAddress("http://localhost:52106/Service/WMS_Service"))//"http://127.0.0.1:8088/mockZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap"))
        //
        {
        }

        // 接口调用的技术实现
        public Task<string> TaskUpload(string xml)
        {
            /*
            ZALLSAP_UPLOAD_GOODSMOV tmp = new ZALLSAP_UPLOAD_GOODSMOV();
            tmp.TARGETSYS = "1101";
            tmp.SOURCESYS = "2306";
            tmp.UPDATETIME = "20250825095811";

            List<ZALLSAP_UPLOAD_GOODSMOV_1> lst1 = new List<ZALLSAP_UPLOAD_GOODSMOV_1>();
            List<ZALLSAP_UPLOAD_GOODSMOV_2> lst2 = new List<ZALLSAP_UPLOAD_GOODSMOV_2>();
            List<ZALLSAP_UPLOAD_GOODSMOV_3> lst3 = new List<ZALLSAP_UPLOAD_GOODSMOV_3>();
            ZALLSAP_UPLOAD_GOODSMOV_1 arr1 = new ZALLSAP_UPLOAD_GOODSMOV_1()
            {
                BSART = "RC6",
                BUDAT = "20250908",
                EBELN = "110406149",
                HTEXT = "",
                VTXTK = "",
                LGPLA = "",
                I_TIME = "164500",
                I_DATE = "20250908"
            };
            lst1.Add(arr1);
            ZALLSAP_UPLOAD_GOODSMOV_2 arr2 = new ZALLSAP_UPLOAD_GOODSMOV_2()
            {
                CHARG = "",
                POSNR = "000001",
                SGTXT = "",
                MATNR = "17AW582409H",
                MENGE = 1.0m,
                MEINS = "CS",
                WERKS = "ZTY1",
                LGORT = "2088",
                INSMK = "",
                HSDAT = "20250908",
                LICHA = "",
                EBELN = "110406149"
            };
            lst2.Add(arr2);
            //ZALLSAP_UPLOAD_GOODSMOV_3 arr3 = new ZALLSAP_UPLOAD_GOODSMOV_3();
            //lst3.Add(arr3);
            tmp.IT_MATDOC_HEAD = lst1.ToArray();
            tmp.IT_MATDOC_DETAILS = lst2.ToArray();
            tmp.OT_MATDOC = lst3.ToArray();
            //a.Operation_ZALLSAP_UPLOAD_GOODSMOV(goodsMov);
            //XmlSerializerNamespaces xmlns = new XmlSerializerNamespaces();
            //xmlns.Add("", "http://Microsoft.LobServices.Sap/2007/03/Rfc/");
            string task = XmlHelper.Serialize(tmp);

            //string ak = "<ZALLSAP_UPLOAD_GOODSMOV>\r\n  <SOURCESYS xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\">1103</SOURCESYS>\r\n  <TARGETSYS xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\">2306</TARGETSYS>\r\n  <UPDATETIME xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\">20250825095811</UPDATETIME>\r\n  <IT_MATDOC_DETAILS xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\">\r\n    <ZALLSAP_UPLOAD_GOODSMOV_2 xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/\">\r\n      <EBELN>2000</EBELN>\r\n      <POSNR>000010</POSNR>\r\n      <SGTXT>MEMO</SGTXT>\r\n      <MATNR>35001876573738K</MATNR>\r\n      <MEINS>CS</MEINS>\r\n      <WERKS>VTJ1</WERKS>\r\n      <LGORT>0026</LGORT>\r\n      <INSMK />\r\n      <DZUSCH p4:nil=\"true\" xmlns:p4=\"http://www.w3.org/2001/XMLSchema-instance\" />\r\n      <HSDAT>00000000</HSDAT>\r\n      <LICHA />\r\n      <CHARG>VTJ2382512</CHARG>\r\n      <ELIKZ p4:nil=\"true\" xmlns:p4=\"http://www.w3.org/2001/XMLSchema-instance\" />\r\n    </ZALLSAP_UPLOAD_GOODSMOV_2>\r\n  </IT_MATDOC_DETAILS>\r\n  <IT_MATDOC_HEAD xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\">\r\n    <ZALLSAP_UPLOAD_GOODSMOV_1 xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/\">\r\n      <BSART>DC1</BSART>\r\n      <EBELN>2000</EBELN>\r\n      <I_DATE>20230825</I_DATE>\r\n      <I_TIME>095811</I_TIME>\r\n      <LGPLA>SUZHOU01</LGPLA>\r\n      <VTXTK />\r\n      <HTEXT>W101 生产一线</HTEXT>\r\n      <BUDAT>20230825</BUDAT>\r\n    </ZALLSAP_UPLOAD_GOODSMOV_1>\r\n  </IT_MATDOC_HEAD>\r\n  <OT_MATDOC p2:nil=\"true\" xmlns:p2=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns=\"http://Microsoft.LobServices.Sap/2007/03/Rfc/\" />\r\n</ZALLSAP_UPLOAD_GOODSMOV>";
            
            //var response = Channel.ERP_PALNS_OUT(true, out sResult);
            //var response1 = Channel.SAP_UPDATE(task, out sResult);
            */

            string sResult;
            var response1 = Channel.SAP_UPDATE(xml, out sResult);
            return Task.FromResult<string>(null); //response;
        }
    }

    // 契约
    //[ServiceContract(Namespace = "microsoft.lobservices.sap._2007._03.Rfc",
    //Name = "ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap")]
    [ServiceContract]
    public interface ISapService
    {
        [OperationContract(Action = "http://tempuri.org/I_K3/ERP_PALNS_OUT", ReplyAction = "*")]//"http://ws.ksf.com.cn/ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingT/Operation_ZALLSAP_UPLOAD_GOODSMOV", ReplyAction = "*")]//
        [XmlSerializerFormat(SupportFaults = true)]
        bool ERP_PALNS_OUT(bool bTrans, out string sResult);
        //Task<ZALLSAP_UPLOAD_GOODSMOV> ZALLSAP_UPLOAD_GOODSMOV(ZALLSAP_UPLOAD_GOODSMOV xml);

        [OperationContract(Action = "http://tempuri.org/I_K3/SAP_UPDATE", ReplyAction = "*")]
        [XmlSerializerFormat(SupportFaults = true)]
        bool SAP_UPDATE(string task, out string sResult);
    }
}
