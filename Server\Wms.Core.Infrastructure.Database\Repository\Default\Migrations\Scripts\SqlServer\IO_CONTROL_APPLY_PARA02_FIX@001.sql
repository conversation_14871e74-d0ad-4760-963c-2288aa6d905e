-- 修复 IO_CONTROL_APPLY 表中 CONTROL_APPLY_PARA02 字段长度限制问题
-- 问题：字段长度为 varchar(50)，无法存储视觉识别系统返回的JSON数据
-- 解决：将字段类型改为 nvarchar(500)，支持更长的数据存储

-- 检查字段是否存在并且长度不足
IF EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'IO_CONTROL_APPLY' 
    AND COLUMN_NAME = 'CONTROL_APPLY_PARA02'
    AND CHARACTER_MAXIMUM_LENGTH = 50
)
BEGIN
    PRINT '正在修改 IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段长度...'
    
    -- 修改字段类型和长度
    ALTER TABLE IO_CONTROL_APPLY 
    ALTER COLUMN CONTROL_APPLY_PARA02 nvarchar(500)
    
    PRINT 'IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段已成功修改为 nvarchar(500)'
END
ELSE
BEGIN
    PRINT 'IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段已经是正确的类型和长度，无需修改'
END

-- 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'IO_CONTROL_APPLY' 
AND COLUMN_NAME = 'CONTROL_APPLY_PARA02'
