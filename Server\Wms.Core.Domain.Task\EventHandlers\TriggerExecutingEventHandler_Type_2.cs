using Kean.Domain.Shared;
using Kean.Domain.Shared.Interfaces;
using Kean.Domain.Stock.Repositories;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Models;
using Kean.Domain.Task.Repositories;
using Kean.Infrastructure.Database.Repository;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading;


namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 设备触发命令执行时，处理类型 2
    /// 栈板到位申请 - 集成视觉识别功能
    /// </summary>
    public sealed class TriggerExecutingEventHandler_Type_2(
        IOrderService orderService,
        IStockService stockService,
        Task.Repositories.IWarehouseRepository warehouseRepository,
        ILogger<TriggerExecutingEventHandler_Type_2> logger,    // 日志
        ICommandBus commandBus,                                 // 命令总线
        INotification notification,                              // 总线通知
        IDefaultDb database,
        IVisualIntegrationService visualIntegrationService = null,    // 视觉识别服务（可选）
        IStationMappingService stationMappingService = null           // 站位映射服务（可选）
    ) : EventHandler<TriggerExecutingEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(TriggerExecutingEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Type == 2)
            {
                switch (@event.Parameter.Value<string>("fallback"))
                {


                    // 栈板到位向视觉系统申请是被
                    default:

                        var data = @event.Parameter as JObject;

                        logger.LogInformation("处理类型2事件：栈板到位申请，设备: {Device}, 仓库: {Warehouse}, 条码: {Barcode}",
                            @event.Device, @event.Warehouse, data?.Value<string>("Barcode"));

                        // 集成视觉识别功能 - 实现"入庫站位棧板到達要求辨識"
                        await IntegrateVisualRecognitionAsync(@event, data);

                        break;

                    // 回退
                    case "Reverse":

                        // TODO：实现回退逻辑。若回退失败可继续降级（ @event.Fallback("..."); ）

                        break;

                    // 其他降级操作
                    case "...":

                        // TODO

                        break;
                }
            }
        }

        /// <summary>
        /// 集成视觉识别功能
        /// 根据接口规范第6节，当产品棧板到达站位时，判断识别设备状态并决定入库方式
        /// 实现"入庫站位棧板到達要求辨識"业务逻辑
        /// </summary>
        /// <param name="event">触发事件</param>
        /// <param name="data">事件数据</param>
        private async System.Threading.Tasks.Task IntegrateVisualRecognitionAsync(
            TriggerExecutingEvent @event,
            JObject data)
        {
            try
            {
                logger.LogInformation("开始处理棧板到达事件，设备: {Device}, 条码: {Barcode}",
                    @event.Device, data?.Value<string>("Barcode"));

                // 检查是否有视觉识别服务可用
                if (visualIntegrationService == null)
                {
                    logger.LogInformation("视觉识别服务未配置，设备 {Device} 使用传统入库方式", @event.Device);
                    return;
                }

                // 方法1：使用站位映射服务获取逻辑站位编号（推荐）
                string logicalStationNo = null;
                if (stationMappingService != null)
                {
                    logicalStationNo = await stationMappingService.GetLogicalStationNoAsync(@event.Device);

                    if (!string.IsNullOrEmpty(logicalStationNo))
                    {
                        // 检查该站位是否启用视觉识别
                        var isVisualRecognitionEnabled = await stationMappingService.IsVisualRecognitionEnabledAsync(@event.Device);
                        if (!isVisualRecognitionEnabled)
                        {
                            logger.LogInformation("设备 {Device} (逻辑站位: {LogicalStationNo}) 未启用视觉识别，使用传统入库方式",
                                @event.Device, logicalStationNo);
                            return;
                        }

                        logger.LogInformation("设备 {Device} 映射到逻辑站位 {LogicalStationNo}，已启用视觉识别",
                            @event.Device, logicalStationNo);
                    }
                }

                // 方法2：如果站位映射服务不可用，使用硬编码映射作为回退
                if (string.IsNullOrEmpty(logicalStationNo))
                {
                    logicalStationNo = MapDeviceToStationNo(@event.Device);
                    if (string.IsNullOrEmpty(logicalStationNo))
                    {
                        logger.LogInformation("设备 {Device} 不是视觉识别站位，使用传统入库方式", @event.Device);
                        return;
                    }

                    logger.LogInformation("使用硬编码映射：设备 {Device} → 逻辑站位 {LogicalStationNo}",
                        @event.Device, logicalStationNo);
                }

                // 调用视觉识别服务处理棧板到达事件
                logger.LogInformation("棧板到达站位 {LogicalStationNo}，设备 {Device}，开始视觉识别流程",
                    logicalStationNo, @event.Device);

                var success = await visualIntegrationService.HandlePalletArrivalAsync(logicalStationNo);

                if (success)
                {
                    logger.LogInformation("视觉识别棧板到达处理成功，站位: {LogicalStationNo}, 设备: {Device}",
                        logicalStationNo, @event.Device);
                }
                else
                {
                    logger.LogWarning("视觉识别棧板到达处理失败，站位: {LogicalStationNo}, 设备: {Device}，将使用传统入库方式",
                        logicalStationNo, @event.Device);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "视觉识别集成过程中发生异常，设备: {Device}，将使用传统入库方式", @event.Device);
                // 不抛出异常，让业务流程继续使用传统入库方式
            }
        }

        /// <summary>
        /// 将设备编号映射到逻辑站位编号（硬编码回退方案）
        /// 当IStationMappingService不可用时使用此方法作为回退
        /// 物理设备编码 → 逻辑站位编码（用于视觉识别系统通信）
        /// 注意：优先使用IStationMappingService.GetLogicalStationNoAsync()方法
        /// </summary>
        /// <param name="deviceCode">物理设备编号</param>
        /// <returns>逻辑站位编号，如果不是识别站位则返回空字符串</returns>
        private static string MapDeviceToStationNo(string deviceCode)
        {
            if (string.IsNullOrEmpty(deviceCode))
                return string.Empty;

            // 硬编码映射作为回退方案
            // 根据实际的物理设备编号到逻辑站位编号的映射规则
            // 映射关系基于docs\站位编码.md中的实际配置
            return deviceCode switch
            {
                // 人工入库站位映射
                "12022" => "1101",  // 人工入库口 → 第一车间人工入库站
                "12028" => "1201",  // 入库口 → 第二车间人工入库站

                // 自动入库站位映射（第一车间）
                "12001" => "1111",  // 入库口1 → 第一车间自动入库站
                "12002" => "1111",  // 入库口2 → 第一车间自动入库站
                "12003" => "1111",  // 入库口3 → 第一车间自动入库站
                "12004" => "1111",  // 入库口4 → 第一车间自动入库站
                "12005" => "1111",  // 入库口5 → 第一车间自动入库站

                // 自动入库站位映射（第二车间）
                "12006" => "1204",  // 入库口6 → 第二车间自动入库站
                "12007" => "1204",  // 入库口7 → 第二车间自动入库站
                "12008" => "1204",  // 入库口8 → 第二车间自动入库站
                "12009" => "1204",  // 入库口9 → 第二车间自动入库站
                "12010" => "1204",  // 入库口10 → 第二车间自动入库站

                _ => string.Empty  // 不是视觉识别站位
            };
        }

        // 注意：视觉识别结果的处理将通过事件总线或其他机制在VisualIntegration域中处理
        // 这里不直接处理识别结果，避免循环依赖
    }
}
