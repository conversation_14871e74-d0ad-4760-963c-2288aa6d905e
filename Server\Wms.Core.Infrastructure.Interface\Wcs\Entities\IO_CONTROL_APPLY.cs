﻿using Kean.Infrastructure.Database;

namespace Kean.Infrastructure.Interface.Wcs.Entities
{
    public class IO_CONTROL_APPLY : IEntity
    {
        [Identifier(true)]
        public int CONTROL_APPLY_ID { get; set; }

        public int? CONTROL_ID { get; set; }

        public string CONTROL_APPLY_TYPE { get; set; }

        public string WAREHOUSE_CODE { get; set; }

        public string DEVICE_CODE { get; set; }

        public string STOCK_BARCODE { get; set; }

        public int? APPLY_TASK_STATUS { get; set; }

        public string CREATE_TIME { get; set; }

        public string CONTROL_APPLY_PARAMETER { get; set; }

        public string CONTROL_APPLY_REMARK { get; set; }
        public string CONTROL_APPLY_PARA01 { get; set; }
        public string CONTROL_APPLY_PARA02 { get; set; }
    }
}
