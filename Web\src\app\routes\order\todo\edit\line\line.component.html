<h2 mat-dialog-title>{{ (data ? 'shared.operation.edit' : 'shared.operation.add') | translate }}</h2>
<div mat-dialog-content>
  <form autocomplete="off" [formGroup]="form">
    <mat-form-field>
      <mat-label>{{'routes.material.code' | translate}}</mat-label>
      <input matInput formControlName="code" [matAutocomplete]="$autocomplete" (ngModelChange)="autocomplete()" required>
      <mat-autocomplete #$autocomplete="matAutocomplete">
        <mat-option *ngFor="let item of material" [value]="item.code">
          <span>{{item.code}}</span>|<small>{{item.name}}</small>
        </mat-option>
      </mat-autocomplete>
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.qty' | translate}}</mat-label>
      <input type="number" matInput formControlName="quantity" min="0" required>
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.batch' | translate}}</mat-label>
      <input matInput formControlName="batch">
    </mat-form-field>
    <!--<mat-form-field>
      <mat-label>{{'routes.material.bill' | translate}}</mat-label>
      <input matInput formControlName="bill">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.supplier' | translate}}</mat-label>
      <input matInput formControlName="supplier">
    </mat-form-field>-->
<!--
    <mat-form-field>
      <mat-label>{{'routes.material.adjustQuantity' | translate}}</mat-label>
      <input matInput formControlName="adjustQuantity">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.adjustReason' | translate}}</mat-label>
      <input matInput formControlName="adjustReason">
    </mat-form-field>-->
    
    <!--<mat-form-field>
      <mat-label>{{'routes.material.effectivePeriod' | translate}}</mat-label>
      <input matInput formControlName="effectivePeriod">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.expirationPeriod' | translate}}</mat-label>
      <input matInput formControlName="expirationPeriod">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.financialPostLatestTime' | translate}}</mat-label>
      <input matInput formControlName="financialPostLatestTime">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.financialPostQuantity' | translate}}</mat-label>
      <input matInput formControlName="financialPostQuantity">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.financialPostStatus' | translate}}</mat-label>
      <input matInput formControlName="financialPostStatus">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.financialPostTime' | translate}}</mat-label>
      <input matInput formControlName="financialPostTime">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.financialPostUploadTime' | translate}}</mat-label>
      <input matInput formControlName="financialPostUploadTime">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.importTime' | translate}}</mat-label>
      <input matInput formControlName="importTime">
    </mat-form-field>-->
    
    
    <!--<mat-form-field>
      <mat-label>{{'routes.material.transferType' | translate}}</mat-label>
      <input matInput formControlName="transferType">
    </mat-form-field>-->
    <!--<mat-form-field>
      <mat-label>{{'routes.material.truckInfo' | translate}}</mat-label>
      <input matInput formControlName="truckInfo">
    </mat-form-field>-->
    <mat-form-field>
      <mat-label>{{'routes.material.warehouseCode' | translate}}</mat-label>
      <input matInput formControlName="warehouseCode">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.productionLine' | translate}}</mat-label>
      <input matInput formControlName="productionLine">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.workGroup' | translate}}</mat-label>
      <input matInput formControlName="workGroup">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.clientCode' | translate}}</mat-label>
      <input matInput formControlName="clientCode">
    </mat-form-field>
    <mat-form-field>
      <mat-label>{{'routes.material.sapNo' | translate}}</mat-label>
      <input matInput formControlName="sapNo">
    </mat-form-field>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button mat-button color="primary" [disabled]="form.invalid" (click)="save()">{{ 'shared.dialog.ok' | translate | uppercase }}</button>
  <button mat-button mat-dialog-close>{{ 'shared.dialog.cancel' | translate | uppercase }}</button>
</div>