﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{

    public class API_Message
    {
        public string SourceSys { get; set; }
        public string TargetSys { get; set; }
        public string UpdateTime { get; set; }
        public string Guid { get; set; }
        public string SingleTargetSys { get; set; }
        public string AppKey { get; set; }
        public string IsTest { get; set; }
        public string IsManualSend { get; set; }

    }
}
