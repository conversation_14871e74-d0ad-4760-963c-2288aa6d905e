import { Component } from "@angular/core";
import { FormGroup, Validators, FormBuilder } from "@angular/forms";
import { MatDialogRef } from "@angular/material/dialog";
import { HttpService } from "@app/core/services/http.service";
import { I18nService } from "@app/core/services/i18n.service";
import { ToastService } from "@app/core/services/toast.service";
import { forEach } from "lodash";

@Component({
  selector: "app-target",
  templateUrl: "./target.component.html",
  styleUrls: ["./target.component.scss"],
})
export class TargetComponent {
  private _form: FormGroup;
  private _original: any[] = [];

  constructor(
    private _formBuilder: FormBuilder,
    private _httpService: HttpService,
    private _i18nService: I18nService,
    private _toastService: ToastService,
    private _dialogRef: MatDialogRef<TargetComponent>
  ) {
    this._httpService.get("cells?type=station&in=true&out=true").then((res: any) => {
      res.items.forEach((element) => {
        this.original.push(element);
      });
    });
    this._form = this._formBuilder.group({
      quantity: [null, [Validators.required]],
      original: [null, [Validators.required]],
    });
  }

  public get form() {
    return this._form;
  }

  public get original() {
    return this._original;
  }

  public save = async () => {
    const quantity = this._form.controls.quantity.value;
    const originalId: string = this._form.controls.original.value;
    var originalS = this.original.find((x) => x.id == originalId);
    this._dialogRef.close({
      original: originalS.id,
      quantity: quantity,
      area: originalS.area,
    });
  };
}
