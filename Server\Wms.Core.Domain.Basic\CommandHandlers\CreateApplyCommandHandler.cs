﻿using AutoMapper;
using Domain_KSF.Models;
using Kean.Domain.Basic.Commands;
using Kean.Domain.Basic.Events;
using Kean.Domain.Basic.Models;
using Kean.Domain.Basic.Repositories;
using Kean.Domain.Shared;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Basic.CommandHandlers
{
   /// <summary>
   /// 创建申请命令处理程序
   /// </summary>
    public sealed class CreateApplyCommandHandler(
        IMapper mapper,                     // 模型映射
        ICommandBus commandBus,             // 命令总线
        IioRepository ioRepository    // 申请控制仓库
    ) : CommandHandler<CreateApplyCommand>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(CreateApplyCommand command, CancellationToken cancellationToken)
        {
            if (command.ValidationResult.IsValid)
            {
                var apply = mapper.Map<Apply>(command);
                apply.CreateTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                apply.Status = 0;
                Output(nameof(command.Id), await ioRepository.CreateApply(apply));
            }
            else
            {
                await commandBus.Notify(command.ValidationResult,
                    cancellationToken: cancellationToken);
            }
        }
    }
}
