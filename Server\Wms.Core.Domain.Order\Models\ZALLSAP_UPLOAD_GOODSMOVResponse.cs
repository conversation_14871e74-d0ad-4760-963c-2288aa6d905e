﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public class ZALLSAP_UPLOAD_GOODSMOVResponse
    {
        private ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILSField;

        private ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEADField;

        private ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOCField;


        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILS
        {
            get
            {
                return this.IT_MATDOC_DETAILSField;
            }
            set
            {
                this.IT_MATDOC_DETAILSField = value;
            }
        }

        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEAD
        {
            get
            {
                return this.IT_MATDOC_HEADField;
            }
            set
            {
                this.IT_MATDOC_HEADField = value;
            }
        }

        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOC
        {
            get
            {
                return this.OT_MATDOCField;
            }
            set
            {
                this.OT_MATDOCField = value;
            }
        }
    }
}
