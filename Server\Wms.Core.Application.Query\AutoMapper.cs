﻿using AutoMapper;
using Domain_KSF.Models;
using Kean.Application.Query.ViewModels;
using Kean.Infrastructure.Configuration;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.Utilities;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using static Mysqlx.Expect.Open.Types.Condition.Types;

namespace Kean.Application.Query
{
    /// <summary>
    /// 模型映射配置
    /// </summary>
    public sealed class AutoMapper : Profile
    {
        /// <summary>
        /// 初始化 Kean.Application.Query.AutoMapper 类的新实例
        /// </summary>
        public AutoMapper()
        {
            CreateMap<T_SYS_SECURITY, Blacklist>()
                .ForMember(viewmodel => viewmodel.Address, options => options.MapFrom(entity => entity.SECURITY_VALUE))
                .ForMember(viewmodel => viewmodel.Timestamp, options => options.MapFrom(entity => entity.SECURITY_TIMESTAMP));

            CreateMap<T_SYS_MENU, Menu>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.MENU_ID))
                .ForMember(viewmodel => viewmodel.Parent, options => options.MapFrom(entity => entity.MENU_PARENT_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.MENU_TYPE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.MENU_NAME))
                .ForMember(viewmodel => viewmodel.Parameter, options => options.MapFrom(entity => entity.MENU_PARAM))
                .ForMember(viewmodel => viewmodel.Icon, options => options.MapFrom(entity => entity.MENU_ICON))
                .ForMember(viewmodel => viewmodel.Media, options => options.MapFrom(entity => entity.MENU_MEDIA));

            CreateMap<T_SYS_ROLE, Role>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.ROLE_ID))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.ROLE_NAME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.ROLE_REMARK));

            CreateMap<T_SYS_USER, User>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.USER_ID))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.USER_NAME))
                .ForMember(viewmodel => viewmodel.Account, options => options.MapFrom(entity => entity.USER_ACCOUNT))
                .ForMember(viewmodel => viewmodel.Avatar, options => options.MapFrom(entity => entity.USER_AVATAR));

            CreateMap<dynamic, Message>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.MESSAGE_ID))
                .ForMember(viewmodel => viewmodel.Time, options => options.MapFrom((entity, _) => entity.MESSAGE_TIME))
                .ForMember(viewmodel => viewmodel.Subject, options => options.MapFrom((entity, _) => entity.MESSAGE_SUBJECT))
                .ForMember(viewmodel => viewmodel.Content, options => options.MapFrom((entity, _) => entity.MESSAGE_CONTENT))
                .ForMember(viewmodel => viewmodel.Flag, options => options.MapFrom((entity, _) => entity.MESSAGE_FLAG))
                .ForMember(viewmodel => viewmodel.Source, options => options.MapFrom((entity, _) => new User { Id = entity.USER_ID, Name = entity.USER_NAME, Avatar = entity.USER_AVATAR }));

            CreateMap<dynamic, Log>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.LOG_ID))
                .ForMember(viewmodel => viewmodel.Time, options => options.MapFrom((entity, _) => entity.LOG_TIME))
                .ForMember(viewmodel => viewmodel.Message, options => options.MapFrom((entity, _) => entity.LOG_MSG))
                .ForMember(viewmodel => viewmodel.IpAddress, options => options.MapFrom((entity, _) => entity.LOG_IP))
                .ForMember(viewmodel => viewmodel.Request, options => options.MapFrom((entity, _) => entity.LOG_REQ))
                .ForMember(viewmodel => viewmodel.Data, options => options.MapFrom((entity, _) => entity.LOG_DATA))
                .ForMember(viewmodel => viewmodel.Operator, options => options.MapFrom((entity, _) => entity.USER_ID == null ? null : new User { Id = entity.USER_ID, Name = entity.USER_NAME, Avatar = entity.USER_AVATAR }));

            CreateMap<T_INF_HIS, Interface>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.INF_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.INF_TYPE))
                .ForMember(viewmodel => viewmodel.Scope, options => options.MapFrom(entity => entity.INF_SCOPE))
                .ForMember(viewmodel => viewmodel.Function, options => options.MapFrom(entity => entity.INF_FUNC))
                .ForMember(viewmodel => viewmodel.Unique, options => options.MapFrom(entity => entity.INF_UNIQUE))
                .ForMember(viewmodel => viewmodel.Message, options => options.MapFrom(entity => entity.INF_MSG))
                .ForMember(viewmodel => viewmodel.Timestamp, options => options.MapFrom(entity => entity.INF_TIME))
                .ForMember(viewmodel => viewmodel.Logged, options => options.MapFrom(entity => entity.LOG_TIME))
                .ForMember(viewmodel => viewmodel.Index, options => options.MapFrom(entity => entity.EXEC_INDEX))
                .ForMember(viewmodel => viewmodel.Result, options => options.MapFrom(entity => entity.EXEC_RESULT))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.EXEC_REMARK));

            CreateMap<T_WH_WAREHOUSE, Warehouse>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.WAREHOUSE_REMARK));

            CreateMap<T_WH_AREA, Area>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.AREA_ID))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.AREA_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.AREA_NAME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.AREA_REMARK));

            CreateMap<T_WH_CELL, Cell>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.CELL_ID))
                .ForMember(viewmodel => viewmodel.Area, options => options.MapFrom(entity => entity.AREA_ID))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.CELL_GROUP))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.CELL_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.CELL_NAME))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.CELL_TYPE))
                .ForMember(viewmodel => viewmodel.AllowIn, options => options.MapFrom(entity => entity.CELL_IN))
                .ForMember(viewmodel => viewmodel.AllowOut, options => options.MapFrom(entity => entity.CELL_OUT))
                .ForMember(viewmodel => viewmodel.Row, options => options.MapFrom(entity => entity.CELL_Z))
                .ForMember(viewmodel => viewmodel.Column, options => options.MapFrom(entity => entity.CELL_X))
                .ForMember(viewmodel => viewmodel.Layer, options => options.MapFrom(entity => entity.CELL_Y))
                .ForMember(viewmodel => viewmodel.Store, options => options.MapFrom(entity => entity.CELL_STATUS))
                .ForMember(viewmodel => viewmodel.Task, options => options.MapFrom(entity => entity.RUN_STATUS))
                .ForMember(viewmodel => viewmodel.Timestamp, options => options.MapFrom(entity => entity.STATUS_TIME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.CELL_REMARK))
                .ForMember(viewmodel => viewmodel.Visual, options => options.MapFrom(entity => entity.CELL_VISUAL));

            CreateMap<T_GOODS_MAIN, Material>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.T_GOODS_MAIN, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_ID))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.PalletQuantity, options => options.MapFrom(entity => entity.PALLET_QUANTITY));

            CreateMap<T_GOODS_CLASS, Category>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.CLASS_ID))
                .ForMember(viewmodel => viewmodel.Parent, options => options.MapFrom(entity => entity.CLASS_PARENT_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.CLASS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.CLASS_NAME));

            CreateMap<V_GOODS_SAFETY, Safety>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.T_GOODS_MAIN, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.SAFETY_ID))
                .ForMember(viewmodel => viewmodel.Material, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_ID))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(viewmodel => viewmodel.LowerLimit, options => options.MapFrom(entity => entity.LOWER_LIMIT))
                .ForMember(viewmodel => viewmodel.UpperLimit, options => options.MapFrom(entity => entity.UPPER_LIMIT));

            CreateMap<T_PLAN_TYPE, Ordtyp>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.TYPE_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.TYPE_NAME))
                .ForMember(viewmodel => viewmodel.Number, options => options.MapFrom(entity => entity.TYPE_NO));

            CreateMap<T_PLAN_MAIN, Order>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.PLAN_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.PLAN_TYPE))
                .ForMember(viewmodel => viewmodel.Number, options => options.MapFrom(entity => entity.PLAN_CODE))
                .ForMember(viewmodel => viewmodel.Creater, options => options.MapFrom(entity => entity.PLAN_CREATER))
                .ForMember(viewmodel => viewmodel.CreateTime, options => options.MapFrom(entity => entity.PLAN_CREATE_TIME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.PLAN_REMARK))
                .ForMember(viewmodel => viewmodel.TruckInfo, options => options.MapFrom(entity => entity.TRUCK_INFO))
                .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.PLAN_STATUS))
                .ForMember(viewmodel => viewmodel.Plancode40, options => options.MapFrom(entity => entity.PLAN_CODE_40))
                .ForMember(viewmodel => viewmodel.Plancode99, options => options.MapFrom(entity => entity.PLAN_CODE_99))
                .ForMember(viewmodel => viewmodel.SapType, options => options.MapFrom(entity => entity.PLAN_SAP_TYPE))
                .ForMember(viewmodel => viewmodel.SapType, options => options.MapFrom(entity => entity.PLAN_SAP_TYPE_NAME))
                .ForMember(viewmodel => viewmodel.Platform, options => options.MapFrom(entity => entity.PLAN_PLATFORM));

            CreateMap<T_PLAN_HIS_MAIN, Order>()
                .IncludeBase<T_PLAN_MAIN, Order>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.PLAN_ID))
                .ForMember(viewmodel => viewmodel.FinalTime, options => options.MapFrom(entity => entity.PLAN_FINAL_TIME));

            CreateMap<V_PLAN_LIST, Order>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.V_GOODS_PROPERTY, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Line, options => options.MapFrom(entity => entity.PLAN_LIST_ID))
                .ForMember(viewmodel => viewmodel.StorageLine, options => options.MapFrom(entity => entity.STORAGE_LIST_ID))
                .ForMember(viewmodel => viewmodel.PlanListFlag, options => options.MapFrom(entity => entity.PLAN_LIST_FLAG))
                .ForMember(viewmodel => viewmodel.PlanListRemark, options => options.MapFrom(entity => entity.PLAN_LIST_REMARK))
                .ForMember(viewmodel => viewmodel.PlanListRepo, options => options.MapFrom(entity => entity.PLAN_LIST_REPOCONFIRM))
                .ForMember(viewmodel => viewmodel.PlanListProd, options => options.MapFrom(entity => entity.PLAN_LIST_PRODCONFIRM))
                .ForMember(viewmodel => viewmodel.PlantCode, options => options.MapFrom(entity => entity.PLANT_CODE))
                .ForMember(viewmodel => viewmodel.ProductionLine, options => options.MapFrom(entity => entity.PRODUCTION_LINE))
                .ForMember(viewmodel => viewmodel.ClientCode, options => options.MapFrom(entity => entity.CLIENT_CODE))
                .ForMember(viewmodel => viewmodel.WarehouseCode, options => options.MapFrom(entity => entity.WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.FinancialPostStatus, options => options.MapFrom(entity => entity.FINANCIAL_POST_STATUS))
                .ForMember(viewmodel => viewmodel.TruckInfo, options => options.MapFrom(entity => entity.TRUCK_INFO))
                .ForMember(viewmodel => viewmodel.TransferType, options => options.MapFrom(entity => entity.TRANSFER_TYPE))
                .ForMember(viewmodel => viewmodel.WorkGroup, options => options.MapFrom(entity => entity.WORK_GROUP))
                .ForMember(viewmodel => viewmodel.AdjustQuantity, options => options.MapFrom(entity => entity.ADJUST_QUANTITY))
                .ForMember(viewmodel => viewmodel.AdjustReason, options => options.MapFrom(entity => entity.ADJUST_REASON))
                .ForMember(viewmodel => viewmodel.FinancialPostQuantity, options => options.MapFrom(entity => entity.FINANCIAL_POST_QUANTITY))
                .ForMember(viewmodel => viewmodel.PlanListRepoTime, options => options.MapFrom(entity => entity.PLAN_LIST_REPOCONFIRM_TIME))
                .ForMember(viewmodel => viewmodel.PlanListProdTime, options => options.MapFrom(entity => entity.PLAN_LIST_PRODCONFIRM_TIME))
                .ForMember(viewmodel => viewmodel.FinancialPostTime, options => options.MapFrom(entity => entity.FINANCIAL_POST_TIMES))
                .ForMember(viewmodel => viewmodel.FinancialPostNo, options => options.MapFrom(entity => entity.FINANCIAL_POST_NO))
                .ForMember(viewmodel => viewmodel.FinancialPostLatestTime, options => options.MapFrom(entity => entity.FINANCIAL_POST_LATEST_TIME))
                .ForMember(viewmodel => viewmodel.FinancialPostUploadTime, options => options.MapFrom(entity => entity.FINANCIAL_POST_UPLOAD_TIME))
                .ForMember(viewmodel => viewmodel.ImportTime, options => options.MapFrom(entity => entity.IMPORT_TIME))
                .ForMember(viewmodel => viewmodel.SapNo, options => options.MapFrom(entity => entity.SAP_NO))
                .ForMember(viewmodel => viewmodel.MinStorageTime, options => options.MapFrom(entity => entity.MIN_STORAGE_TIME))
                .ForMember(viewmodel => viewmodel.MaxStorageTime, options => options.MapFrom(entity => entity.MAX_STORAGE_TIME))
                .ForMember(viewmodel => viewmodel.Material, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_NAME))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Quantity, options => options.MapFrom(entity => entity.PLANNED_QUANTITY))
                .ForMember(viewmodel => viewmodel.Executing, options => options.MapFrom(entity => entity.ORDERED_QUANTITY))
                .ForMember(viewmodel => viewmodel.Finished, options => options.MapFrom(entity => entity.FINISHED_QUANTITY))
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.PLAN_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.PLAN_TYPE))
                .ForMember(viewmodel => viewmodel.Number, options => options.MapFrom(entity => entity.PLAN_CODE))
                .ForMember(viewmodel => viewmodel.Creater, options => options.MapFrom(entity => entity.PLAN_CREATER))
                .ForMember(viewmodel => viewmodel.CreateTime, options => options.MapFrom(entity => entity.PLAN_CREATE_TIME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.PLAN_REMARK))
                .ForMember(viewmodel => viewmodel.Plancode40, options => options.MapFrom(entity => entity.PLAN_CODE_40))
                .ForMember(viewmodel => viewmodel.Plancode99, options => options.MapFrom(entity => entity.PLAN_CODE_99))
                .ForMember(viewmodel => viewmodel.Platform, options => options.MapFrom(entity => entity.PLAN_PLATFORM))
                .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.PLAN_STATUS));

            CreateMap<V_PLAN_HIS_LIST, Order>()
                .IncludeBase<V_PLAN_LIST, Order>()
                .ForMember(viewmodel => viewmodel.FinalTime, options => options.MapFrom(entity => entity.PLAN_FINAL_TIME));

            CreateMap<V_STORAGE_MAIN, Stock>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.STORAGE_ID))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Full, options => options.MapFrom(entity => entity.FULL_FLAG))
                .ForMember(viewmodel => viewmodel.Quantity, options => options.MapFrom(entity => entity.STORAGE_LIST_COUNT))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Area, options => options.MapFrom(entity => entity.AREA_NAME))
                .ForMember(viewmodel => viewmodel.Cell, options => options.MapFrom(entity => entity.CELL_NAME))
                .ForMember(viewmodel => viewmodel.Position, options => options.MapFrom(entity => entity.CELL_TYPE));

            CreateMap<T_STORAGE_LIST, Application.Command.ViewModels.Stock.Line>()
               .ForMember(entity => entity.Id, options => options.MapFrom(model => model.STORAGE_LIST_ID))
               .ForMember(entity => entity.MainId, options => options.MapFrom(model => model.STORAGE_ID))
               .ForMember(entity => entity.Order, options => options.MapFrom(model => model.PLAN_LIST_ID))
               .ForMember(entity => entity.Material, options => options.MapFrom(model => model.GOODS_ID))
               .ForMember(entity => entity.Quantity, options => options.MapFrom(model => model.STORAGE_LIST_QUANTITY))
               .ForMember(entity => entity.InboundTime, options => options.MapFrom(model => model.INBOUND_TIME))
               .ForMember(entity => entity.InventoryTime, options => options.MapFrom(model => model.INVENTORY_TIME))
               .ForMember(entity => entity.AvailableTime, options => options.MapFrom(model => model.AVAILABLE_TIME))
               .ForMember(entity => entity.UnavailableTime, options => options.MapFrom(model => model.UNAVAILABLE_TIME))
               .ForMember(entity => entity.EffectiveTime, options => options.MapFrom(model => model.GOODS_EFF_TIME))
               .ForMember(entity => entity.ExpirationTime, options => options.MapFrom(model => model.GOODS_EXP_TIME))
               .ForMember(entity => entity.Enabled, options => options.MapFrom(model => model.STORAGE_LIST_FLAG))
               .ReverseMap();

            CreateMap<Stock, V_STORAGE_LIST>()
               .IncludeBase<MaterialProperty, Infrastructure.Database.Repository.Default.Entities.Abstract.V_GOODS_PROPERTY>()
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_ID, options => options.MapFrom(entity => entity.Id))
               .ForMember(viewmodel => viewmodel.STORAGE_ID, options => options.MapFrom(entity => entity.MainId))
               .ForMember(viewmodel => viewmodel.GOODS_ID, options => options.MapFrom(entity => entity.Material))
               .ForMember(viewmodel => viewmodel.GOODS_CODE, options => options.MapFrom(entity => entity.Code))
               .ForMember(viewmodel => viewmodel.GOODS_NAME, options => options.MapFrom(entity => entity.Name))
               .ForMember(viewmodel => viewmodel.CLASS_NAME, options => options.MapFrom(entity => entity.Category))
               .ForMember(viewmodel => viewmodel.TYPE_ID, options => options.MapFrom(entity => entity.Group))
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_QUANTITY, options => options.MapFrom(entity => entity.Quantity))
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_LOCK_QUANTITY, options => options.MapFrom(entity => entity.LockedQuantity))
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_UNLOCK_QUANTITY, options => options.MapFrom(entity => entity.UnlockedQuantity))
               .ForMember(viewmodel => viewmodel.INBOUND_TIME, options => options.MapFrom(entity => entity.InboundTime))
               .ForMember(viewmodel => viewmodel.INVENTORY_TIME, options => options.MapFrom(entity => entity.InventoryTime))
               .ForMember(viewmodel => viewmodel.AVAILABLE_TIME, options => options.MapFrom(entity => entity.AvailableTime))
               .ForMember(viewmodel => viewmodel.UNAVAILABLE_TIME, options => options.MapFrom(entity => entity.UnavailableTime))
               .ForMember(viewmodel => viewmodel.INVENTORY_AGE, options => options.MapFrom(entity => entity.InventoryAge))
               .ForMember(viewmodel => viewmodel.GOODS_AGE, options => options.MapFrom(entity => entity.MaterialAge))
               .ForMember(viewmodel => viewmodel.GOODS_EFF_TIME, options => options.MapFrom(entity => entity.EffectiveTime))
               .ForMember(viewmodel => viewmodel.GOODS_EXP_TIME, options => options.MapFrom(entity => entity.ExpirationTime))
               .ForMember(viewmodel => viewmodel.STOCK_BARCODE, options => options.MapFrom(entity => entity.Barcode))
               .ForMember(viewmodel => viewmodel.FULL_FLAG, options => options.MapFrom(entity => entity.Full))
               .ForMember(viewmodel => viewmodel.WAREHOUSE_NAME, options => options.MapFrom(entity => entity.Warehouse))
               .ForMember(viewmodel => viewmodel.AREA_NAME, options => options.MapFrom(entity => entity.Area))
               .ForMember(viewmodel => viewmodel.CELL_NAME, options => options.MapFrom(entity => entity.Cell))
               .ForMember(viewmodel => viewmodel.CELL_TYPE, options => options.MapFrom(entity => entity.Position))
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_FLAG, options => options.MapFrom(entity => entity.Enabled))
               .ForMember(viewmodel => viewmodel.STORAGE_LIST_REMARK, options => options.MapFrom(entity => entity.Remark))
               .ForMember(viewmodel => viewmodel.OVERDUE_TIME, options => options.MapFrom(entity => entity.Overdue));

            CreateMap<V_STORAGE_LIST, Stock>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.V_GOODS_PROPERTY, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.STORAGE_LIST_ID))
                .ForMember(viewmodel => viewmodel.MainId, options => options.MapFrom(entity => entity.STORAGE_ID))
                .ForMember(viewmodel => viewmodel.Material, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_NAME))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Quantity, options => options.MapFrom(entity => entity.STORAGE_LIST_QUANTITY))
                .ForMember(viewmodel => viewmodel.LockedQuantity, options => options.MapFrom(entity => entity.STORAGE_LIST_LOCK_QUANTITY))
                .ForMember(viewmodel => viewmodel.UnlockedQuantity, options => options.MapFrom(entity => entity.STORAGE_LIST_UNLOCK_QUANTITY))
                .ForMember(viewmodel => viewmodel.Managelistquantity, options => options.MapFrom(entity => entity.MANAGE_LIST_QUANTITY))
                .ForMember(viewmodel => viewmodel.InboundTime, options => options.MapFrom(entity => entity.INBOUND_TIME))
                .ForMember(viewmodel => viewmodel.InventoryTime, options => options.MapFrom(entity => entity.INVENTORY_TIME))
                .ForMember(viewmodel => viewmodel.AvailableTime, options => options.MapFrom(entity => entity.AVAILABLE_TIME))
                .ForMember(viewmodel => viewmodel.UnavailableTime, options => options.MapFrom(entity => entity.UNAVAILABLE_TIME))
                .ForMember(viewmodel => viewmodel.InventoryAge, options => options.MapFrom(entity => entity.INVENTORY_AGE))
                .ForMember(viewmodel => viewmodel.MaterialAge, options => options.MapFrom(entity => entity.GOODS_AGE))
                .ForMember(viewmodel => viewmodel.EffectiveTime, options => options.MapFrom(entity => entity.GOODS_EFF_TIME))
                .ForMember(viewmodel => viewmodel.ExpirationTime, options => options.MapFrom(entity => entity.GOODS_EXP_TIME))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Full, options => options.MapFrom(entity => entity.FULL_FLAG))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Area, options => options.MapFrom(entity => entity.AREA_NAME))
                .ForMember(viewmodel => viewmodel.Cell, options => options.MapFrom(entity => entity.CELL_NAME))
                .ForMember(viewmodel => viewmodel.Position, options => options.MapFrom(entity => entity.CELL_TYPE))
                .ForMember(viewmodel => viewmodel.Enabled, options => options.MapFrom(entity => entity.STORAGE_LIST_FLAG))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.STORAGE_LIST_REMARK))
                .ForMember(viewmodel => viewmodel.Overdue, options => options.MapFrom(entity => entity.OVERDUE_TIME));

            CreateMap<V_MANAGE_LIST, TaskStock>()
              //.IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.V_GOODS_PROPERTY, MaterialProperty>()
              .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.MANAGE_ID))
              .ForMember(viewmodel => viewmodel.StorageId, options => options.MapFrom(entity => entity.STORAGE_ID))
              .ForMember(viewmodel => viewmodel.StorageListId, options => options.MapFrom(entity => entity.STORAGE_LIST_ID))
              .ForMember(viewmodel => viewmodel.GoodsId, options => options.MapFrom(entity => entity.GOODS_ID))
              .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
              .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
              .ForMember(viewmodel => viewmodel.ManageListQuantity, options => options.MapFrom(entity => entity.MANAGE_LIST_QUANTITY))
              .ForMember(viewmodel => viewmodel.StorageListQuantity, options => options.MapFrom(entity => entity.STORAGE_LIST_QUANTITY))
              .ForMember(viewmodel => viewmodel.Original, options => options.MapFrom(entity => entity.START_CELL_NAME))
              .ForMember(viewmodel => viewmodel.Destination, options => options.MapFrom(entity => entity.END_CELL_NAME))
              .ForMember(viewmodel => viewmodel.BeginTime, options => options.MapFrom(entity => entity.BEGIN_TIME))
              .ForMember(viewmodel => viewmodel.EndTime, options => options.MapFrom(entity => entity.END_TIME))
              .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
              .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
              .ForMember(viewmodel => viewmodel.Batch, options => options.MapFrom(entity => entity.GOODS_BATCH_NO))
              .ForMember(viewmodel => viewmodel.Alias, options => options.MapFrom(entity => entity.GOODS_ALIAS))
              .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
              .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.MANAGE_STATUS))
              .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.MANAGE_REMARK));

            CreateMap<V_STORAGE_LOCK, Stock>()
                .IncludeBase<V_STORAGE_LIST, Stock>()
                .ForMember(viewmodel => viewmodel.Lock, options => options.MapFrom(entity => entity.LOCK_ID))
                .ForMember(viewmodel => viewmodel.Locker, options => options.MapFrom(entity => entity.LOCK_CODE))
                .ForMember(viewmodel => viewmodel.Order, options => options.MapFrom(entity => entity.PLAN_LIST_ID));

            //CreateMap<V_STORAGE_LOCK, Domain.Stock.Models.StockLine>()
            //    .IncludeBase<V_STORAGE_LIST, Domain.Stock.Models.Stock>()
            //    .ForMember(viewmodel => viewmodel.Lock, options => options.MapFrom(entity => entity.LOCK_ID))
            //    .ForMember(viewmodel => viewmodel.Order, options => options.MapFrom(entity => entity.PLAN_LIST_ID));

            CreateMap<V_STORAGE_SAFETY, Safety>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.T_GOODS_MAIN, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.SAFETY_ID))
                .ForMember(viewmodel => viewmodel.Material, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_ID))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(viewmodel => viewmodel.LowerLimit, options => options.MapFrom(entity => entity.LOWER_LIMIT))
                .ForMember(viewmodel => viewmodel.UpperLimit, options => options.MapFrom(entity => entity.UPPER_LIMIT))
                .ForMember(viewmodel => viewmodel.Quantity, options => options.MapFrom(entity => entity.STORAGE_QUANTITY))
                .ForMember(viewmodel => viewmodel.Overflow, options => options.MapFrom(entity => entity.OVERFLOW_PERCENT));

            CreateMap<V_RECORD_MAIN, Record>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.RECORD_ID))
                .ForMember(viewmodel => viewmodel.Transaction, options => options.MapFrom(entity => entity.RECORD_TYPE))
                .ForMember(viewmodel => viewmodel.Tag, options => options.MapFrom(entity => entity.RECORD_TAG))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Original, options => options.MapFrom(entity => entity.START_CELL_NAME))
                .ForMember(viewmodel => viewmodel.Destination, options => options.MapFrom(entity => entity.END_CELL_NAME))
                .ForMember(viewmodel => viewmodel.BeginTime, options => options.MapFrom(entity => entity.BEGIN_TIME))
                .ForMember(viewmodel => viewmodel.EndTime, options => options.MapFrom(entity => entity.END_TIME))
                .ForMember(viewmodel => viewmodel.Operator, options => options.MapFrom(entity => entity.USER_NAME))
                .ForMember(viewmodel => viewmodel.Account, options => options.MapFrom(entity => entity.USER_ACCOUNT));

            CreateMap<V_RECORD_LIST, Record>()
                .IncludeBase<Infrastructure.Database.Repository.Default.Entities.Abstract.V_GOODS_PROPERTY, MaterialProperty>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.RECORD_LIST_ID))
                .ForMember(viewmodel => viewmodel.Material, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.GOODS_CODE))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom(entity => entity.GOODS_NAME))
                .ForMember(viewmodel => viewmodel.Category, options => options.MapFrom(entity => entity.CLASS_NAME))
                .ForMember(viewmodel => viewmodel.Group, options => options.MapFrom(entity => entity.TYPE_ID))
                .ForMember(viewmodel => viewmodel.Quantity, options => options.MapFrom(entity => entity.RECORD_LIST_QUANTITY))
                .ForMember(viewmodel => viewmodel.InboundTime, options => options.MapFrom(entity => entity.INBOUND_TIME))
                .ForMember(viewmodel => viewmodel.AvailableTime, options => options.MapFrom(entity => entity.AVAILABLE_TIME))
                .ForMember(viewmodel => viewmodel.UnavailableTime, options => options.MapFrom(entity => entity.UNAVAILABLE_TIME))
                .ForMember(viewmodel => viewmodel.InventoryAge, options => options.MapFrom(entity => entity.INVENTORY_AGE))
                .ForMember(viewmodel => viewmodel.MaterialAge, options => options.MapFrom(entity => entity.GOODS_AGE))
                .ForMember(viewmodel => viewmodel.EffectiveTime, options => options.MapFrom(entity => entity.GOODS_EFF_TIME))
                .ForMember(viewmodel => viewmodel.ExpirationTime, options => options.MapFrom(entity => entity.GOODS_EXP_TIME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.RECORD_LIST_REMARK))
                .ForMember(viewmodel => viewmodel.Transaction, options => options.MapFrom(entity => entity.RECORD_TYPE))
                .ForMember(viewmodel => viewmodel.Tag, options => options.MapFrom(entity => entity.RECORD_TAG))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Original, options => options.MapFrom(entity => entity.START_CELL_NAME))
                .ForMember(viewmodel => viewmodel.Destination, options => options.MapFrom(entity => entity.END_CELL_NAME))
                .ForMember(viewmodel => viewmodel.BeginTime, options => options.MapFrom(entity => entity.BEGIN_TIME))
                .ForMember(viewmodel => viewmodel.EndTime, options => options.MapFrom(entity => entity.END_TIME))
                .ForMember(viewmodel => viewmodel.Operator, options => options.MapFrom(entity => entity.USER_NAME))
                .ForMember(viewmodel => viewmodel.Account, options => options.MapFrom(entity => entity.USER_ACCOUNT));

            CreateMap<V_MANAGE_MAIN, Task>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.MANAGE_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.MANAGE_TYPE))
                .ForMember(viewmodel => viewmodel.Tag, options => options.MapFrom(entity => entity.MANAGE_TAG))
                .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.MANAGE_STATUS))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_NAME))
                .ForMember(viewmodel => viewmodel.Original, options => options.MapFrom(entity => entity.START_CELL_NAME))
                .ForMember(viewmodel => viewmodel.Destination, options => options.MapFrom(entity => entity.END_CELL_NAME))
                .ForMember(viewmodel => viewmodel.Time, options => options.MapFrom(entity => entity.BEGIN_TIME))
                .ForMember(viewmodel => viewmodel.Priority, options => options.MapFrom(entity => entity.PRIORITY))
                .ForMember(viewmodel => viewmodel.Operator, options => options.MapFrom(entity => entity.USER_NAME))
                .ForMember(viewmodel => viewmodel.Account, options => options.MapFrom(entity => entity.USER_ACCOUNT))
                .ForMember(viewmodel => viewmodel.Previous, options => options.MapFrom(entity => entity.PREVIOUS_ID))
                .ForMember(viewmodel => viewmodel.Message, options => options.MapFrom(entity => entity.MANAGE_MSG))
                .ForMember(viewmodel => viewmodel.Planid, options => options.MapFrom(entity => entity.PLAN_ID))
                .ForMember(viewmodel => viewmodel.Plancode, options => options.MapFrom(entity => entity.PLAN_CODE))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.MANAGE_REMARK));

            CreateMap<V_MANAGE_TRIGGER, Trigger>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.TRIGGER_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.TRIGGER_TYPE))
                .ForMember(viewmodel => viewmodel.Warehouse, options => options.MapFrom(entity => entity.TRIGGER_WAREHOUSE))
                .ForMember(viewmodel => viewmodel.Device, options => options.MapFrom(entity => entity.TRIGGER_DEVICE))
                .ForMember(viewmodel => viewmodel.Parameter, options => options.MapFrom(entity => entity.TRIGGER_PARAM))
                .ForMember(viewmodel => viewmodel.Time, options => options.MapFrom(entity => entity.TRIGGER_TIME))
                .ForMember(viewmodel => viewmodel.Timeout, options => options.MapFrom(entity => entity.TRIGGER_TIMEOUT))
                .ForMember(viewmodel => viewmodel.Executed, options => options.MapFrom(entity => entity.EXEC_COUNT))
                .ForMember(viewmodel => viewmodel.Result, options => options.MapFrom(entity => entity.EXEC_RESULT))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom(entity => entity.EXEC_REMARK));

            CreateMap<T_DEVICE_MAIN, Device>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.DEVICE_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.DEVICE_TYPE))
                .ForMember(viewmodel => viewmodel.Driver, options => options.MapFrom(entity => entity.DEVICE_DRIVER))
                .ForMember(viewmodel => viewmodel.Parameter, options => options.MapFrom(entity => entity.DEVICE_PARAMS))
                .ForMember(viewmodel => viewmodel.Routes, options => options.MapFrom(entity => entity.DEVICE_ROUTES))
                .ForMember(viewmodel => viewmodel.Capacity, options => options.MapFrom(entity => entity.DEVICE_CAPACITY))
                .ForMember(viewmodel => viewmodel.Loaded, options => options.MapFrom(entity => entity.DEVICE_LOADED))
                .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.DEVICE_STATUS))
                .ForMember(viewmodel => viewmodel.Error, options => options.MapFrom(entity => entity.DEVICE_ERROR))
                .ForMember(viewmodel => viewmodel.Instruction, options => options.MapFrom(entity => entity.DEVICE_TASK))
                .ForMember(viewmodel => viewmodel.Locked, options => options.MapFrom(entity => entity.DEVICE_LOCK))
                .ForMember(viewmodel => viewmodel.Data, options => options.MapFrom(entity => entity.DEVICE_DATA))
                .ForMember(viewmodel => viewmodel.Enabled, options => options.MapFrom(entity => entity.DEVICE_FLAG))
                .ForMember(viewmodel => viewmodel.Timestamp, options => options.MapFrom(entity => entity.DEVICE_STATUS_TIME));

            CreateMap<T_DEVICE_TASK, Instruction>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.TASK_ID))
                .ForMember(viewmodel => viewmodel.Task, options => options.MapFrom(entity => entity.TASK_GROUP))
                .ForMember(viewmodel => viewmodel.Barcode, options => options.MapFrom(entity => entity.TASK_BARCODE))
                .ForMember(viewmodel => viewmodel.Priority, options => options.MapFrom(entity => entity.TASK_LEVEL))
                .ForMember(viewmodel => viewmodel.Index, options => options.MapFrom(entity => entity.TASK_ORDER))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom(entity => entity.TASK_TYPE))
                .ForMember(viewmodel => viewmodel.Original, options => options.MapFrom(entity => entity.TASK_START))
                .ForMember(viewmodel => viewmodel.OriginalDevice, options => options.MapFrom(entity => entity.TASK_START_DEVICE))
                .ForMember(viewmodel => viewmodel.OriginalCoordinate, options => options.MapFrom(entity => entity.TASK_START_COORD))
                .ForMember(viewmodel => viewmodel.Destination, options => options.MapFrom(entity => entity.TASK_END))
                .ForMember(viewmodel => viewmodel.DestinationDevice, options => options.MapFrom(entity => entity.TASK_END_DEVICE))
                .ForMember(viewmodel => viewmodel.DestinationCoordinate, options => options.MapFrom(entity => entity.TASK_END_COORD))
                .ForMember(viewmodel => viewmodel.Parameters, options => options.MapFrom(entity => entity.TASK_PARAMS))
                .ForMember(viewmodel => viewmodel.Route, options => options.MapFrom(entity => entity.TASK_ROUTE))
                .ForMember(viewmodel => viewmodel.Number, options => options.MapFrom(entity => entity.TASK_NUMBER))
                .ForMember(viewmodel => viewmodel.State, options => options.MapFrom(entity => entity.TASK_STATUS))
                .ForMember(viewmodel => viewmodel.Error, options => options.MapFrom(entity => entity.TASK_ERROR))
                .ForMember(viewmodel => viewmodel.GeneratedTime, options => options.MapFrom(entity => entity.TASK_TIME))
                .ForMember(viewmodel => viewmodel.StartedTime, options => options.MapFrom(entity => entity.START_TIME))
                .ForMember(viewmodel => viewmodel.CompletedTime, options => options.MapFrom(entity => entity.END_TIME));

            CreateMap<T_DEVICE_ERROR, Error>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.ERROR_ID))
                .ForMember(viewmodel => viewmodel.Device, options => options.MapFrom(entity => entity.ERROR_DEVICE))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom(entity => entity.ERROR_CODE))
                .ForMember(viewmodel => viewmodel.StartTime, options => options.MapFrom(entity => entity.ERROR_START))
                .ForMember(viewmodel => viewmodel.EndTime, options => options.MapFrom(entity => entity.ERROR_END))
                .ForMember(viewmodel => viewmodel.Duration, options => options.MapFrom(entity => entity.ERROR_DURATION));

            //NEW mapper FOR KSF
            CreateMap<dynamic, Operation>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.OPERATION_ID))
                .ForMember(viewmodel => viewmodel.Station, options => options.MapFrom((entity, _) => entity.OPERATION_STATION))
                .ForMember(viewmodel => viewmodel.Time, options => options.MapFrom((entity, _) => entity.OPERATION_TIME))
                .ForMember(viewmodel => viewmodel.Opprocess, options => options.MapFrom((entity, _) => entity.OPERATION_PROCESS))
                .ForMember(viewmodel => viewmodel.Opfunction, options => options.MapFrom((entity, _) => entity.OPERATION_FUNCTION))
                .ForMember(viewmodel => viewmodel.Opmessage, options => options.MapFrom((entity, _) => entity.OPERATION_MESSAGE))
                .ForMember(viewmodel => viewmodel.IO, options => options.MapFrom((entity, _) => entity.OPERATION_IO))
                .ForMember(viewmodel => viewmodel.Orderseq, options => options.MapFrom((entity, _) => entity.OPERATION_SEQUENCE))
                .ForMember(viewmodel => viewmodel.Operator, options => options.MapFrom((entity, _) => entity.USER_ID == null ? null : new User { Id = entity.USER_ID, Name = entity.USER_NAME, Avatar = entity.USER_AVATAR }));

            CreateMap<T_SYS_CLIENT_CATE, ClientCate>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.CLIENTCATE_ID))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom((entity, _) => entity.CLIENTCATE_NAME))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom((entity, _) => entity.CLIENTCATE_CODE))
                .ForMember(viewmodel => viewmodel.BeginDateCount, options => options.MapFrom((entity, _) => entity.CLIENTCATE_BEGIN_DATE_COUNT))
                .ForMember(viewmodel => viewmodel.FinalDateCount, options => options.MapFrom((entity, _) => entity.CLIENTCATE_FINAL_DATE_COUNT))
                .ForMember(viewmodel => viewmodel.Model, options => options.MapFrom((entity, _) => entity.CLIENTCATE_MODEL));

            CreateMap<dynamic, ClientInfo>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.CLIENTINFO_ID))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom((entity, _) => entity.CLIENTINFO_NAME))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom((entity, _) => entity.CLIENTINFO_CODE))
                .ForMember(viewmodel => viewmodel.ShortName, options => options.MapFrom((entity, _) => entity.CLIENTINFO_SHORTNAME))
                .ForMember(viewmodel => viewmodel.ClientCateId, options => options.MapFrom((entity, _) => entity.CLIENTCATE_ID))
                .ForMember(viewmodel => viewmodel.ClientCateCode, options => options.MapFrom((entity, _) => entity.CLIENTCATE_CODE))
                .ForMember(viewmodel => viewmodel.ClientCateName, options => options.MapFrom((entity, _) => entity.CLIENTCATE_NAME))
                .ForMember(viewmodel => viewmodel.BeginDateCount, options => options.MapFrom((entity, _) => entity.CLIENTCATE_BEGIN_DATE_COUNT))
                .ForMember(viewmodel => viewmodel.FinalDateCount, options => options.MapFrom((entity, _) => entity.CLIENTCATE_FINAL_DATE_COUNT))
                .ForMember(viewmodel => viewmodel.Model, options => options.MapFrom((entity, _) => entity.CLIENTCATE_MODEL));

            CreateMap<T_SYS_PLATFORM, Platform>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.PLATFORM_ID))
                .ForMember(viewmodel => viewmodel.Name, options => options.MapFrom((entity, _) => entity.PLATFORM_NAME))
                .ForMember(viewmodel => viewmodel.Code, options => options.MapFrom((entity, _) => entity.PLATFORM_CODE))
                .ForMember(viewmodel => viewmodel.Status, options => options.MapFrom((entity, _) => entity.PLATFORM_STATUS));

            CreateMap<IO_CONTROL, Control>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.CONTROL_ID))
                .ForMember(viewmodel => viewmodel.RelativeControlId, options => options.MapFrom((entity, _) => entity.RELATIVE_CONTROL_ID))
                .ForMember(viewmodel => viewmodel.ManageId, options => options.MapFrom((entity, _) => entity.MANAGE_ID))
                .ForMember(viewmodel => viewmodel.Stockbarcode, options => options.MapFrom((entity, _) => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom((entity, _) => entity.CONTROL_TASK_TYPE))
                .ForMember(viewmodel => viewmodel.Level, options => options.MapFrom((entity, _) => entity.CONTROL_TASK_LEVEL))
                .ForMember(viewmodel => viewmodel.StartWarehouseCode, options => options.MapFrom((entity, _) => entity.START_WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.StartDeviceCode, options => options.MapFrom((entity, _) => entity.START_DEVICE_CODE))
                .ForMember(viewmodel => viewmodel.EndWarehouseCode, options => options.MapFrom((entity, _) => entity.END_WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.EndDeviceCode, options => options.MapFrom((entity, _) => entity.END_DEVICE_CODE))
                .ForMember(viewmodel => viewmodel.PreStatus, options => options.MapFrom((entity, _) => entity.PRE_CONTROL_STATUS))
                .ForMember(viewmodel => viewmodel.Status, options => options.MapFrom((entity, _) => entity.CONTROL_STATUS))
                .ForMember(viewmodel => viewmodel.ErrorText, options => options.MapFrom((entity, _) => entity.ERROR_TEXT))
                .ForMember(viewmodel => viewmodel.BeginTime, options => options.MapFrom((entity, _) => entity.CONTROL_BEGIN_TIME))
                .ForMember(viewmodel => viewmodel.EndTime, options => options.MapFrom((entity, _) => entity.CONTROL_END_TIME))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom((entity, _) => entity.CONTROL_REMARK));

            CreateMap<IO_CONTROL_APPLY, Apply>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_ID))
                .ForMember(viewmodel => viewmodel.ControlId, options => options.MapFrom((entity, _) => entity.CONTROL_ID))
                .ForMember(viewmodel => viewmodel.Type, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_TYPE))
                .ForMember(viewmodel => viewmodel.WarehouseCode, options => options.MapFrom((entity, _) => entity.WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.DeviceCode, options => options.MapFrom((entity, _) => entity.DEVICE_CODE))
                .ForMember(viewmodel => viewmodel.Stockbarcode, options => options.MapFrom((entity, _) => entity.STOCK_BARCODE))
                .ForMember(viewmodel => viewmodel.Status, options => options.MapFrom((entity, _) => entity.APPLY_TASK_STATUS))
                .ForMember(viewmodel => viewmodel.CreateTime, options => options.MapFrom((entity, _) => entity.CREATE_TIME))
                .ForMember(viewmodel => viewmodel.Parameter, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_PARAMETER))
                .ForMember(viewmodel => viewmodel.Remark, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_REMARK))
                .ForMember(viewmodel => viewmodel.Para01, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_PARA01))
                .ForMember(viewmodel => viewmodel.Para02, options => options.MapFrom((entity, _) => entity.CONTROL_APPLY_PARA02));

            CreateMap<T_MANAGE_LIST, ManageList>()
                .ForMember(viewmodel => viewmodel.Id, options => options.MapFrom(entity => entity.MANAGE_LIST_ID))
                .ForMember(viewmodel => viewmodel.ManageId, options => options.MapFrom(entity => entity.MANAGE_ID))
                .ForMember(viewmodel => viewmodel.PlanListId, options => options.MapFrom(entity => entity.PLAN_LIST_ID))
                .ForMember(viewmodel => viewmodel.GoodsId, options => options.MapFrom(entity => entity.GOODS_ID))
                .ForMember(viewmodel => viewmodel.PlannedQuantity, options => options.MapFrom(entity => entity.PLANNED_QUANTITY))
                .ForMember(viewmodel => viewmodel.OrderedQuantity, options => options.MapFrom(entity => entity.ORDERED_QUANTITY))
                .ForMember(viewmodel => viewmodel.FinishedQuantity, options => options.MapFrom(entity => entity.FINISHED_QUANTITY))
                .ForMember(viewmodel => viewmodel.GoodsBatchNo, options => options.MapFrom(entity => entity.GOODS_BATCH_NO))
                .ForMember(viewmodel => viewmodel.GoodsBillNo, options => options.MapFrom(entity => entity.GOODS_BILL_NO))
                .ForMember(viewmodel => viewmodel.GoodsSupplier, options => options.MapFrom(entity => entity.GOODS_SUPPLIER))
                .ForMember(viewmodel => viewmodel.GoodsMFG, options => options.MapFrom(entity => entity.GOODS_MFG))
                .ForMember(viewmodel => viewmodel.GoodsQCState, options => options.MapFrom(entity => entity.GOODS_QC_STATE))
                .ForMember(viewmodel => viewmodel.ManageListFlag, options => options.MapFrom(entity => entity.MANAGE_LIST_FLAG))
                .ForMember(viewmodel => viewmodel.ManageListRemark, options => options.MapFrom(entity => entity.MANAGE_LIST_REMARK))
                .ForMember(viewmodel => viewmodel.ManageListREPOConfirm, options => options.MapFrom(entity => entity.MANAGE_LIST_REPOCONFIRM))
                .ForMember(viewmodel => viewmodel.ManageListPRODConfirm, options => options.MapFrom(entity => entity.MANAGE_LIST_PRODCONFIRM))
                .ForMember(viewmodel => viewmodel.CreateTime, options => options.MapFrom(entity => entity.CREATE_TIME))
                .ForMember(viewmodel => viewmodel.UpdateTime, options => options.MapFrom(entity => entity.UPDATE_TIME))
                .ForMember(viewmodel => viewmodel.PlantCode, options => options.MapFrom(entity => entity.PLANT_CODE))
                .ForMember(viewmodel => viewmodel.ProductionLine, options => options.MapFrom(entity => entity.PRODUCTION_LINE))
                .ForMember(viewmodel => viewmodel.ClientCode, options => options.MapFrom(entity => entity.CLIENT_CODE))
                .ForMember(viewmodel => viewmodel.warehouseCode, options => options.MapFrom(entity => entity.WAREHOUSE_CODE))
                .ForMember(viewmodel => viewmodel.FinancialPostStatus, options => options.MapFrom(entity => entity.FINANCIAL_POST_STATUS))
                .ForMember(viewmodel => viewmodel.TruckInfo, options => options.MapFrom(entity => entity.TRUCK_INFO))
                .ForMember(viewmodel => viewmodel.TransferType, options => options.MapFrom(entity => entity.TRANSFER_TYPE))
                .ForMember(viewmodel => viewmodel.WorkGroup, options => options.MapFrom(entity => entity.WORK_GROUP))
                .ForMember(viewmodel => viewmodel.AdjustQuantity, options => options.MapFrom(entity => entity.ADJUST_QUANTITY))
                .ForMember(viewmodel => viewmodel.AdjustReason, options => options.MapFrom(entity => entity.ADJUST_REASON))
                .ForMember(viewmodel => viewmodel.FinancialPostQuantity, options => options.MapFrom(entity => entity.FINANCIAL_POST_QUANTITY))
                .ForMember(viewmodel => viewmodel.ManageListREPOConfirmTime, options => options.MapFrom(entity => entity.MANAGE_LIST_REPOCONFIRM_TIME))
                .ForMember(viewmodel => viewmodel.ManageListPRODConfirmTime, options => options.MapFrom(entity => entity.MANAGE_LIST_PRODCONFIRM_TIME))
                .ForMember(viewmodel => viewmodel.FinancialPostTimeS, options => options.MapFrom(entity => entity.FINANCIAL_POST_TIMES))
                .ForMember(viewmodel => viewmodel.FinancialPostNo, options => options.MapFrom(entity => entity.FINANCIAL_POST_NO))
                .ForMember(viewmodel => viewmodel.FinancialPostLatestTime, options => options.MapFrom(entity => entity.FINANCIAL_POST_LATEST_TIME))
                .ForMember(viewmodel => viewmodel.FinancialPostUploadTime, options => options.MapFrom(entity => entity.FINANCIAL_POST_UPLOAD_TIME))
                .ForMember(viewmodel => viewmodel.ImportTime, options => options.MapFrom(entity => entity.IMPORT_TIME))
                .ForMember(viewmodel => viewmodel.SapNo, options => options.MapFrom(entity => entity.SAP_NO))
                .ForMember(viewmodel => viewmodel.ManageListQuantity, options => options.MapFrom(entity => entity.MANAGE_LIST_QUANTITY));
            CreateMap<T_PDA_PRINTER_IP, Printer>()
               .ForMember(viewmodel => viewmodel.PdaIp, options => options.MapFrom(entity => entity.PDA_IP))
               .ForMember(viewmodel => viewmodel.PrinterIp, options => options.MapFrom(entity => entity.PRINTER_IP))
               .ForMember(viewmodel => viewmodel.PrinterName, options => options.MapFrom(entity => entity.PRINTER_NAME));

        }
    }

    /// <summary>
    /// AutoMapper 扩展方法
    /// </summary>
    internal static class AutoMapperExtension
    {
        /// <summary>
        /// 获取映射表达式
        /// </summary>
        /// <typeparam name="TSource">源类型</typeparam>
        /// <typeparam name="TDestination">目标类型</typeparam>
        /// <param name="mapper">映射</param>
        /// <param name="property">属性</param>
        /// <returns>Lambda 表达式</returns>
        internal static Expression<Func<TSource, object>> GetPropertyMapExpression<TSource, TDestination>(this IMapper mapper, string property)
        {
            if (mapper.ConfigurationProvider is global::AutoMapper.Internal.IGlobalConfiguration configuration)
            {
                var map = configuration.FindTypeMapFor<TSource, TDestination>().PropertyMaps.FirstOrDefault(p => p.DestinationName.Equals(property, StringComparison.OrdinalIgnoreCase));
                if (map?.CustomMapExpression != null)
                {
                    return Expression.Lambda<Func<TSource, object>>(Expression.Convert(map.CustomMapExpression.Body, typeof(object)), map.CustomMapExpression.Parameters);
                }
            }
            return null;
        }
    }
}
