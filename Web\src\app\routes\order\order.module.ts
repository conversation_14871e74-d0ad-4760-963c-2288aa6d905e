import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { OrderRoutingModule } from './order-routing.module';
import { TemplatesModule } from '../.templates/templates.module';
import { TodoComponent } from './todo/todo.component';
import { TodoEditComponent } from './todo/edit/edit.component';
import { TodoEditLineComponent } from './todo/edit/line/line.component';
import { TargetComponent } from './todo/target/target.component';
import { TodoOutComponent } from './todo-out/todo-out.component';
import { FilterComponent } from './todo-out/filter/filter.component';
// import { EditComponent } from './todo-out/edit/edit.component';
import { BindplatformComponent } from './todo-out/bindplatform/bindplatform.component';
import { PrintComponent } from './todo-out/print/print.component';


@NgModule({
  declarations: [
    TodoComponent,
    TodoEditComponent,
    TodoEditLineComponent,
    TargetComponent,
    TodoOutComponent,
    FilterComponent,
    // EditComponent,
    BindplatformComponent,
    PrintComponent
  ],
  imports: [
    SharedModule,
    OrderRoutingModule,
    TemplatesModule
  ]
})
export class OrderModule { }
