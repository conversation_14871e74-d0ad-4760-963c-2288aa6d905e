﻿using AutoMapper;
using Kean.Application.Query.Interfaces;
using Kean.Application.Query.ViewModels;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Kean.Application.Query.Implements
{
    /// <summary>
    /// 设备信息查询服务实现
    /// </summary>
    public sealed class DeviceService(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : IDeviceService
    {
        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetDeviceCount 方法
         */
        public async Task<int> GetDeviceCount(string id, string type, string state)
        {
            return (int)(await GetDeviceSchema(id, type, state)
                .Single(d => new { Count = Function.Count(d.DEVICE_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetDeviceList 方法
         */
        public async Task<IEnumerable<Device>> GetDeviceList(string id, string type, string state, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Device>>(await GetDeviceSchema(id, type, state)
                .Sort<T_DEVICE_MAIN, Device>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetTask 相关方法的条件
         */
        private ISchema<T_DEVICE_MAIN> GetDeviceSchema(string id, string type, string state)
        {
            var schema = database.From<T_DEVICE_MAIN>();
            if (id != null)
            {
                schema = schema.Where(d => d.DEVICE_ID == id);
            }
            if (type != null)
            {
                schema = schema.Where(d => d.DEVICE_TYPE == type);
            }
            if (state != null)
            {
                schema = schema.Where(d => d.DEVICE_STATUS == state);
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetDeviceCount 方法
         */
        public async Task<int> GetInstructionCount(int? number, int? task, string device, string state, string barcode, DateTime? generatedFrom, DateTime? generatedTo)
        {
            return (int)(await GetInstructionSchema(number, task, device, state, barcode, generatedFrom, generatedTo)
                .Single(i => new { Count = Function.Count(i.TASK_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetDeviceList 方法
         */
        public async Task<IEnumerable<Instruction>> GetInstructionList(int? number, int? task, string device, string state, string barcode, DateTime? generatedFrom, DateTime? generatedTo, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Instruction>>(await GetInstructionSchema(number, task, device, state, barcode, generatedFrom, generatedTo)
                .Sort<T_DEVICE_TASK, Instruction>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetTask 相关方法的条件
         */
        private ISchema<T_DEVICE_TASK> GetInstructionSchema(int? number, int? task, string device, string state, string barcode, DateTime? generatedFrom, DateTime? generatedTo)
        {
            var schema = database.From<T_DEVICE_TASK>();
            if (number.HasValue)
            {
                schema = schema.Where(i => i.TASK_NUMBER == number.Value);
            }
            if (task.HasValue)
            {
                schema = schema.Where(i => i.TASK_GROUP == task.Value);
            }
            if (device != null)
            {
                schema = schema.Where(i => i.TASK_START_DEVICE == device);
            }
            if (state != null)
            {
                schema = schema.Where(i => i.TASK_STATUS == state);
            }
            if (barcode != null)
            {
                schema = schema.Where(i => i.TASK_BARCODE.Contains(barcode));
            }
            if (generatedFrom.HasValue)
            {
                schema = schema.Where(i => i.TASK_TIME >= generatedFrom.Value);
            }
            if (generatedTo.HasValue)
            {
                schema = schema.Where(i => i.TASK_TIME <= generatedTo.Value);
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetErrorCount 方法
         */
        public async Task<int> GetErrorCount(string device, int? code, DateTime? startFrom, DateTime? startTo, DateTime? endFrom, DateTime? endTo)
        {
            return (int)(await GetErrorSchema(device, code, startFrom, startTo, endFrom, endTo)
                .Single(e => new { Count = Function.Count(e.ERROR_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IDeviceService.GetErrorList 方法
         */
        public async Task<IEnumerable<Error>> GetErrorList(string device, int? code, DateTime? startFrom, DateTime? startTo, DateTime? endFrom, DateTime? endTo, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Error>>(await GetErrorSchema(device, code, startFrom, startTo, endFrom, endTo)
                .Sort<T_DEVICE_ERROR, Error>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetError 相关方法的条件
         */
        private ISchema<T_DEVICE_ERROR> GetErrorSchema(string device, int? code, DateTime? startFrom, DateTime? startTo, DateTime? endFrom, DateTime? endTo)
        {
            var schema = database.From<T_DEVICE_ERROR>();
            if (device != null)
            {
                schema = schema.Where(e => e.ERROR_DEVICE == device);
            }
            if (code.HasValue)
            {
                schema = schema.Where(e => e.ERROR_CODE == code);
            }
            if (startFrom.HasValue)
            {
                schema = schema.Where(e => e.ERROR_START >= startFrom.Value);
            }
            if (startTo.HasValue)
            {
                schema = schema.Where(e => e.ERROR_START <= startTo.Value);
            }
            if (endFrom.HasValue)
            {
                schema = schema.Where(e => e.ERROR_END >= endFrom.Value);
            }
            if (endTo.HasValue)
            {
                schema = schema.Where(e => e.ERROR_END <= endTo.Value);
            }
            return schema;
        }
        public async Task<IEnumerable<Printer>> GetPrinterIpList(string pdaip, string printerip, string printername, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Printer>>(await GetPrinterIpSchema(pdaip, printerip, printername)
                .Sort<T_PDA_PRINTER_IP, Printer>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetPrinterIpList 相关方法的条件
         */
        private ISchema<T_PDA_PRINTER_IP> GetPrinterIpSchema(string pdaip, string printerip, string printername)
        {
            var schema = database.From<T_PDA_PRINTER_IP>();
            if (pdaip != null)
            {
                schema = schema.Where(p => p.PDA_IP == pdaip);
            }
            if (printerip != null)
            {
                schema = schema.Where(p => p.PRINTER_IP == printerip);
            }
            if (printername != null)
            {
                schema = schema.Where(p => p.PRINTER_NAME == printername);
            }
            return schema;
        }

    }
}
