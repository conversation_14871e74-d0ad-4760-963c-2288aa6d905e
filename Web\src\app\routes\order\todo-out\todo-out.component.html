<div class="grid">
    <div class="row-auto">
        <mat-toolbar class="app-background-card">


            <!-- <div class="mat-tree-node" style="min-height: 30px;">
                <mat-checkbox (change)="toggle('half')" [checked]="half">{{
                    "routes.stock.todooutbound.title.half" | translate }}</mat-checkbox>
                <mat-checkbox style="margin-left: 20px;" (change)="toggle('full')" [checked]="full">{{
                    "routes.stock.todooutbound.title.full"
                    | translate
                    }}</mat-checkbox>
            </div>-->
            <input [(ngModel)]="planCode204099" (keydown.enter)="getPlanInfo(planCode204099)" [disabled]="!planlistsheet"
                style="border: 2px,solid;margin-left: 20px; "
                placeholder="{{ 'routes.stock.todooutbound.title.placeholder' | translate }}">

            <button mat-icon-button color="secondary" matTooltip="{{ 'shared.operation.filter' | translate }}"
                (click)="getPlanInfo(planCode204099)" [disabled]="!planlistsheet">
                <mat-icon>search</mat-icon>
            </button>


            <div style="width: 35%;float: right;">

                <span>
                    <button mat-button color="primary" *ngIf="half" disabled>{{
                        'routes.stock.todooutbound.title.half' | translate
                        }}</button>


                </span>
                <span>
                    <button mat-button color="primary" *ngIf="full" disabled>{{
                        'routes.stock.todooutbound.title.full' |
                        translate
                        }}</button>


                </span>
                <span>
                    <button mat-button color="primary" *ngIf="recieveTime" disabled>指定允收天数：{{
                        recieveTime
                        }}天</button>


                </span>
                <span>
                    <button mat-button color="primary" *ngIf="ignoreNullStorage" disabled>{{
                        'routes.stock.todooutbound.title.ignoreNullStorage' | translate
                        }}</button>


                </span>
                <span>
                    <button mat-button color="primary" *ngIf="overFullPallet" disabled>{{
                        'routes.stock.todooutbound.title.overFullPallet' | translate
                        }}</button>


                </span>


                <span>
                    <button mat-button color="primary" *ngIf="crane" disabled>指定吊车：{{crane.CRANE_NAME}}</button>


                </span>

            </div>


            <button mat-button color="primary" (click)="convertSheet(true)" [disabled]=planlistsheet id="buttonPlan"
                style="background-color: gray; color: black;" *ngIf="buttonshow">{{
                'routes.stock.todooutbound.sheet.planlist' |
                translate | uppercase }}</button>
            <button mat-button color="primary" (click)="convertSheet(false)" [disabled]=!planlistsheet
                id="buttonStorage" *ngIf="buttonshow">{{
                'routes.stock.todooutbound.sheet.storagelist'
                | translate | uppercase }}</button>
            <span class="spacer"></span>
            <button mat-button color="primary" (click)="setfilter()"
                [disabled]='!planlistsheet||(planlistsheet&&selection.selected.length==0)'>{{
                'shared.operation.filterrules.button' | translate }}</button>
            <button mat-button color="primary" (click)="platform(planCode204099)"
                [disabled]='!(planCode204099&&rows.data.length>0)'>{{
                'routes.stock.todooutbound.function.platform.title'
                |
                translate | uppercase }}</button>

            <button mat-button color="primary" (click)="outbound()"
                [disabled]="planlistsheet||selectionP.length == 0 || (selection.selected.length == 0&&!planlistsheet)">{{
                'routes.stock.todooutbound.function.outbound'
                |
                translate | uppercase }}</button>
            <button mat-button color="primary" (click)="testButton(1)" [disabled]="planlistsheet||selectionP.length == 0 || (selection.selected.length == 0&&!planlistsheet)">{{
                '平置仓配零'}}</button>
            <!--<button mat-button color="primary" (click)="testButton(2)">{{
                'routes.stock.todooutbound.function.batchprepare'
                | translate | uppercase }}</button>-->
            <button mat-button color="primary" [disabled]="rows.data.length == 0" [loading]="loading"
                (click)="reset()">{{ 'routes.stock.inbound.reset' | translate | uppercase }}</button>


        </mat-toolbar>
        <mat-divider></mat-divider>
    </div>
    <div class="row-fill">
        <table mat-table [dataSource]="rows" matSort>
            <ng-container matColumnDef="check">
                <th mat-header-cell *matHeaderCellDef style="width: 24px; padding-right: 24px;">
                    <table-row-selection [model]="selection" [data]="rows.data"></table-row-selection>
                </th>
                <td mat-cell *matCellDef="let row" style="padding-right: 24px;">
                    <table-row-selection [model]="selection" [data]="row"></table-row-selection>
                </td>
            </ng-container>
            <ng-container [matColumnDef]="item.id" [ngSwitch]="item.id" *ngFor="let item of columnsD"
                [stickyEnd]="item.id=='operation'">
                <ng-container *ngSwitchCase="'manufacturingDate'">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{ row.manufacturingDate | date:'yyyy-MM-dd' }} </td>
                </ng-container>
                <ng-container *ngSwitchCase="'qualityState'">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{ 'routes.material.enum.qs.' + row.qualityState | translate }}
                    </td>
                </ng-container>
                <ng-container *ngSwitchCase="'managelistquantity'">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row"> <a class="qty"
                            [ngClass]="{ 'app-foreground-primary': row.managelistquantity }" (click)="edit(row)">{{
                            row.managelistquantityEdited
                            }}</a> /{{ row.managelistquantity }}
                    </td>
                </ng-container>
                <ng-container *ngSwitchCase="'enabled'">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row">
                        {{'routes.material.enum.enabled.' + row.enabled | translate}}
                        <span *ngIf="row.enabled == 'false' && row.remark">（{{row.remark}}）</span>
                    </td>
                </ng-container>
                <ng-container *ngSwitchCase="'operation'">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row">
                        <button mat-icon-button matTooltip="{{ 'shared.operation.edit' | translate }}"
                            (click)="edit(row)">
                            <mat-icon>edit</mat-icon>
                        </button>
                    </td>
                </ng-container>
                <ng-container *ngSwitchCase="'operation1'">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row">
                        <button mat-icon-button matTooltip="{{ 'shared.operation.print' | translate }}"
                            (click)="print(row)">
                            <mat-icon>print</mat-icon>
                        </button>
                    </td>
                </ng-container>
                <ng-container *ngSwitchCase="'remark'">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row">{{ row.remark=="LK"?"立库出库":"平置仓配零" }}</td>
                </ng-container>
                <ng-container *ngSwitchDefault>
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ item.header | translate }}</th>
                    <td mat-cell *matCellDef="let row">{{ row[item.id] }}</td>
                </ng-container>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="columns | field:'id'; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: columns | field:'id';"
                [ngClass]="{'app-background-primary-lighter': colorful && row.enabled == 'true', 'app-background-accent-lighter': colorful && row.enabled == 'false'}">
            </tr>
            <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell no-data" [attr.colspan]="columns.length">{{ 'routes.stock.inbound.emptytodo' |
                    translate
                    }}</td>
            </tr>
        </table>
    </div>
</div>

<ng-template #$qty let-row>
    <a class="qty" [ngClass]="{ 'app-foreground-primary': row.quantity }" (click)="edit(row)">{{ row.quantity }}</a> /
    {{ row.remaining }}

</ng-template>