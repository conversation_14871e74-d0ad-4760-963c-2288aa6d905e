﻿using Kean.Application.Query.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Kean.Application.Query.Interfaces
{
    /// <summary>
    /// 表示设备信息查询服务
    /// </summary>
    public interface IDeviceService
    {
        /// <summary>
        /// 获取设备数量
        /// </summary>
        /// <param name="id">设备号</param>
        /// <param name="type">类型</param>
        /// <param name="state">状态</param>
        /// <returns>结果</returns>
        Task<int> GetDeviceCount(string id, string type, string state);

        /// <summary>
        /// 获取设备列表
        /// </summary>
        /// <param name="id">设备号</param>
        /// <param name="type">类型</param>
        /// <param name="state">状态</param>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Device>> GetDeviceList(string id, string type, string state, string sort, int? offset, int? limit);

        /// <summary>
        /// 获取指令数量
        /// </summary>
        /// <param name="number">指令号</param>
        /// <param name="task">任务号</param>
        /// <param name="device">设备号</param>
        /// <param name="state">状态</param>
        /// <param name="barcode">条码</param>
        /// <param name="generatedFrom">时间头</param>
        /// <param name="generatedTo">时间尾</param>
        /// <returns>结果</returns>
        Task<int> GetInstructionCount(int? number, int? task, string device, string state, string barcode, DateTime? generatedFrom, DateTime? generatedTo);

        /// <summary>
        /// 获取指令列表
        /// </summary>
        /// <param name="number">指令号</param>
        /// <param name="task">任务号</param>
        /// <param name="device">设备号</param>
        /// <param name="state">状态</param>
        /// <param name="barcode">条码</param>
        /// <param name="generatedFrom">时间头</param>
        /// <param name="generatedTo">时间尾</param>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Instruction>> GetInstructionList(int? number, int? task, string device, string state, string barcode, DateTime? generatedFrom, DateTime? generatedTo, string sort, int? offset, int? limit);

        /// <summary>
        /// 获取故障数量
        /// </summary>
        /// <param name="device">设备号</param>
        /// <param name="code">故障码</param>
        /// <param name="startFrom">发生时间头</param>
        /// <param name="startTo">发生时间尾</param>
        /// <param name="endFrom">消除时间头</param>
        /// <param name="endTo">消除时间尾</param>
        /// <returns>结果</returns>
        Task<int> GetErrorCount(string device, int? code, DateTime? startFrom, DateTime? startTo, DateTime? endFrom, DateTime? endTo);

        /// <summary>
        /// 获取故障列表
        /// </summary>
        /// <param name="device">设备号</param>
        /// <param name="code">故障码</param>
        /// <param name="startFrom">发生时间头</param>
        /// <param name="startTo">发生时间尾</param>
        /// <param name="endFrom">消除时间头</param>
        /// <param name="endTo">消除时间尾</param>
        /// <param name="sort">排序</param>
        /// <param name="offset">偏移</param>
        /// <param name="limit">限制</param>
        /// <returns>结果视图</returns>
        Task<IEnumerable<Error>> GetErrorList(string device, int? code, DateTime? startFrom, DateTime? startTo, DateTime? endFrom, DateTime? endTo, string sort, int? offset, int? limit);
        Task<IEnumerable<Printer>> GetPrinterIpList(string pdaip, string printerip, string printername, string sort, int? offset, int? limit);

    }
}
