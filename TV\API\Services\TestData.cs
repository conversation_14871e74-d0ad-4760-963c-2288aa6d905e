﻿using System.Collections.Generic;

namespace API.Services
{
    public class TestData : IData
    {
        #region Home

        public dynamic HomeCellAvaliable()
        {
            dynamic result = new
            {
                title = new string[] { "1巷道", "2巷道", "3巷道", "4巷道", "5巷道", "6巷道" },
                data = new int[] { 100, 60, 60, 70, 69, 60 },
                valdata = new int[] { 683, 234, 234, 523, 345, 234 }
            };

            return result;
        }

        public List<string> HomeImageDir()
        {
            var result = new List<string>()
            {
                "images/1.jpg",
                "images/2.jpg",
                "images/3.jpg",
                "images/4.jpg"
            };

            return result;
        }

        public dynamic HomeManageHis()
        {
            dynamic result = new
            {
                xAxis = new string[] { "11日", "12日", "13日", "14日", "15日", "17日" },
                manageIn = new int[] { 8, 16, 10, 12, 7, 9 },
                manageOut = new int[] { 5, 12, 6, 4, 5, 12 }
            };

            return result;
        }

        public List<dynamic> HomeManageList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });

            return result;
        }

        public List<dynamic> HomeManageinList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });

            return result;
        }

        public List<dynamic> HomeManageCurrentList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });

            return result;
        }

        public List<dynamic> HomeManageinCurrentList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                endCell = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });

            return result;
        }

        public string HomeRouteAvaliable()
        {
            var result = "95|80|100";

            return result;
        }

        public List<dynamic> HomeStorageList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2"
            });

            return result;
        }

        public List<int> HomeSummary()
        {
            var result = new List<int>()
            {
                 11,21,33,44,55,66
            };

            return result;
        }

        #endregion


        #region Manage

        public List<dynamic> ManageApplyList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });
            result.Add(new
            {
                applyType = "1",
                deviceCode = "12106",
                stockBarcode = "T00001",
                parameter = "1",
                errorMessage = "可用库存不足(可能原因:库存不足)"
            });

            return result;
        }

        public dynamic ManageLaneway()
        {
            dynamic result = new
            {
                legend = new string[] { "入库", "出库", "移库"},
                xAxis = new string[] { "1巷道", "2巷道", "3巷道", "4巷道", "5巷道", "6巷道" },
                manageIn = new int[] { 2, 3, 3, 9, 15, 12 },
                manageOut = new int[] { 1, 4, 5, 11, 12, 9 },
                manageMove = new int[] { 1, 2, 5, 4, 4, 7 }
            };

            return result;
        }

        public List<dynamic> ManageStatus()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                value = 4,
                name = "等待执行"
            });
            result.Add(new
            {
                value = 2,
                name = "执行中"
            });
            result.Add(new
            {
                value = 1,
                name = "拣选中"
            });
            result.Add(new
            {
                value = 2,
                name = "错误"
            });

            return result;
        }

        public List<dynamic> ManageList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                createTime = "2022-07-11"
            });

            return result;
        }

        public List<dynamic> ManageSummary()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                manageType = "物料入库",
                count = 10
            });
            result.Add(new
            {
                manageType = "物料出库",
                count = 20
            });
            result.Add(new
            {
                manageType = "载具入库",
                count = 3
            });
            result.Add(new
            {
                manageType = "载具出库",
                count = 1
            });

            return result;
        }

        public List<dynamic> ManageType()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                value = 1,
                name = "入库"
            });
            result.Add(new
            {
                value = 2,
                name = "出库"
            });
            result.Add(new
            {
                value = 3,
                name = "移库"
            });
            
            return result;
        }

        #endregion


        #region Storage

        public dynamic StorageStockInfo()
        {
            dynamic result = new
            {
                age = new List<dynamic>()
                {
                    new{value = 1,name = "1-5天" },
                    new{value = 2,name = "6-10天"},
                    new{value = 1,name = "11-15天"},
                    new{value = 2,name = "15天以上"}
                },
                type = new List<dynamic>()
                {
                    new{value = 1,name = "成品" },
                    new{value = 2,name = "原材料"},
                    new{value = 1,name = "包装"},
                    new{value = 2,name = "载具"}
                },
                count = "2000|1500|500"
            };

            return result;
        }

        public dynamic StorageStocklaneway()
        {
            dynamic result = new
            {
                xAxis = new string[] {"1巷道", "2巷道", "3巷道", "4巷道", "5巷道", "6巷道" },
                goods = new int[] { 2, 3, 3, 9, 15, 12 },
                carrier = new int[] { 1, 4, 5, 11, 12, 9 }
            };

            return result;
        }

        public dynamic StorageCellRate()
        {
            dynamic result = new
            {
                max = new string[] { "1巷道", "2巷道", "3巷道", "4巷道", "5巷道", "6巷道" },
                current = new int[,] { { 93, 100, 66, 80, 140, 120 } },
                count = "1000|2000"
            };

            return result;
        }

        public List<dynamic> StorageList()
        {
            List<dynamic> result = new List<dynamic>();
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });
            result.Add(new
            {
                stockBarcode = "T00011",
                cellCode = "01-02-12",
                goodsName = "AD107显示芯片",
                quantity = "2",
                entryTime = "2022-07-11"
            });

            return result;
        }

        #endregion
    }
}
