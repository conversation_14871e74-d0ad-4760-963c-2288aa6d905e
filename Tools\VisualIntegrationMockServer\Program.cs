using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;

const byte STX = 0x02;
const byte ETX = 0x03;

Console.Title = "VisualIntegration Mock TCP Server";
Console.WriteLine("[Mock] Starting TCP server on 127.0.0.1:8080 ...");

var listener = new TcpListener(IPAddress.Loopback, 8080);
try
{
    listener.Start();
}
catch (SocketException ex)
{
    Console.WriteLine($"[Mock][ERROR] Failed to bind 127.0.0.1:8080. Is the port in use? {ex.Message}");
    Console.WriteLine("[Mock] Try closing the conflicting process or change the port in Program.cs");
    return;
}
Console.WriteLine("[Mock] Listening. Press Ctrl+C to stop.");

_ = Task.Run(async () =>
{
    while (true)
    {
        try
        {
            var client = await listener.AcceptTcpClientAsync();
            Console.WriteLine($"[Mock] Accepted connection from {client.Client.RemoteEndPoint}");
            _ = HandleClientAsync(client);
        }
        catch (Exception ex)
        {
            Console.WriteLine("[Mock][ERROR] Accept loop error: " + ex.Message);
        }
    }
});

await Task.Delay(-1);

static async Task HandleClientAsync(TcpClient client)
{
    using var c = client;
    NetworkStream? stream = null;
    var buffer = new byte[8192];
    var leftover = new MemoryStream();

    try
    {
        stream = c.GetStream();
        while (true)
        {
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
            if (bytesRead == 0)
            {
                Console.WriteLine("[Mock] Client closed: " + client.Client.RemoteEndPoint);
                break;
            }

            leftover.Write(buffer, 0, bytesRead);
            var data = leftover.ToArray();

            int start = Array.IndexOf(data, STX);
            while (start >= 0)
            {
                int end = Array.IndexOf(data, ETX, start + 1);
                if (end < 0)
                {
                    // incomplete frame, keep leftover
                    break;
                }

                int frameLen = end - start + 1;
                var frame = new byte[frameLen];
                Array.Copy(data, start, frame, 0, frameLen);

                try
                {
                    var json = ExtractMessage(frame);
                    Console.WriteLine($"[Mock] << {json}");
                    var responseJson = BuildResponse(json);
                    var framed = FrameMessage(responseJson);
                    await stream.WriteAsync(framed, 0, framed.Length);
                    await stream.FlushAsync();
                    Console.WriteLine($"[Mock] >> {responseJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine("[Mock][ERROR] " + ex);
                }

                // move to next
                int nextPos = end + 1;
                if (nextPos >= data.Length)
                {
                    data = Array.Empty<byte>();
                    break;
                }
                var remaining = new byte[data.Length - nextPos];
                Array.Copy(data, nextPos, remaining, 0, remaining.Length);
                data = remaining;
                start = Array.IndexOf(data, STX);
            }

            // rewrite leftover
            leftover.Dispose();
            leftover = new MemoryStream();
            if (data.Length > 0)
            {
                leftover.Write(data, 0, data.Length);
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine("[Mock][ERROR] " + ex);
    }
    finally
    {
        try { stream?.Dispose(); } catch {}
        try { leftover.Dispose(); } catch {}
        try { c.Close(); } catch {}
    }
}

static string ExtractMessage(byte[] frame)
{
    if (frame.Length < 2) throw new ArgumentException("Invalid frame length");
    if (frame[0] != STX || frame[^1] != ETX) throw new ArgumentException("Invalid frame markers");
    return Encoding.UTF8.GetString(frame, 1, frame.Length - 2);
}

static byte[] FrameMessage(string json)
{
    var payload = Encoding.UTF8.GetBytes(json);
    var framed = new byte[payload.Length + 2];
    framed[0] = STX;
    Array.Copy(payload, 0, framed, 1, payload.Length);
    framed[^1] = ETX;
    return framed;
}

static string BuildResponse(string requestJson)
{
    using var doc = JsonDocument.Parse(requestJson);
    var root = doc.RootElement;
    string funcId = root.GetProperty("FuncID").GetString() ?? string.Empty;
    string funcSeqNo = root.TryGetProperty("FuncSeqNo", out var seq) ? (seq.GetString() ?? "") : "";

    if (funcId.Equals("ASRSOnline", StringComparison.OrdinalIgnoreCase))
    {
        var reply = new
        {
            FuncID = "ASRSOnlineReply",
            FuncSeqNo = funcSeqNo,
            Content = new { Result = true, Message = "" }
        };
        return JsonSerializer.Serialize(reply);
    }
    else if (funcId.Equals("CheckAlive", StringComparison.OrdinalIgnoreCase))
    {
        var reply = new
        {
            FuncID = "CheckAliveReply",
            FuncSeqNo = funcSeqNo,
            Content = new { Result = true, Message = "" },
            Status = new[] { true, false, true, true }
        };
        return JsonSerializer.Serialize(reply);
    }
    else if (funcId.Equals("StnoReq", StringComparison.OrdinalIgnoreCase))
    {
        string stno = "";
        try
        {
            if (root.TryGetProperty("Content", out var content) && content.TryGetProperty("Stno", out var stnoElem))
            {
                stno = stnoElem.GetString() ?? "";
            }
        }
        catch { }
        bool isSupported = stno == "1111" || stno == "1101" || stno == "1201" || stno == "1204";
        var reply = new
        {
            FuncID = "StnoReqReply",
            FuncSeqNo = funcSeqNo,
            Content = new { Result = isSupported, Message = isSupported ? "" : "Unsupported station", Stno = stno }
        };
        return JsonSerializer.Serialize(reply);
    }
    else
    {
        var reply = new
        {
            FuncID = funcId + "Reply",
            FuncSeqNo = funcSeqNo,
            Content = new { Result = true, ErrCode = 0, ErrDesc = "" }
        };
        return JsonSerializer.Serialize(reply);
    }
}

