﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Kean.Application.Command.ViewModels
{
    public class Print
    {
        public string Tag { get; set; }
        public string Line { get; set; }
        public string Count { get; set; }
    }
    public class PrintCommandIn
    {
        public string Goods_Code { get; set; }
        public string Goods_Name { get; set; }
        public string Goods_Model { get; set; }
        public string Goode_Bill { get; set; }
        public string Goods_Batch { get; set; }
        public string Goods_Count { get; set; }
        public string Goods_SN_Code { get; set; }
        public string Goods_Supplier { get; set; }


    }
    public class PrintCommandOut
    {
        public string Code { get; set; }
        public string CsrCode { get; set; }
        public string CsrName { get; set; }
        public string ExpDate { get; set; }
        public string Po { get; set; }
        public string ProDate { get; set; }
        public string Quantity { get; set; }
        public string Sn { get; set; }
        public string supplier { get; set; }


    }
}
