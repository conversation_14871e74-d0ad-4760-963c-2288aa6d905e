﻿using Kean.Domain.Interface.Events;
using Kean.Domain.Interface.RemoteClients;
using Kean.Domain.Shared;
using Newtonsoft.Json.Linq;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Interface.EventHandlers
{
    /// <summary>
    /// 同步输入命令执行时，处理 WCS 任务申请的接口
    /// </summary>
    public sealed class SyncInputExecutingEventHandler_Wcs_Apply(
        INotification notification, // 总线通知
        ITaskService taskService,   // 任务代理
        IWcsClient wcsClient        // WCS 接口
    ) : EventHandler<SyncInputExecutingEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(SyncInputExecutingEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Scope == "WCS" && @event.Function == "Trigger")
            {
                var data = @event.Data as JObject;
                await taskService.Trigger(
                    type: data.Value<int>("Type"),
                    warehouse: data.Value<int>("Warehouse"),
                    device: data.Value<string>("Device"),
                    parameter: new JObject() { ["Barcode"] = data.Value<string>("Barcode"), ["Parameters"] = data.Value<string>("Parameter"), ["Parameter01"] = data.Value<string>("Parameter01"), ["Parameter02"] = data.Value<string>("Parameter02") },
                    timestamp: data.Value<DateTime>("Timestamp"),
                    timeout: null);
                await wcsClient.ApplyAck(int.Parse(@event.Unique), -notification.Count);
            }
        }
    }
}
