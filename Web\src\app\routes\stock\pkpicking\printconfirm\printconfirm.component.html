<h2 mat-dialog-title>{{ 'shared.operation.printerconfirm' | translate }}</h2>
<div mat-dialog-content>
  <form autocomplete="off" [formGroup]="form">
    
    <mat-form-field>
      <mat-label>{{ 'shared.operation.printerIp' | translate }}</mat-label>
      <mat-select formControlName="printerIp" required>
          <mat-option *ngFor="let item of printerIpList" [value]="item.printerIp">{{item.printerName}}</mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button mat-button color="primary" [disabled]="form.invalid" (click)="save()">{{ 'shared.dialog.ok' | translate | uppercase }}</button>
  <button mat-button mat-dialog-close>{{ 'shared.dialog.cancel' | translate | uppercase }}</button>
</div>
