﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Off"
      throwExceptions="false">

	<!-- the targets to write to -->
	<targets>
		<!-- write logs to file  -->
		<!--<target xsi:type="File" name="all" fileName="${basedir}/logs/${shortdate}-all.log"
            layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${callsite}|${message} ${exception:format=tostring}" />-->

		<target xsi:type="File" name="f" fileName="${basedir}/logs/${shortdate}.log"
				layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${callsite}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />
	</targets>

	<!-- rules to map from logger name to target -->
	<!--Trace|Debug|Info|Warn|Error|Fatal|Off-->
	<rules>
		<logger name="api-log" minlevel="Trace" writeTo="f" />
	</rules>
</nlog>