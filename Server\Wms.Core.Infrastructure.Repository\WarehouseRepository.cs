﻿using AutoMapper;
using <PERSON>pper;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Infrastructure.Repository
{
    /// <summary>
    /// 库房仓库
    /// </summary>
    public class WarehouseRepository(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : Domain.Stock.Repositories.IWarehouseRepository,
        Domain.Task.Repositories.IWarehouseRepository,
        Domain.Device.Repositories.IWarehouseRepository
    {
        /*
         * 实现 Kean.Domain.Stock.Repositories.IWarehouseRepository.IsCell 方法
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.IsCell 方法
         */
        public async Task<bool?> IsCell(int id)
        {
            var type = (await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id)
                .Single(c => new { c.CELL_TYPE }))?.CELL_TYPE;
            return type == null ? null : type == nameof(Domain.Task.Models.Cell);
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IWarehouseRepository.UpdateCell 方法
         */
        public async Task UpdateCell(int id, Domain.Stock.Enums.CellState state)
        {
            var timestamp = DateTime.Now;
            await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id)
                .Update(new
                {
                    CELL_STATUS = state.ToString(),
                    STATUS_TIME = timestamp,
                    UPDATE_TIME = timestamp
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetStationById 方法
         */
        public async Task<Domain.Task.Models.Station> GetStationById(int id)
        {
            return mapper.Map<Domain.Task.Models.Station>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id && c.CELL_TYPE == nameof(Domain.Task.Models.Station))
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetStationByCode 方法
         */
        public async Task<Domain.Task.Models.Station> GetStationByCode(string code, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Station>(await database.From<T_WH_CELL>()
 .Where(c => c.CELL_CODE == code && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Station))
 .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetStationByName 方法
         */
        public async Task<Domain.Task.Models.Station> GetStationByName(string name, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Station>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_NAME == name && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Station))
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetStationByDevice 方法
         */
        public async Task<Domain.Task.Models.Station> GetStationByDevice(string device, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Station>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_CODE == device && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Station))
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetCellById 方法
         */
        public async Task<Domain.Task.Models.Cell> GetCellById(int id)
        {
            return mapper.Map<Domain.Task.Models.Cell>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id && c.CELL_TYPE == nameof(Domain.Task.Models.Cell))
                .Lock(Lock.Xlock)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetCellByCode 方法
         */
        public async Task<Domain.Task.Models.Cell> GetCellByCode(string code, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Cell>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_CODE == code && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Cell))
                .Lock(Lock.Xlock)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetCellByName 方法
         */
        public async Task<Domain.Task.Models.Cell> GetCellByName(string name, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Cell>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_NAME == name && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Cell))
                .Lock(Lock.Xlock)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetCellByDevice 方法
         */
        public async Task<Domain.Task.Models.Cell> GetCellByDevice(string device, int warehouse)
        {
            return mapper.Map<Domain.Task.Models.Cell>(await database.From<T_WH_CELL>()
                .Where(c => c.CELL_CODE == device && c.WAREHOUSE_ID == warehouse && c.CELL_TYPE == nameof(Domain.Task.Models.Cell))
                .Lock(Lock.Xlock)
                .Single());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetFreeCells 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Cell>> GetFreeCells(int warehouse, IEnumerable<string> laneway, int? spec, string pallet)
        {
            pallet = $"({pallet})";
            var schema = database.From<T_WH_CELL>()
                .Where(c => c.WAREHOUSE_ID == warehouse
                    && laneway.Contains(c.CELL_LANEWAY)
                    && c.CELL_TYPE == nameof(Domain.Task.Models.Cell)
                    && c.CELL_STATUS == nameof(Domain.Stock.Enums.CellState.Empty)
                    && c.RUN_STATUS == nameof(Domain.Task.Enums.CellState.Enabled)
                    && (c.CELL_PALLET == null || c.CELL_PALLET.Contains(pallet)));
            if (spec.HasValue)
            {
                schema = schema.Where(c => c.CELL_SPEC == null || c.CELL_SPEC >= spec);

            }
            var cell = mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(await schema.Select());
            return cell;// mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(await schema.Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetFreeCellsKSF 方法
         */
        public IEnumerable<Domain.Task.Models.Cell> GetFreeCellsKSF(Domain.Task.Models.Station orginalStation, IEnumerable<string> laneway, int? spec, string pallet)
        {
            pallet = $"({pallet})";
            //var schema = database.From<T_WH_CELL>()
            //    .Where(c => c.WAREHOUSE_ID == warehouse
            //        && (laneway == null || laneway.Count<string>() == 0) ? true : laneway.Contains(c.CELL_LANEWAY)
            //        && c.CELL_TYPE == nameof(Domain.Task.Models.Cell)
            //        && c.CELL_STATUS == nameof(Domain.Stock.Enums.CellState.Empty)
            //        && c.RUN_STATUS == nameof(Domain.Task.Enums.CellState.Enabled)
            //        && (c.CELL_PALLET == null || c.CELL_PALLET.Contains(pallet)));
            //if (spec.HasValue)
            //{
            //    schema = schema.Where(c => c.CELL_SPEC == null || c.CELL_SPEC >= spec);

            //}
            IEnumerable<T_WH_CELL> mWH_CELL = database.Context.
                Query<T_WH_CELL>($"SELECT T0.* FROM T_WH_CELL T0,IO_CONTROL_ROUTE T1  " +
                $"WHERE T1.CONTROL_ROUTE_STATUS=1 " +
                $"AND T1.START_DEVICE = '{orginalStation.Laneway}'  " +
                $"AND T1.END_DEVICE = T0.CELL_LANEWAY  " +
                $"AND T0.CELL_STATUS = 'Empty'  " +
                $"AND RUN_STATUS = 'Enabled'", transaction: database.Context.Transaction);
            //if (mWH_CELL == null) { return null; }

            //return mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(mWH_CELL);
            return mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(mWH_CELL);
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetLoadCells 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Cell>> GetLoadCells(int warehouse, IEnumerable<string> laneway, string pallet)
        {
            pallet = $"({pallet})";
            return mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(await database.From<T_WH_CELL>()
                .Where(c => c.WAREHOUSE_ID == warehouse
                    && laneway.Contains(c.CELL_LANEWAY)
                    && c.CELL_TYPE == nameof(Domain.Task.Models.Cell)
                    && c.CELL_STATUS != nameof(Domain.Stock.Enums.CellState.Empty)
                    && c.RUN_STATUS == nameof(Domain.Task.Enums.CellState.Enabled)
                    && (c.CELL_PALLET == null || c.CELL_PALLET.Contains(pallet)))
                .Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.GetRunningCells 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Cell>> GetRunningCells(int warehouse, IEnumerable<string> laneway)
        {
            return mapper.Map<IEnumerable<Domain.Task.Models.Cell>>(await database.From<T_WH_CELL>()
                .Where(c => c.WAREHOUSE_ID == warehouse
                    && laneway.Contains(c.CELL_LANEWAY)
                    && c.CELL_TYPE == nameof(Domain.Task.Models.Cell)
                    && c.RUN_STATUS == nameof(Domain.Task.Enums.CellState.Selected))
                .Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.MonopolizeCell 方法
         */
        public async Task<bool> MonopolizeCell(int id, string version)
        {
            var newVersion = Guid.NewGuid().ToString("N");
            return await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id && c.STATUS_VERSION == version)
                .Update(new
                {
                    STATUS_VERSION = newVersion,
                    UPDATE_TIME = DateTime.Now
                }) > 0;
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.LockCell 方法
         */
        public async Task LockCell(params int[] id)
        {
            var timestamp = DateTime.Now;
            await database.From<T_WH_CELL>()
                .Where(c => c.CELL_TYPE == nameof(Domain.Task.Models.Cell) && id.Contains(c.CELL_ID))
                .Update(new
                {
                    RUN_STATUS = nameof(Domain.Task.Enums.CellState.Selected),
                    STATUS_TIME = timestamp,
                    UPDATE_TIME = timestamp
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.ReleaseCell 方法
         */
        public async Task ReleaseCell(params int[] id)
        {
            var timestamp = DateTime.Now;
            await database.From<T_WH_CELL>()
                .Where(c => c.CELL_TYPE == nameof(Domain.Task.Models.Cell) && id.Contains(c.CELL_ID))
                .Update(new
                {
                    RUN_STATUS = nameof(Domain.Task.Enums.CellState.Enabled),
                    STATUS_TIME = timestamp,
                    CELL_REMARK = default(string),
                    UPDATE_TIME = timestamp
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.IWarehouseRepository.DisableCell 方法
         */
        public async Task DisableCell(string remark, params int[] id)
        {
            var timestamp = DateTime.Now;
            await database.From<T_WH_CELL>()
                .Where(c => c.CELL_TYPE == nameof(Domain.Task.Models.Cell) && id.Contains(c.CELL_ID))
                .Update(new
                {
                    RUN_STATUS = nameof(Domain.Task.Enums.CellState.Disabled),
                    STATUS_TIME = timestamp,
                    CELL_REMARK = remark,
                    UPDATE_TIME = timestamp
                });
        }

        /*
         * 实现 Kean.Domain.Device.Repositories.IWarehouseRepository.GetDeviceCode 方法
         */
        public async Task<KeyValuePair<string, string>> GetDeviceCode(int id)
        {
            var cell = await database.From<T_WH_CELL>()
                .Where(c => c.CELL_ID == id)
                .Single();
            return cell.CELL_TYPE switch
            {
                nameof(Domain.Task.Models.Cell) => new(cell.CELL_CODE, cell.CELL_LANEWAY),
                nameof(Domain.Task.Models.Station) => new(cell.CELL_CODE, cell.CELL_CODE),
                _ => default
            };
        }

        public async Task UpdateCellDegree(List<string> arrX, List<string> arrY, List<string> arrZ, string degree)
        {
            try
            {
                await database.From<T_WH_CELL>()
                .Where(c => c.CELL_TYPE == nameof(Domain.Task.Models.Cell)
                && c.CELL_X >= Convert.ToInt32(arrX[0]) && c.CELL_X <= Convert.ToInt32(arrX[1])
                && c.CELL_Y >= Convert.ToInt32(arrY[0]) && c.CELL_Y <= Convert.ToInt32(arrY[1])
                && c.CELL_Z >= Convert.ToInt32(arrZ[0]) && c.CELL_Z <= Convert.ToInt32(arrZ[1]))
                .Update(new
                {
                    CELL_HOTDEGREE = degree,
                    UPDATE_TIME = DateTime.Now
                });
            }
            catch (Exception)
            {

                throw;
            }
        }
    }
}
