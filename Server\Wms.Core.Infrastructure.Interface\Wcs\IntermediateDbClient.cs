﻿using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Interface.Wcs.Entities;
using System.Threading.Tasks;

namespace Kean.Infrastructure.Interface.Wcs
{
    /// <summary>
    /// WCS 远程客户端实现
    /// </summary>
    public partial class IntermediateDbClient(
        IDefaultDb database // 数据库连接
    ) : Domain.Interface.RemoteClients.IWcsClient
    {
        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.RouteState 方法
         */
        public async Task<bool?> RouteState(
            string original,
            string destination)
        {
            var route = await database.From<IO_CONTROL_ROUTE>()
    .Where(r => r.START_DEVICE == original && r.END_DEVICE == destination)
    .Single(r => new { r.CONTROL_ROUTE_STATUS });
            return route == null ? null : route.CONTROL_ROUTE_STATUS == 1;
        }

        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.CreateTask 方法
         */
        public async Task<string> CreateTask(
            string key,
            string warehouse,
            string type,
            string priority,
            string barcode,
            string original,
            string destination,
            string timestamp,
            params string[] parameters)
        {
            return (await database.From<IO_CONTROL>().Add(new()
            {
                MANAGE_ID = int.Parse(key),
                STOCK_BARCODE = barcode,
                CONTROL_TASK_TYPE = int.TryParse(type, out var i) ? i : null,
                CONTROL_TASK_LEVEL = priority,
                START_WAREHOUSE_CODE = warehouse,
                START_DEVICE_CODE = original,
                END_WAREHOUSE_CODE = warehouse,
                END_DEVICE_CODE = destination,
                PRE_CONTROL_STATUS = 0,
                CONTROL_STATUS = 0,
                ERROR_TEXT = string.Empty,
                CONTROL_BEGIN_TIME = timestamp,
                CONTROL_END_TIME = string.Empty,
                CONTROL_REMARK = string.Empty
            }))?.ToString();
        }

        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.DeleteTask 方法
         */
        public async Task<bool> DeleteTask(
            string key)
        {
            var manageId = int.Parse(key);
            var ioControl = await database.From<IO_CONTROL>()
                .Where(c => c.MANAGE_ID == manageId)
                .Single();
            return ioControl == null || ioControl.CONTROL_STATUS == 900 || ioControl.CONTROL_STATUS == 999;
        }

        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.CreateTask 方法
         */
        public async Task<string> UpdatePriority(
            string key,
            string priority)
        {
            var manageId = int.Parse(key);
            priority = $"-{priority}"; // 加个前缀是为了让调度可以识别，具体做法根据项目而定
            return await database.From<IO_CONTROL>()
                .Where(c => c.MANAGE_ID == manageId && c.CONTROL_STATUS < 10)
                .Update(new { CONTROL_TASK_LEVEL = priority }) > 0 ? null : "任务不允许更新优先级";
        }

        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.ApplyAck 方法
         */
        public async Task ApplyAck(
            int unique,
            int result)
        {
            if (result >= 0)
            {
                await database.From<IO_CONTROL_APPLY>()
                    .Where(a => a.CONTROL_APPLY_ID == unique)
                    .Delete();
            }
        }

        /*
         * 实现 Kean.Domain.Interface.RemoteClients.IWcsClient.FeedbackAck 方法
         */
        public async Task FeedbackAck(
            int unique,
            int result)
        {
            if (result >= 0)
            {
                await database.From<IO_CONTROL>()
                .Where(c => c.CONTROL_ID == unique)
                .Update(new { PRE_CONTROL_STATUS = result });
            }
        }
    }
}
