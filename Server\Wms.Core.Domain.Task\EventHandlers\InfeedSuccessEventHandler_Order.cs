﻿using Kean.Domain.Shared;
using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Events;
using Kean.Infrastructure.Configuration;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 创建上架命令成功时，下达控制任务
    /// </summary>
    public sealed class InfeedSuccessEventHandler_Order(
        IServiceProvider serviceProvider,   // 服务供应商
        ITaskService taskService,
        IDefaultDb database,
        WcsMode wcsMode                     // WCS 模式
    ) : EventHandler<InfeedSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(InfeedSuccessEvent @event, CancellationToken cancellationToken)
        {

            //T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == @event.PlanListId).Single();
            //if (planList != null)
            //{
            //    planList.ORDERED_QUANTITY += (decimal)@event.ManageListQuantity;
            //    await database.From<T_PLAN_LIST>().Update(planList);
            //}

        }
    }
}
