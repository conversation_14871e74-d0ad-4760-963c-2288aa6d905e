﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: System.Runtime.Serialization.ContractNamespaceAttribute("http://Microsoft.LobServices.Sap/2007/03/Rfc/", ClrNamespace = "microsoft.lobservices.sap._2007._03.Rfc")]
[assembly: System.Runtime.Serialization.ContractNamespaceAttribute("http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", ClrNamespace = "microsoft.lobservices.sap._2007._03.Types.Rfc")]

namespace microsoft.lobservices.sap._2007._03.Rfc
{
    using microsoft.lobservices.sap._2007._03.Types.Rfc;
    using System.Runtime.Serialization;
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public partial class ZALLSAP_UPLOAD_GOODSMOV
    {

        private string sOURCESYSField;

        private string tARGETSYSField;

        private string uPDATETIMEField;

        private ZALLSAP_UPLOAD_GOODSMOV_2[] iT_MATDOC_DETAILSField;

        private ZALLSAP_UPLOAD_GOODSMOV_1[] iT_MATDOC_HEADField;

        private ZALLSAP_UPLOAD_GOODSMOV_3[] oT_MATDOCField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string SOURCESYS
        {
            get
            {
                return this.sOURCESYSField;
            }
            set
            {
                this.sOURCESYSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string TARGETSYS
        {
            get
            {
                return this.tARGETSYSField;
            }
            set
            {
                this.tARGETSYSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string UPDATETIME
        {
            get
            {
                return this.uPDATETIMEField;
            }
            set
            {
                this.uPDATETIMEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILS
        {
            get
            {
                return this.iT_MATDOC_DETAILSField;
            }
            set
            {
                this.iT_MATDOC_DETAILSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEAD
        {
            get
            {
                return this.iT_MATDOC_HEADField;
            }
            set
            {
                this.iT_MATDOC_HEADField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable = true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/", IsNullable = false)]
        public ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOC
        {
            get
            {
                return this.oT_MATDOCField;
            }
            set
            {
                this.oT_MATDOCField = value;
            }
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public partial class ZALLSAP_UPLOAD_GOODSMOVResponse : object, System.Runtime.Serialization.IExtensibleDataObject
    {

        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;

        private microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILSField;

        private microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEADField;

        private microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOCField;

        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }

        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_2[] IT_MATDOC_DETAILS
        {
            get
            {
                return this.IT_MATDOC_DETAILSField;
            }
            set
            {
                this.IT_MATDOC_DETAILSField = value;
            }
        }

        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_1[] IT_MATDOC_HEAD
        {
            get
            {
                return this.IT_MATDOC_HEADField;
            }
            set
            {
                this.IT_MATDOC_HEADField = value;
            }
        }

        [System.Runtime.Serialization.DataMemberAttribute(IsRequired = true)]
        public microsoft.lobservices.sap._2007._03.Types.Rfc.ZALLSAP_UPLOAD_GOODSMOV_3[] OT_MATDOC
        {
            get
            {
                return this.OT_MATDOCField;
            }
            set
            {
                this.OT_MATDOCField = value;
            }
        }
    }
}
namespace microsoft.lobservices.sap._2007._03.Types.Rfc
{
    using System.Runtime.Serialization;

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/")]
    public partial class ZALLSAP_UPLOAD_GOODSMOV_2
    {

        private string eBELNField;

        private string pOSNRField;

        private string sGTXTField;

        private string mATNRField;

        private decimal mENGEField;

        //private bool mENGEFieldSpecified;

        private string mEINSField;

        private string wERKSField;

        private string lGORTField;

        private string iNSMKField;

        private string dZUSCHField;

        private string hSDATField;

        private string lICHAField;

        private string cHARGField;

        private string eLIKZField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string EBELN
        {
            get
            {
                return this.eBELNField;
            }
            set
            {
                this.eBELNField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string POSNR
        {
            get
            {
                return this.pOSNRField;
            }
            set
            {
                this.pOSNRField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string SGTXT
        {
            get
            {
                return this.sGTXTField;
            }
            set
            {
                this.sGTXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = false)]
        public decimal MENGE
        {
            get
            {
                return this.mENGEField;
            }
            set
            {
                this.mENGEField = value;
            }
        }

        /// <remarks/>
        //[System.Xml.Serialization.XmlIgnoreAttribute()]
        //public bool MENGESpecified
        //{
        //    get
        //    {
        //        return this.mENGEFieldSpecified;
        //    }
        //    set
        //    {
        //        this.mENGEFieldSpecified = value;
        //    }
        //}

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MEINS
        {
            get
            {
                return this.mEINSField;
            }
            set
            {
                this.mEINSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string WERKS
        {
            get
            {
                return this.wERKSField;
            }
            set
            {
                this.wERKSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LGORT
        {
            get
            {
                return this.lGORTField;
            }
            set
            {
                this.lGORTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string INSMK
        {
            get
            {
                return this.iNSMKField;
            }
            set
            {
                this.iNSMKField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string DZUSCH
        {
            get
            {
                return this.dZUSCHField;
            }
            set
            {
                this.dZUSCHField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string HSDAT
        {
            get
            {
                return this.hSDATField;
            }
            set
            {
                this.hSDATField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LICHA
        {
            get
            {
                return this.lICHAField;
            }
            set
            {
                this.lICHAField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string CHARG
        {
            get
            {
                return this.cHARGField;
            }
            set
            {
                this.cHARGField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string ELIKZ
        {
            get
            {
                return this.eLIKZField;
            }
            set
            {
                this.eLIKZField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/")]
    public partial class ZALLSAP_UPLOAD_GOODSMOV_3
    {

        private string sTATUSField;

        private string iNFOTEXTField;

        private string mBLNRField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string STATUS
        {
            get
            {
                return this.sTATUSField;
            }
            set
            {
                this.sTATUSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string INFOTEXT
        {
            get
            {
                return this.iNFOTEXTField;
            }
            set
            {
                this.iNFOTEXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Types/Rfc/")]
    public partial class ZALLSAP_UPLOAD_GOODSMOV_1
    {

        private string bSARTField;

        private string eBELNField;

        private string i_DATEField;

        private string i_TIMEField;

        private string lGPLAField;

        private string vTXTKField;

        private string hTEXTField;

        private string bUDATField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string BSART
        {
            get
            {
                return this.bSARTField;
            }
            set
            {
                this.bSARTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string EBELN
        {
            get
            {
                return this.eBELNField;
            }
            set
            {
                this.eBELNField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string I_DATE
        {
            get
            {
                return this.i_DATEField;
            }
            set
            {
                this.i_DATEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string I_TIME
        {
            get
            {
                return this.i_TIMEField;
            }
            set
            {
                this.i_TIMEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LGPLA
        {
            get
            {
                return this.lGPLAField;
            }
            set
            {
                this.lGPLAField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string VTXTK
        {
            get
            {
                return this.vTXTKField;
            }
            set
            {
                this.vTXTKField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string HTEXT
        {
            get
            {
                return this.hTEXTField;
            }
            set
            {
                this.hTEXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string BUDAT
        {
            get
            {
                return this.bUDATField;
            }
            set
            {
                this.bUDATField = value;
            }
        }
    }


}


[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace = "http://ws.ksf.com.cn/", ConfigurationName = "ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap")]
public interface ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap
{

    // CODEGEN: 命名空间 http://Microsoft.LobServices.Sap/2007/03/Rfc/ 的元素名称 ZALLSAP_UPLOAD_GOODSMOV 以后生成的消息协定未标记为 nillable
    [System.ServiceModel.OperationContractAttribute(Action = "http://ws.ksf.com.cn/ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingT/Operation" +
        "_ZALLSAP_UPLOAD_GOODSMOV", ReplyAction = "*")]
    Operation_ZALLSAP_UPLOAD_GOODSMOVResponse Operation_ZALLSAP_UPLOAD_GOODSMOV(Operation_ZALLSAP_UPLOAD_GOODSMOVRequest request);

    //[System.ServiceModel.OperationContractAttribute(Action="http://ws.ksf.com.cn/ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingT/Operation" +
    //    "_ZALLSAP_UPLOAD_GOODSMOV", ReplyAction="*")]
    //System.Threading.Tasks.Task<Operation_ZALLSAP_UPLOAD_GOODSMOVResponse> Operation_ZALLSAP_UPLOAD_GOODSMOVAsync(Operation_ZALLSAP_UPLOAD_GOODSMOVRequest request);
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped = false)]
public partial class Operation_ZALLSAP_UPLOAD_GOODSMOVRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Name = "Operation_ZALLSAP_UPLOAD_GOODSMOV", Namespace = "http://ws.ksf.com.cn/", Order = 0)]
    public Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody Body;

    public Operation_ZALLSAP_UPLOAD_GOODSMOVRequest()
    {
    }

    public Operation_ZALLSAP_UPLOAD_GOODSMOVRequest(Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody Body)
    {
        this.Body = Body;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.Runtime.Serialization.DataContractAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
public partial class Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody
{

    [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue = false, Order = 0)]
    public microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOV ZALLSAP_UPLOAD_GOODSMOV;

    public Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody()
    {
    }

    public Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody(microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOV ZALLSAP_UPLOAD_GOODSMOV)
    {
        this.ZALLSAP_UPLOAD_GOODSMOV = ZALLSAP_UPLOAD_GOODSMOV;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped = false)]
public partial class Operation_ZALLSAP_UPLOAD_GOODSMOVResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Name = "Operation_ZALLSAP_UPLOAD_GOODSMOVResponse", Namespace = "http://ws.ksf.com.cn/", Order = 0)]
    public Operation_ZALLSAP_UPLOAD_GOODSMOVResponseBody Body;

    public Operation_ZALLSAP_UPLOAD_GOODSMOVResponse()
    {
    }

    public Operation_ZALLSAP_UPLOAD_GOODSMOVResponse(Operation_ZALLSAP_UPLOAD_GOODSMOVResponseBody Body)
    {
        this.Body = Body;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.Runtime.Serialization.DataContractAttribute(Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
public partial class Operation_ZALLSAP_UPLOAD_GOODSMOVResponseBody
{

    [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue = false, Order = 0)]
    public microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOVResponse ZALLSAP_UPLOAD_GOODSMOVResponse;

    public Operation_ZALLSAP_UPLOAD_GOODSMOVResponseBody()
    {
    }

    public Operation_ZALLSAP_UPLOAD_GOODSMOVResponseBody(microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOVResponse ZALLSAP_UPLOAD_GOODSMOVResponse)
    {
        this.ZALLSAP_UPLOAD_GOODSMOVResponse = ZALLSAP_UPLOAD_GOODSMOVResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
public interface ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapChannel : ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap, System.ServiceModel.IClientChannel
{
}


public partial class ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient : System.ServiceModel.ClientBase<ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap>, ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap
{

    public ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient()
    {
    }

    public ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient(string endpointConfigurationName) :
            base(endpointConfigurationName)
    {
    }

    public ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient(string endpointConfigurationName, string remoteAddress) :
            base(endpointConfigurationName, remoteAddress)
    {
    }

    public ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) :
            base(endpointConfigurationName, remoteAddress)
    {
    }

    public ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    Operation_ZALLSAP_UPLOAD_GOODSMOVResponse ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap.Operation_ZALLSAP_UPLOAD_GOODSMOV(Operation_ZALLSAP_UPLOAD_GOODSMOVRequest request)
    {
        return base.Channel.Operation_ZALLSAP_UPLOAD_GOODSMOV(request);
    }

    public microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOVResponse Operation_ZALLSAP_UPLOAD_GOODSMOV(microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOV ZALLSAP_UPLOAD_GOODSMOV)
    {
        Operation_ZALLSAP_UPLOAD_GOODSMOVRequest inValue = new Operation_ZALLSAP_UPLOAD_GOODSMOVRequest();
        inValue.Body = new Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody();
        inValue.Body.ZALLSAP_UPLOAD_GOODSMOV = ZALLSAP_UPLOAD_GOODSMOV;
        Operation_ZALLSAP_UPLOAD_GOODSMOVResponse retVal = ((ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap)(this)).Operation_ZALLSAP_UPLOAD_GOODSMOV(inValue);
        return retVal.Body.ZALLSAP_UPLOAD_GOODSMOVResponse;
    }

    //[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    //System.Threading.Tasks.Task<Operation_ZALLSAP_UPLOAD_GOODSMOVResponse> ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap.Operation_ZALLSAP_UPLOAD_GOODSMOVAsync(Operation_ZALLSAP_UPLOAD_GOODSMOVRequest request)
    //{
    //    return base.Channel.Operation_ZALLSAP_UPLOAD_GOODSMOVAsync(request);
    //}

    //public System.Threading.Tasks.Task<Operation_ZALLSAP_UPLOAD_GOODSMOVResponse> Operation_ZALLSAP_UPLOAD_GOODSMOVAsync(microsoft.lobservices.sap._2007._03.Rfc.ZALLSAP_UPLOAD_GOODSMOV ZALLSAP_UPLOAD_GOODSMOV)
    //{
    //    Operation_ZALLSAP_UPLOAD_GOODSMOVRequest inValue = new Operation_ZALLSAP_UPLOAD_GOODSMOVRequest();
    //    inValue.Body = new Operation_ZALLSAP_UPLOAD_GOODSMOVRequestBody();
    //    inValue.Body.ZALLSAP_UPLOAD_GOODSMOV = ZALLSAP_UPLOAD_GOODSMOV;
    //    return ((ZALLSAP_UPLOAD_GOODSMOV_Orchestration_1_Port_DingTSoap)(this)).Operation_ZALLSAP_UPLOAD_GOODSMOVAsync(inValue);
    //}
}
