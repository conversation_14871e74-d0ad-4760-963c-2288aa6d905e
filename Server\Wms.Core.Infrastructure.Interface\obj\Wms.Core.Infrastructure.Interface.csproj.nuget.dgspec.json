{"format": 1, "restore": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj": {}}, "projects": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj", "projectName": "Domain_KSF", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj", "projectName": "Kean.Domain.App", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj", "projectName": "Kean.Domain.Basic", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Domain_KSF\\Domain_KSF.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj", "projectName": "Kean.Domain.Identity", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj", "projectName": "Kean.Domain.Interface", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj", "projectName": "Kean.Domain.Material", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj", "projectName": "Kean.Domain.Message", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj", "projectName": "Kean.Domain.Order", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj", "projectName": "Kean.Domain.Seedwork", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj", "projectName": "Kean.Domain.Shared", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj", "projectName": "Kean.Domain.Stock", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj", "projectName": "Kean.Domain.Task", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Shared\\Wms.Core.Domain.Shared.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj", "projectName": "Kean.Infrastructure.Configuration", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\Wms.Core.Infrastructure.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Configuration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj", "projectName": "Kean.Infrastructure.Database", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "FluentMigrator.Runner": {"target": "Package", "version": "[6.2.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "MySql.Data": {"target": "Package", "version": "[9.1.0, )"}, "Npgsql": {"target": "Package", "version": "[9.0.1, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj", "projectName": "Kean.Infrastructure.Interface", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\Wms.Core.Infrastructure.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.App\\Wms.Core.Domain.App.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Basic\\Wms.Core.Domain.Basic.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Identity\\Wms.Core.Domain.Identity.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Interface\\Wms.Core.Domain.Interface.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Material\\Wms.Core.Domain.Material.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Message\\Wms.Core.Domain.Message.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Order\\Wms.Core.Domain.Order.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Seedwork\\Wms.Core.Domain.Seedwork.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Stock\\Wms.Core.Domain.Stock.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Domain.Task\\Wms.Core.Domain.Task.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Database\\Wms.Core.Infrastructure.Database.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj"}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Zebra.Printer.SDK": {"target": "Package", "version": "[2.16.2905, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj", "projectName": "Kean.Infrastructure.Network", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\Wms.Core.Infrastructure.Network.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Network\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MQTTnet": {"target": "Package", "version": "[4.3.7.1207, )"}, "OPCFoundation.NetStandard.Opc.Ua": {"target": "Package", "version": "[1.5.374.126, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj", "projectName": "Kean.Infrastructure.NoSql", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\Wms.Core.Infrastructure.NoSql.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.NoSql\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj", "projectName": "Kean.Infrastructure.Soap", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\Wms.Core.Infrastructure.Soap.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Soap\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SoapCore": {"target": "Package", "version": "[1.1.0.51, )"}, "System.Security.Cryptography.Xml": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj", "projectName": "Kean.Infrastructure.Utilities", "projectPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\Wms.Core.Infrastructure.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProject\\K\\康师傅\\代码\\SIASUN_KSF_BS\\Server\\Wms.Core.Infrastructure.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\BaseSoftware\\Visual Studio\\2022\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}