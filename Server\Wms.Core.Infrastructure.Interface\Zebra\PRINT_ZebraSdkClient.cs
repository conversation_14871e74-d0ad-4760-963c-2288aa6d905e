﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zebra.Sdk.Comm;
using System.Data;
using Zebra.Sdk.Printer.Discovery;
using Zebra.Sdk.Printer;

namespace Kean.Infrastructure.Interface.Zebra

{
    // 假设调用目标是 ERP。注意：Kean.Infrastructure.Interface.DependencyInjection 中定义了此类的依赖注入
    public partial class PRINT_ZebraSdkClient : Domain.Interface.RemoteClients.IPrintClient
    {
        // 构造函数略（根据实际情况注入）


        // 根据实际情况依赖注入
        public PRINT_ZebraSdkClient(
            ILogger<PRINT_ZebraSdkClient> logger
        )
        {
            _logger = logger;
        }
        private readonly ILogger<PRINT_ZebraSdkClient> _logger;

        public async Task<string> Print(string ip, string info)
        {
            Connection printerConnection = null;
            //var ex = new Exception();
            try
            {
                try
                {
                    printerConnection = GetConnection(ip);//连接打印机
                }
                catch (ConnectionException e)
                {
                    throw e;
                    //ex = e;
                }
                await Task.Run(async () =>
                {
                    try
                    {

                        printerConnection.Open();

                        //await Task.Delay(1500);

                        PrinterLanguage printerLanguage = ZebraPrinterFactory.GetInstance(printerConnection).PrinterControlLanguage;
                        //await Task.Delay(1500);

                        //foreach (var info in infoList)
                        //{
                        printerConnection.Write(GetConfigLabel(printerLanguage, info));
                        //}
                    }
                    catch (ConnectionException e)
                    {
                        throw e;
                        //ex = e;
                    }
                    catch (ZebraPrinterLanguageUnknownException e)
                    {
                        throw e;
                        //ex = e;
                    }
                    finally
                    {
                        try
                        {
                            await Task.Delay(1000);

                            if (printerConnection != null)
                            {
                                printerConnection.Close();
                            }

                            await Task.Delay(1000);

                        }
                        catch (ConnectionException e)
                        {
                            throw e;
                            //ex = e;
                        }
                        finally
                        {

                        }
                    }
                });
                return "shared.notification.success";
            }
            catch (Exception ex)
            {

                return ex.Message;
            }
           
            //if (ex.Message == null)
            //{
            //    return "shared.notification.success";
            //}
            //else
            //{
            //    return ex.Message;
            //}


        }



        public Connection GetConnection(string print_ip)
        {
            try
            {
                return new TcpConnection(print_ip, 9100);
            }
            catch (Exception e)
            {
                throw new ConnectionException(e.Message, e);
            }
        }

        // 字符串截断
        public static string TruncateString(string input, int maxLength)
        {
            if (string.IsNullOrEmpty(input)) return input;
            return input.Length > maxLength
                ? input.Substring(0, maxLength)
                : input;
        }

        private byte[] GetConfigLabel(PrinterLanguage printerLanguage, string printInfo)
        {
            byte[] configLabel = null;
            var print = printInfo.Split("<#>");

            string code = "1";// print[0];
            string name = "1";//print[1];
            string model = "1";//print[2];
            string bill = "1";//print[3];
            string batch = "1";//print[4];
            string sn = "1";//print[5];
            string qty = "1";//print[6];
            string supplier = "1";//print[7];
            string qr = printInfo; //code + "<#>" + bill + "<#>" + batch + "<#>" + qty + "<#>" + sn + "<#>" + supplier;

            name = TruncateString(name, 7);
            model = TruncateString(model, 7);
            sn = TruncateString(sn, 15);
            supplier = TruncateString(supplier, 4);

            if (printerLanguage == PrinterLanguage.ZPL)
            {
                configLabel = Encoding.UTF8.GetBytes($@"



^XA~TA000~JSN^LT0^MNW^MTD^PON^PMN^LH0,0^JMA^PR5,5~SD30^JUS^LRN^CI27^PA0,1,1,0^XZ
^XA

^CW1,E:MSUNG.FNT
^CI28

^FO0,190^GB559,0,4^FS
^FO398,192^GB0,367,4^FS
^FO0,230^GB559,0,4^FS
^FO0,270^GB559,0,4^FS
^FO0,310^GB559,0,4^FS
^FO0,350^GB559,0,4^FS
^FO0,390^GB559,0,4^FS
^FO0,430^GB559,0,4^FS
^FO0,470^GB559,0,4^FS
^FO0,509^GB559,0,4^FS


^PQ1,0,1,Y
^XZ

");
            }
            else if (printerLanguage == PrinterLanguage.CPCL)
            {
                string cpclConfigLabel = "! 0 200 200 406 1\r\n" + "ON-FEED IGNORE\r\n" + "BOX 20 20 380 380 8\r\n" + "T 0 6 137 177 TEST\r\n" + "PRINT\r\n";
                configLabel = Encoding.UTF8.GetBytes(cpclConfigLabel);
            }
            else if (printerLanguage == PrinterLanguage.LINE_PRINT)
            {
                configLabel = Encoding.UTF8.GetBytes("====================|  TEST  |====================");
            }
            return configLabel;
        }



    }
}