﻿using Kean.Application.Command.Interfaces;
using Kean.Application.Command.ViewModels;
using Kean.Application.Query.Interfaces;
using Kean.Infrastructure.Utilities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Controllers
{
    /// <summary>
    /// 订单服务
    /// </summary>
    [ApiController, Route("api/orders")]
    public class OrdersController(
        Application.Command.Interfaces.IOrderService orderCommandService,   // 订单命令服务
        Application.Query.Interfaces.IOrderService orderQueryService,        // 订单查询服务
        Application.Query.Interfaces.IStockService stockService,
        Application.Query.Interfaces.ISysConfigService sysConfigService,
        Application.Query.Interfaces.IWarehouseService warehouseService,
        Application.Command.Interfaces.ITaskService taskService,
        Application.Command.Interfaces.IStockService stockServiceCommand
    ) : ControllerBase
    {
        /// <summary>
        /// 获取订单类型列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("types")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetTypeList()
        {
            var items = await orderQueryService.GetTypeList();
            return StatusCode(200, new { items, total = items.Count() });
        }

        /// <summary>
        /// 获取订单详情列表和对应库存详情列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("getPSlist")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetTypeList(
            [FromQuery] string plancode)
        {
            try
            {
                bool canBind = true;
                var orderLines = await orderQueryService.GetLineListByPlanCode(plancode);

                //var storageLines = await stockService.GetStockListByPlanCode(orderLines);

                return StatusCode(200, new { orderLines = orderLines, canBind = true });
            }
            catch (Exception)
            {

                throw;
            }
        }

        /// <summary>
        /// 获取订单详情列表和对应库存详情列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpPost("getStoragelist")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetStorageList(
            [FromMember] Application.Query.ViewModels.Order[] orders,
            [FromMember] bool half,
            [FromMember] bool full,
            [FromMember] string crane,
            [FromMember] bool overFullPallet,
            [FromMember] bool ignoreNullStorage,
            [FromMember] int allowdays)
        {
            try
            {
                bool canBind = true;
                //var orderLines = await orderQueryService.GetLineListByPlanCode(plancode);

                var storageLines = await stockService.GetStockListByPlanList(orders, half, full, crane, null, null, allowdays);
                if (storageLines.Count() == 1 && storageLines.First().Barcode == "0")
                {
                    return StatusCode(422, storageLines.First().Remark);
                }

                return StatusCode(200, new { storageLines = storageLines, canBind = true });
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        /// <summary>
        /// 获取堆垛机列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("crane")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetCraneList()
        {
            var craneList = sysConfigService.GetCraneList();

            return StatusCode(200, new { craneList });

        }

        /// <summary>
        /// 获取实例列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetOrderList(
            [FromQuery] int? type,
            [FromQuery] string number,
            [FromQuery] string creater,
            [FromQuery] string state,
            [FromQuery] DateTime? createTimeFrom,
            [FromQuery] DateTime? createTimeTo,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            var items = await orderQueryService.GetOrderList(type, number, state, creater, createTimeFrom, createTimeTo, sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await orderQueryService.GetOrderCount(type, number, state, creater, createTimeFrom, createTimeTo);
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 获取行列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("{id}/lines")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetLineList(
            [FromRoute] int? id,
            [FromQuery] int? state,
            [FromQuery] string code,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            var items = await orderQueryService.GetLineList(id, state, code, sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await orderQueryService.GetLineCount(id, state, code);
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <response code="201">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost]
        [ProducesResponseType(201)]
        [ProducesResponseType(422)]
        [Log("创建订单信息", Data = ["order"])]
        public async Task<IActionResult> CreateOrder(
            [FromBody] Order order)
        {
            var result = await orderCommandService.Create(order);
            if (result.Id > 0)
            {
                return StatusCode(201, result.Id);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }

        /// <summary>
        /// 修改订单
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpPut("{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [Log("修改订单信息", Data = ["order"])]
        public async Task<IActionResult> ModifyOrder(
            [FromRoute] int id,
            [FromBody] Order order)
        {
            order.Id = id;
            var result = await orderCommandService.Modify(order);
            if (result.Success)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }

        /// <summary>
        /// 删除订单
        /// </summary>
        /// <response code="204">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(422)]
        [Log("删除订单信息")]
        public async Task<IActionResult> DeleteOrder(
            [FromRoute] int id)
        {
            var result = await orderCommandService.Delete(Math.Abs(id), id > 0);
            if (result.Success)
            {
                return StatusCode(204);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpPut("{id}/state")]
        [ProducesResponseType(204)]
        [ProducesResponseType(422)]
        [Log("更新订单状态", Data = ["increment"])]
        public async Task<IActionResult> UpdateOrder(
            [FromRoute] int id,
            [FromBody] int increment)
        {
            var result = await orderCommandService.Update(id, increment);
            if (result.Success)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }


        /// <summary>
        /// 更新订单
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost("sendOutfeed")]
        [ProducesResponseType(204)]
        [ProducesResponseType(422)]
        [Log("创建出库任务")]
        public async Task<IActionResult> SendOutfeed(
            [FromMember] Application.Query.ViewModels.Order[] orders,
            [FromMember] Application.Query.ViewModels.Stock[] stocks)
        {
            (int Id, Failure Failure) result;
            bool bResult = true;
            string sResult = string.Empty;

            foreach (Application.Query.ViewModels.Stock stock in stocks)
            {
                if (stock.Remark != null && stock.Remark == "PK")
                {
                    continue;
                    Application.Command.ViewModels.Stock mStock = new Application.Command.ViewModels.Stock();
                    mStock.Barcode = stock.Barcode;
                    mStock.Operator = 1;
                    List<Stock.Line> lines = new List<Stock.Line>();
                    Stock.Line line = new Stock.Line();
                    //line.Order = 2003;
                    //line.Quantity = -(decimal)stock.ManagelistquantityEdited;

                    ////////////////////////////////////////////
                    IEnumerable<Application.Query.ViewModels.Order> lstOrderPK = orders.Where(p => p.Material == (int)stock.Material && p.Quantity - p.Executing - p.Finished > 0).OrderByDescending(p => p.Quantity - p.Executing - p.Finished);
                    decimal toDownloadPK = (decimal)stock.ManagelistquantityEdited;
                    foreach (var order in lstOrderPK)
                    {
                        if (order.Quantity - order.Executing - order.Finished >= toDownloadPK)
                        {
                            order.Executing += toDownloadPK;
                            line.Order = order.Line;
                            line.Id = (int)stock.Id;
                            line.Quantity = toDownloadPK;
                            break;
                        }
                        else
                        {
                            toDownloadPK -= (decimal)(order.Quantity - order.Executing - order.Finished);
                            order.Executing += (order.Quantity - order.Executing - order.Finished);
                            line.Order = order.Line;
                            line.Id = (int)stock.Id;
                            line.Quantity = toDownloadPK;
                        }
                    }
                    lines.Add(line);
                    mStock.Lines = lines;
                    mStock.Tag = "PlanLock";
                    JObject task = JObject.Parse(JsonHelper.Serialize(line));
                    await stockServiceCommand.Lock(mStock, null);
                    foreach (var l in mStock.Lines)
                    {
                        l.Quantity = -l.Quantity;
                    }
                    await stockServiceCommand.Outbound(mStock);
                    continue;
                }
                else
                {

                    Application.Command.ViewModels.Task task = new Application.Command.ViewModels.Task();
                    task.Warehouse = 1;
                    task.Barcode = stock.Barcode;
                    task.Manual = false;
                    task.Original = stock.Cell;
                    task.Destination = await warehouseService.getOutPutDestination(null, stock.Cell, 1);
                    //weijn
                    IEnumerable<Application.Query.ViewModels.Order> lstOrder = orders.Where(p => p.Material == (int)stock.Material && p.Quantity - p.Executing - p.Finished > 0).OrderByDescending(p => p.Quantity - p.Executing - p.Finished);
                    decimal toDownload = (decimal)stock.ManagelistquantityEdited;
                    foreach (var order in lstOrder)
                    {
                        if (order.Quantity - order.Executing - order.Finished >= toDownload)
                        {
                            order.Executing += toDownload;
                            task.PlanLock += $"|{order.Line},{toDownload}";
                            break;
                        }
                        else
                        {
                            toDownload -= (decimal)(order.Quantity - order.Executing - order.Finished);
                            task.PlanLock += $"|{order.Line},{order.Quantity - order.Executing - order.Finished}";
                            order.Executing += (order.Quantity - order.Executing - order.Finished);
                        }
                    }

                    if (stock.Managelistquantity < stock.ManagelistquantityEdited || stock.ManagelistquantityEdited <= 0)
                    {
                        sResult = $"出库数量:{stock.ManagelistquantityEdited} 不正确";
                        bResult = false;
                        break;
                    }
                    else
                    {
                        task.ManageListQuantity = stock.ManagelistquantityEdited;
                    }
                    //lstOrder.Executing += stock.ManagelistquantityEdited;
                    task.Tag = "PlanLockStockOut";
                    result = await taskService.Outfeed(task);
                    //stockServiceCommand.Outbound(stock, false);
                }
            }

            if (bResult)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, sResult);
            }
        }

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost("sendOutfeedPK")]
        [ProducesResponseType(204)]
        [ProducesResponseType(422)]
        [Log("创建平库任务")]
        public async Task<IActionResult> SendOutfeedPK(
            [FromMember] Application.Query.ViewModels.Order[] orders,
            [FromMember] Application.Query.ViewModels.Stock[] stocks)
        {
            (int Id, Failure Failure) result;
            bool bResult = true;
            string sResult = string.Empty;

            foreach (Application.Query.ViewModels.Stock stock in stocks)
            {
                if (stock.Remark != null && stock.Remark == "PK")
                {
                    Application.Command.ViewModels.Task task = new Application.Command.ViewModels.Task();
                    task.Warehouse = 1;
                    task.Barcode = stock.Barcode;
                    task.Manual = false;
                    task.Original = stock.Cell;
                    task.Destination = await warehouseService.getOutPutDestination(null, stock.Cell, 2);
                    //weijn
                    IEnumerable<Application.Query.ViewModels.Order> lstOrder = orders.Where(p => p.Material == (int)stock.Material && p.Quantity - p.Executing - p.Finished > 0).OrderByDescending(p => p.Quantity - p.Executing - p.Finished);
                    decimal toDownload = (decimal)stock.ManagelistquantityEdited;
                    foreach (var order in lstOrder)
                    {
                        if (order.Quantity - order.Executing - order.Finished >= toDownload)
                        {
                            order.Executing += toDownload;
                            task.PlanLock += $"|{order.Line},{toDownload}";
                            break;
                        }
                        else
                        {
                            toDownload -= (decimal)(order.Quantity - order.Executing - order.Finished);
                            task.PlanLock += $"|{order.Line},{order.Quantity - order.Executing - order.Finished}";
                            order.Executing += (order.Quantity - order.Executing - order.Finished);
                        }
                    }

                    if (stock.Managelistquantity < stock.ManagelistquantityEdited || stock.ManagelistquantityEdited <= 0)
                    {
                        sResult = $"出库数量:{stock.ManagelistquantityEdited} 不正确";
                        bResult = false;
                        break;
                    }
                    else
                    {
                        task.ManageListQuantity = stock.ManagelistquantityEdited;
                    }
                    //lstOrder.Executing += stock.ManagelistquantityEdited;
                    task.Tag = "PlanLockFeedOut";
                    result = await taskService.Outfeed(task);
                }
                if (stock.Remark != null && stock.Remark == "LOCK")
                {
                    Application.Command.ViewModels.Stock mStock = new Application.Command.ViewModels.Stock();
                    mStock.Barcode = stock.Barcode;
                    mStock.Operator = 1;
                    mStock.Tag = "PlanLock";
                    List<Stock.Line> lines = new List<Stock.Line>();

                    IEnumerable<Application.Query.ViewModels.Order> lstOrder = orders.Where(p => p.Material == (int)stock.Material && p.Quantity - p.Executing - p.Finished > 0).OrderByDescending(p => p.Quantity - p.Executing - p.Finished);
                    decimal toDownload = (decimal)stock.ManagelistquantityEdited;
                    foreach (var order in lstOrder)
                    {

                        if (order.Quantity - order.Executing - order.Finished >= toDownload)
                        {
                            order.Executing += toDownload;
                            Stock.Line line = new Stock.Line();
                            line.Quantity = toDownload;
                            line.Material = (int)stock.Material;
                            line.Id = (int)stock.Id;
                            line.Order = order.Line;
                            lines.Add(line);
                            break;
                        }
                        else
                        {
                            order.Executing += (order.Quantity - order.Executing - order.Finished);
                            toDownload -= (decimal)(order.Quantity - order.Executing - order.Finished);
                            Stock.Line line = new Stock.Line();
                            line.Quantity = (decimal)(order.Quantity - order.Executing - order.Finished);
                            line.Material = (int)stock.Material;
                            line.Id = (int)stock.Id;
                            line.Order = order.Line;
                            lines.Add(line);
                        }
                    }

                    if (stock.Managelistquantity < stock.ManagelistquantityEdited || stock.ManagelistquantityEdited <= 0)
                    {
                        sResult = $"出库数量:{stock.ManagelistquantityEdited} 不正确";
                        bResult = false;
                        break;
                    }

                    mStock.Lines = lines;
                    await stockServiceCommand.Lock(mStock, null);

                }
            }

            if (bResult)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, sResult);
            }
        }

        /// <summary>
        /// 获取归档列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("archives")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetArchivedList(
            [FromQuery] int? type,
            [FromQuery] string number,
            [FromQuery] string creater,
            [FromQuery] DateTime? createTimeFrom,
            [FromQuery] DateTime? createTimeTo,
            [FromQuery] DateTime? finalTimeFrom,
            [FromQuery] DateTime? finalTimeTo,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            var items = await orderQueryService.GetArchivedList(type, number, creater, createTimeFrom, createTimeTo, finalTimeFrom, finalTimeTo, sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await orderQueryService.GetArchivedCount(type, number, creater, createTimeFrom, createTimeTo, finalTimeFrom, finalTimeTo);
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 获取归档行列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("archives/{id}/lines")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetArchivedLineList(
            [FromRoute] int? id,
            [FromQuery] int[] category,
            [FromQuery] string code,
            [FromQuery] string name,
            [FromQuery] string batch,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            if (category.Length == 0)
            {
                category = null;
            }
            var items = await orderQueryService.GetArchivedLineList(id, category, code, name, batch, sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await orderQueryService.GetArchivedLineCount(id, category, code, name, batch);
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 导出归档列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("archives/excel")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> ExportArchived(
            [FromQuery] int? type,
            [FromQuery] string number,
            [FromQuery] string creater,
            [FromQuery] DateTime? createTimeFrom,
            [FromQuery] DateTime? createTimeTo,
            [FromQuery] DateTime? finalTimeFrom,
            [FromQuery] DateTime? finalTimeTo)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Sheet1");
            var column = 0;
            worksheet.Cells[1, ++column].Value = "单号";
            worksheet.Cells[1, ++column].Value = "类型";
            worksheet.Cells[1, ++column].Value = "制单人";
            worksheet.Cells[1, ++column].Value = "制单时间";
            worksheet.Cells[1, ++column].Value = "归档时间";
            worksheet.Cells[1, ++column].Value = "备注";
            var index = 1;
            var types = (await orderQueryService.GetTypeList()).ToDictionary(t => t.Id, t => t.Code);
            foreach (var item in await orderQueryService.GetArchivedList(type, number, creater, createTimeFrom, createTimeTo, finalTimeFrom, finalTimeTo, null, null, null))
            {
                index++;
                column = 0;
                worksheet.Cells[index, ++column].Value = item.Number;
                worksheet.Cells[index, ++column].Value = types[item.Type];
                worksheet.Cells[index, ++column].Value = item.Creater;
                worksheet.Cells[index, ++column].Value = item.CreateTime;
                worksheet.Cells[index, column].Style.Numberformat.Format = "yyyy-MM-dd";
                worksheet.Cells[index, ++column].Value = item.FinalTime;
                worksheet.Cells[index, column].Style.Numberformat.Format = "yyyy-MM-dd";
                worksheet.Cells[index, ++column].Value = item.Remark;
            }
            return File(package.GetAsByteArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        /// <summary>
        /// 导出归档行列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("archives/{id}/lines/excel")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> ExportArchivedLine(
            [FromRoute] int? id,
            [FromQuery] int[] category,
            [FromQuery] string code,
            [FromQuery] string name,
            [FromQuery] string batch)
        {
            if (category.Length == 0)
            {
                category = null;
            }
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Sheet1");
            var column = 0;
            worksheet.Cells[1, ++column].Value = "单号";
            worksheet.Cells[1, ++column].Value = "物料号";
            worksheet.Cells[1, ++column].Value = "物料名";
            worksheet.Cells[1, ++column].Value = "订单数量";
            worksheet.Cells[1, ++column].Value = "完成数量";
            worksheet.Cells[1, ++column].Value = "单位";
            worksheet.Cells[1, ++column].Value = "规格型号";
            worksheet.Cells[1, ++column].Value = "批次";
            var index = 1;
            foreach (var item in await orderQueryService.GetArchivedLineList(id, category, code, name, batch, null, null, null))
            {
                index++;
                column = 0;
                worksheet.Cells[index, ++column].Value = item.Number;
                worksheet.Cells[index, ++column].Value = item.Code;
                worksheet.Cells[index, ++column].Value = item.Name;
                worksheet.Cells[index, ++column].Value = item.Quantity;
                worksheet.Cells[index, ++column].Value = item.Finished;
                worksheet.Cells[index, ++column].Value = item.Unit;
                worksheet.Cells[index, ++column].Value = item.Model;
                worksheet.Cells[index, ++column].Value = item.Batch;
            }
            return File(package.GetAsByteArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [HttpPost("confirm")]
        [ProducesResponseType(201)]
        [ProducesResponseType(422)]
        [Log("生产确认/仓库确认", Data = ["confirmInfo"])]
        public async Task<IActionResult> BillConfirm(
            [FromBody] ConfirmInfo confirmInfo)
        {
            var result = await orderCommandService.BillConfirm(confirmInfo);
            if (result.Success)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }



        /// <summary>
        /// 99单合单
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("hedan")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> HeDan(
            [FromQuery] string[] planid,
            [FromQuery] string tag,
            [FromMiddleware] int session)
        {
            var result = await orderCommandService.Configurate(planid[0], tag, "plancode99");
            if (result.Success)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }

        /// <summary>
        /// 出库单指定出库月台
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("platform")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> SelectStation(
            string plancode,
            string no,
            string truck,
            [FromMiddleware] int session)
        {
            string platform = no + "," + truck;
            //switch (no)
            //{
            //    case "0":
            //        platform = "001";
            //        break;
            //    case "1":
            //        platform = "002";
            //        break;
            //    case "2":
            //        platform = "003";
            //        break;
            //    default:
            //        break;
            //}
            var planMainList = await orderQueryService.GetOrderListBy204099(2, plancode);
            string idS = string.Empty;
            foreach (var item in planMainList)
            {
                idS += item.Id + ",";
            }

            var result = await orderCommandService.Configurate(idS.TrimEnd(','), platform, "platform");
            if (result.Success)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }


        }
    }
}
