﻿using AutoMapper;
using Domain_KSF.Models;
using Kean.Domain.Stock.Models;
using Kean.Infrastructure.Configuration;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace Kean.Infrastructure.Repository
{
    /// <summary>
    /// 模型映射配置
    /// </summary>
    public sealed class AutoMapper : Profile
    {
        /// <summary>
        /// 初始化 Kean.Application.Repository.AutoMapper 类的新实例
        /// </summary>
        public AutoMapper()
        {
            AllowNullCollections = true;

            CreateMap<Domain.Basic.Models.Role, T_SYS_ROLE>()
                .ForMember(entity => entity.ROLE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.ROLE_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.ROLE_REMARK, options => options.MapFrom(model => model.Remark));

            CreateMap<Domain.Basic.Models.User, T_SYS_USER>()
                .ForMember(entity => entity.USER_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.USER_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.USER_ACCOUNT, options => options.MapFrom(model => model.Account));

            CreateMap<Domain.Material.Models.Material, T_GOODS_MAIN>()
                .IncludeBase<MaterialProperty, Database.Repository.Default.Entities.Abstract.T_GOODS_MAIN>()
                .ForMember(entity => entity.GOODS_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.CLASS_ID, options => options.MapFrom(model => model.Category))
                .ForMember(entity => entity.TYPE_ID, options => options.MapFrom(model => model.Group))
                .ForMember(entity => entity.GOODS_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(entity => entity.GOODS_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.PALLET_QUANTITY, options => options.MapFrom(model => model.PalletQuantity));

            CreateMap<Domain.Material.Models.Category, T_GOODS_CLASS>()
                .ForMember(entity => entity.CLASS_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.CLASS_PARENT_ID, options => options.MapFrom(model => model.Parent))
                .ForMember(entity => entity.CLASS_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(entity => entity.CLASS_NAME, options => options.MapFrom(model => model.Name));

            CreateMap<Domain.Material.Models.Safety, T_GOODS_SAFETY>()
                .ForMember(entity => entity.SAFETY_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.GOODS_ID, options => options.MapFrom(model => model.Material))
                .ForMember(entity => entity.WAREHOUSE_ID, options => options.MapFrom(model => string.Join(',', model.Warehouse.OrderBy(i => i))))
                .ForMember(entity => entity.LOWER_LIMIT, options => options.MapFrom(model => model.LowerLimit))
                .ForMember(entity => entity.UPPER_LIMIT, options => options.MapFrom(model => model.UpperLimit));

            CreateMap<Domain.Order.Models.Type, T_PLAN_TYPE>()
                .ForMember(entity => entity.TYPE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.TYPE_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(entity => entity.TYPE_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.TYPE_NO, options => options.MapFrom(model => model.Number))
                .ReverseMap()
                .ForMember(model => model.Process, options => options.MapFrom(entity => entity.TYPE_PROC.Split('-', StringSplitOptions.None)));

            CreateMap<Domain.Order.Models.Order, T_PLAN_MAIN>()
                .ForMember(entity => entity.PLAN_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.PLAN_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.PLAN_CODE, options => options.MapFrom(model => model.Number))
                .ForMember(entity => entity.PLAN_CREATER, options => options.MapFrom(model => model.Creater))
                .ForMember(entity => entity.PLAN_CREATE_TIME, options => options.MapFrom(model => model.CreateTime))
                .ForMember(entity => entity.PLAN_REMARK, options => options.MapFrom(model => model.Remark))
                .ForMember(entity => entity.TRUCK_INFO, options => options.MapFrom(model => model.TruckInfo))
                .ForMember(entity => entity.PLAN_STATUS, options => options.MapFrom(model => model.State))
                .ReverseMap();

            CreateMap<Domain.Order.Models.Order, T_PLAN_HIS_MAIN>()
                .IncludeBase<Domain.Order.Models.Order, T_PLAN_MAIN>()
                .ForMember(entity => entity.PLAN_ID, options => options.MapFrom(model => model.Id));

            CreateMap<Domain.Order.Models.OrderLine, T_PLAN_LIST>()
                .IncludeBase<MaterialProperty, Database.Repository.Default.Entities.Abstract.T_GOODS_PROPERTY>()
                .ForMember(entity => entity.PLAN_LIST_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.STORAGE_LIST_ID, options => options.MapFrom(model => model.Stock))
                .ForMember(entity => entity.GOODS_ID, options => options.MapFrom(model => model.Material))
                .ForMember(entity => entity.PLANNED_QUANTITY, options => options.MapFrom(model => model.Quantity))
                .ForMember(entity => entity.ORDERED_QUANTITY, options => options.MapFrom(model => model.Executing))
                .ForMember(entity => entity.FINISHED_QUANTITY, options => options.MapFrom(model => model.Finished))
                .ForMember(entity => entity.PLAN_LIST_REPOCONFIRM, options => options.MapFrom(model => model.RepoConfirm))
                .ForMember(entity => entity.PLAN_LIST_PRODCONFIRM, options => options.MapFrom(model => model.ProdConfirm))

                .ForMember(entity => entity.PLANT_CODE, options => options.MapFrom(model => model.PlantCode))
                .ForMember(entity => entity.PRODUCTION_LINE, options => options.MapFrom(model => model.ProductionLine))
                .ForMember(entity => entity.CLIENT_CODE, options => options.MapFrom(model => model.ClientCode))
                .ForMember(entity => entity.WAREHOUSE_CODE, options => options.MapFrom(model => model.WarehouseCode))
                .ForMember(entity => entity.FINANCIAL_POST_STATUS, options => options.MapFrom(model => model.FinancialPostStatus))
                .ForMember(entity => entity.TRUCK_INFO, options => options.MapFrom(model => model.TruckInfo))
                .ForMember(entity => entity.TRANSFER_TYPE, options => options.MapFrom(model => model.TransferType))
                .ForMember(entity => entity.WORK_GROUP, options => options.MapFrom(model => model.WorkGroup))
                .ForMember(entity => entity.ADJUST_QUANTITY, options => options.MapFrom(model => model.AdjustQuantity))
                .ForMember(entity => entity.ADJUST_REASON, options => options.MapFrom(model => model.AdjustReason))
                .ForMember(entity => entity.FINANCIAL_POST_QUANTITY, options => options.MapFrom(model => model.FinancialPostQuantity))
                .ForMember(entity => entity.PLAN_LIST_REPOCONFIRM_TIME, options => options.MapFrom(model => model.PlanListRepoTime))
                .ForMember(entity => entity.PLAN_LIST_PRODCONFIRM_TIME, options => options.MapFrom(model => model.PlanListProdTime))
                .ForMember(entity => entity.FINANCIAL_POST_TIMES, options => options.MapFrom(model => model.FinancialPostTime))
                .ForMember(entity => entity.FINANCIAL_POST_NO, options => options.MapFrom(model => model.FinancialPostNo))
                .ForMember(entity => entity.FINANCIAL_POST_LATEST_TIME, options => options.MapFrom(model => model.FinancialPostLatestTime))
                .ForMember(entity => entity.FINANCIAL_POST_UPLOAD_TIME, options => options.MapFrom(model => model.FinancialPostUploadTime))
                .ForMember(entity => entity.IMPORT_TIME, options => options.MapFrom(model => model.ImportTime))
                .ForMember(entity => entity.SAP_NO, options => options.MapFrom(model => model.SapNo))
                .ReverseMap();


            CreateMap<Domain.Order.Models.OrderLine, T_PLAN_HIS_LIST>()
                    .IncludeBase<Domain.Order.Models.OrderLine, T_PLAN_LIST>()
                    .ForMember(entity => entity.PLAN_LIST_ID, options => options.MapFrom(model => model.Id));

            CreateMap<Domain.Stock.Models.Stock, T_STORAGE_MAIN>()
                .ForMember(entity => entity.STOCK_BARCODE, options => options.MapFrom(model => model.Barcode))
                .ForMember(entity => entity.STOCK_SPEC, options => options.MapFrom(model => model.Spec))
                .ForMember(entity => entity.CELL_ID, options => options.MapFrom(model => model.Cell))
                .ForMember(entity => entity.FULL_FLAG, options => options.MapFrom(model => model.Full))
                .ReverseMap();

            CreateMap<Domain.Stock.Models.StockLine, T_STORAGE_LIST>()
                .IncludeBase<MaterialProperty, Database.Repository.Default.Entities.Abstract.T_GOODS_PROPERTY>()
                .ForMember(entity => entity.STORAGE_LIST_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.PLAN_LIST_ID, options => options.MapFrom(model => model.Order))
                .ForMember(entity => entity.GOODS_ID, options => options.MapFrom(model => model.Material))
                .ForMember(entity => entity.STORAGE_LIST_QUANTITY, options => options.MapFrom(model => model.Quantity))
                .ForMember(entity => entity.INBOUND_TIME, options => options.MapFrom(model => model.InboundTime))
                .ForMember(entity => entity.INVENTORY_TIME, options => options.MapFrom(model => model.InventoryTime))
                .ForMember(entity => entity.AVAILABLE_TIME, options => options.MapFrom(model => model.AvailableTime))
                .ForMember(entity => entity.UNAVAILABLE_TIME, options => options.MapFrom(model => model.UnavailableTime))
                .ForMember(entity => entity.GOODS_EFF_TIME, options => options.MapFrom(model => model.EffectiveTime))
                .ForMember(entity => entity.GOODS_EXP_TIME, options => options.MapFrom(model => model.ExpirationTime))
                .ForMember(entity => entity.STORAGE_LIST_FLAG, options => options.MapFrom(model => model.Enabled))
                .ReverseMap();

            CreateMap<Domain.Stock.Models.Record, T_RECORD_MAIN>()
                .ForMember(entity => entity.RECORD_TYPE, options => options.MapFrom(model => model.Transaction))
                .ForMember(entity => entity.RECORD_TAG, options => options.MapFrom(model => model.Tag))
                .ForMember(entity => entity.STOCK_BARCODE, options => options.MapFrom(model => model.Barcode))
                .ForMember(entity => entity.START_CELL_ID, options => options.MapFrom(model => model.Original))
                .ForMember(entity => entity.END_CELL_ID, options => options.MapFrom(model => model.Destination))
                .ForMember(entity => entity.BEGIN_TIME, options => options.MapFrom(model => model.BeginTime))
                .ForMember(entity => entity.END_TIME, options => options.MapFrom(model => model.EndTime))
                .ForMember(entity => entity.OPERATOR, options => options.MapFrom(model => model.Operator));

            CreateMap<Domain.Stock.Models.StockLine, T_RECORD_LIST>()
                .IncludeBase<MaterialProperty, Database.Repository.Default.Entities.Abstract.T_GOODS_PROPERTY>()
                .ForMember(entity => entity.STORAGE_LIST_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.PLAN_LIST_ID, options => options.MapFrom(model => model.Order))
                .ForMember(entity => entity.GOODS_ID, options => options.MapFrom(model => model.Material))
                .ForMember(entity => entity.RECORD_LIST_QUANTITY, options => options.MapFrom(model => model.Quantity))
                .ForMember(entity => entity.INBOUND_TIME, options => options.MapFrom(model => model.InboundTime))
                .ForMember(entity => entity.AVAILABLE_TIME, options => options.MapFrom(model => model.AvailableTime))
                .ForMember(entity => entity.UNAVAILABLE_TIME, options => options.MapFrom(model => model.UnavailableTime))
                .ForMember(entity => entity.INVENTORY_AGE, options => options.MapFrom(model => DateTime.Now.Subtract(model.InboundTime).TotalHours))
                .ForMember(entity => entity.GOODS_AGE, options => options.MapFrom(model => model.ManufacturingDate.HasValue ? DateTime.Now.Subtract(model.ManufacturingDate.Value).TotalHours : default(double?)))
                .ForMember(entity => entity.GOODS_EFF_TIME, options => options.MapFrom(model => model.EffectiveTime))
                .ForMember(entity => entity.GOODS_EXP_TIME, options => options.MapFrom(model => model.ExpirationTime));

            CreateMap<Domain.Task.Models.Task, T_MANAGE_MAIN>()
                .ForMember(entity => entity.MANAGE_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.MANAGE_TAG, options => options.MapFrom(model => model.Tag))
                .ForMember(entity => entity.WAREHOUSE_ID, options => options.MapFrom(model => model.Warehouse))
                .ForMember(entity => entity.STOCK_BARCODE, options => options.MapFrom(model => model.Barcode))
                .ForMember(entity => entity.STOCK_SPEC, options => options.MapFrom(model => model.Spec))
                .ForMember(entity => entity.START_CELL_ID, options => options.MapFrom(model => model.Original))
                .ForMember(entity => entity.END_CELL_ID, options => options.MapFrom(model => model.Destination))
                .ForMember(entity => entity.PRIORITY, options => options.MapFrom(model => model.Priority))
                .ForMember(entity => entity.PREVIOUS_ID, options => options.MapFrom(model => model.Previous))
                .ForMember(entity => entity.OPERATOR, options => options.MapFrom(model => model.Operator))
                .ForMember(entity => entity.BEGIN_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.MANAGE_REMARK, options => options.MapFrom(model => model.Remark))
                .ForMember(entity => entity.MANAGE_EXTERNAL, options => options.MapFrom(model => model.External))
                .ForMember(entity => entity.PLAN_ID, options => options.MapFrom(model => model.PlanId))
                .ReverseMap()
                .ForMember(model => model.Id, options => options.MapFrom(entity => entity.MANAGE_ID))
                .ForMember(model => model.State, options => options.MapFrom(entity => entity.MANAGE_STATUS));

            CreateMap<Domain.Task.Models.Trigger, T_MANAGE_TRIGGER>()
                .ForMember(entity => entity.TRIGGER_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.TRIGGER_TIMEOUT, options => options.MapFrom(model => model.Timeout))
                .ForMember(entity => entity.TRIGGER_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.TRIGGER_WAREHOUSE, options => options.MapFrom(model => model.Warehouse))
                .ForMember(entity => entity.TRIGGER_DEVICE, options => options.MapFrom(model => model.Device))
                .ForMember(entity => entity.TRIGGER_PARAM, options => options.MapFrom(model => model.Parameter == null ? null : model.Parameter.ToString(Formatting.None)))
                .ForMember(entity => entity.EXEC_COUNT, options => options.MapFrom(model => model.Executed))
                .ForMember(entity => entity.EXEC_REMARK, options => options.MapFrom(model => model.Remark))
                .ForMember(entity => entity.TRIGGER_PARAM_01, options => options.MapFrom(model => model.Parameter01))
                .ForMember(entity => entity.TRIGGER_PARAM_02, options => options.MapFrom(model => model.Parameter02))
                .ReverseMap()
                .ForMember(model => model.Id, options => options.MapFrom(entity => entity.TRIGGER_ID))
                .ForMember(model => model.Parameter, options => options.MapFrom(entity => entity.TRIGGER_PARAM == null ? null : JObject.Parse(entity.TRIGGER_PARAM)));

            CreateMap<Domain.Task.Models.Trigger, T_MANAGE_TRIGGER_HIS>()
                .ForMember(entity => entity.TRIGGER_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.TRIGGER_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.TRIGGER_TIMEOUT, options => options.MapFrom(model => model.Timeout))
                .ForMember(entity => entity.TRIGGER_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.TRIGGER_WAREHOUSE, options => options.MapFrom(model => model.Warehouse))
                .ForMember(entity => entity.TRIGGER_DEVICE, options => options.MapFrom(model => model.Device))
                .ForMember(entity => entity.TRIGGER_PARAM, options => options.MapFrom(model => model.Parameter == null ? null : model.Parameter.ToString(Formatting.None)))
                .ForMember(entity => entity.EXEC_RESULT, options => options.MapFrom(model => model.Result.ToString()))
                .ForMember(entity => entity.EXEC_COUNT, options => options.MapFrom(model => model.Executed))
                .ForMember(entity => entity.TRIGGER_PARAM_01, options => options.MapFrom(model => model.Parameter01))
                .ForMember(entity => entity.TRIGGER_PARAM_02, options => options.MapFrom(model => model.Parameter02))
                .ForMember(entity => entity.EXEC_REMARK, options => options.MapFrom(model => model.Remark));

            CreateMap<T_WH_CELL, Domain.Task.Models.Station>()
                .ForMember(model => model.Id, options => options.MapFrom(entity => entity.CELL_ID))
                .ForMember(model => model.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(model => model.Area, options => options.MapFrom(entity => entity.AREA_ID))
                .ForMember(model => model.Name, options => options.MapFrom(entity => entity.CELL_NAME))
                .ForMember(model => model.Device, options => options.MapFrom(entity => entity.CELL_CODE))
                .ForMember(model => model.AllowIn, options => options.MapFrom(entity => entity.CELL_IN))
                .ForMember(model => model.AllowOut, options => options.MapFrom(entity => entity.CELL_OUT))
                .ForMember(model => model.Inner, options => options.MapFrom(entity => entity.CELL_INNER))
                .ForMember(model => model.Outer, options => options.MapFrom(entity => entity.CELL_OUTER))
                .ForMember(model => model.Laneway, options => options.MapFrom(entity => entity.CELL_LANEWAY.Split(',', StringSplitOptions.None)))
                .ForMember(model => model.Spec, options => options.MapFrom(entity => entity.CELL_SPEC))
                .ForMember(model => model.Pallet, options => options.MapFrom((entity, _) => entity.CELL_PALLET?.Split('(', ')').Where(s => !string.IsNullOrWhiteSpace(s))))
                .ForMember(model => model.Edge, options => options.MapFrom(entity => entity.CELL_EDGE))
                .ForMember(model => model.Row, options => options.MapFrom(entity => entity.CELL_Z))
                .ForMember(model => model.Column, options => options.MapFrom(entity => entity.CELL_X))
                .ForMember(model => model.Layer, options => options.MapFrom(entity => entity.CELL_Y));

            CreateMap<T_WH_CELL, Domain.Task.Models.Cell>()
                .ForMember(model => model.Id, options => options.MapFrom(entity => entity.CELL_ID))
                .ForMember(model => model.Warehouse, options => options.MapFrom(entity => entity.WAREHOUSE_ID))
                .ForMember(model => model.Area, options => options.MapFrom(entity => entity.AREA_ID))
                .ForMember(model => model.Name, options => options.MapFrom(entity => entity.CELL_NAME))
                .ForMember(model => model.Device, options => options.MapFrom(entity => entity.CELL_CODE))
                .ForMember(model => model.Inner, options => options.MapFrom(entity => entity.CELL_INNER))
                .ForMember(model => model.Outer, options => options.MapFrom(entity => entity.CELL_OUTER))
                .ForMember(model => model.Laneway, options => options.MapFrom(entity => entity.CELL_LANEWAY))
                .ForMember(model => model.Spec, options => options.MapFrom(entity => entity.CELL_SPEC))
                .ForMember(model => model.Pallet, options => options.MapFrom((entity, _) => entity.CELL_PALLET?.Split('(', ')').Where(s => !string.IsNullOrWhiteSpace(s))))
                .ForMember(model => model.Edge, options => options.MapFrom(entity => entity.CELL_EDGE))
                .ForMember(model => model.Row, options => options.MapFrom(entity => entity.CELL_Z))
                .ForMember(model => model.Column, options => options.MapFrom(entity => entity.CELL_X))
                .ForMember(model => model.Layer, options => options.MapFrom(entity => entity.CELL_Y))
                .ForMember(model => model.Deep, options => options.MapFrom(entity => entity.CELL_DEEP))
                .ForMember(model => model.State, options => options.MapFrom(entity => entity.RUN_STATUS))
                .ForMember(model => model.IsEmpty, options => options.MapFrom(entity => entity.CELL_STATUS == "Empty"))
                .ForMember(model => model.Version, options => options.MapFrom(entity => entity.STATUS_VERSION))
                .ForMember(model => model.Degree, options => options.MapFrom(entity => entity.CELL_HOTDEGREE));

            CreateMap<Domain.Interface.Models.Protocol, T_INF_INPUT>()
                .ForMember(entity => entity.INPUT_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.INPUT_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.INPUT_TIMEOUT, options => options.MapFrom(model => model.Timeout))
                .ForMember(entity => entity.INPUT_SOURCE, options => options.MapFrom(model => model.Scope))
                .ForMember(entity => entity.INPUT_FUNC, options => options.MapFrom(model => model.Function))
                .ForMember(entity => entity.INPUT_UNIQUE, options => options.MapFrom(model => model.Unique))
                .ForMember(entity => entity.INPUT_DATA, options => options.MapFrom(model => model.Data == null ? null : JsonHelper.Serialize(model.Data)))
                .ForMember(entity => entity.INPUT_MSG, options => options.MapFrom(model => model.Message))
                .ForMember(entity => entity.EXEC_COUNT, options => options.MapFrom(model => model.Executed))
                .ReverseMap()
                .ForMember(model => model.Data, options => options.MapFrom(entity => entity.INPUT_DATA == null ? null : JsonHelper.Deserialize(entity.INPUT_DATA)));

            CreateMap<Domain.Interface.Models.Protocol, T_INF_OUTPUT>()
                .ForMember(entity => entity.OUTPUT_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.OUTPUT_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.OUTPUT_TIMEOUT, options => options.MapFrom(model => model.Timeout))
                .ForMember(entity => entity.OUTPUT_TARGET, options => options.MapFrom(model => model.Scope))
                .ForMember(entity => entity.OUTPUT_FUNC, options => options.MapFrom(model => model.Function))
                .ForMember(entity => entity.OUTPUT_UNIQUE, options => options.MapFrom(model => model.Unique))
                .ForMember(entity => entity.OUTPUT_DATA, options => options.MapFrom(model => model.Data == null ? null : JsonHelper.Serialize(model.Data)))
                .ForMember(entity => entity.OUTPUT_MSG, options => options.MapFrom(model => model.Message))
                .ForMember(entity => entity.EXEC_COUNT, options => options.MapFrom(model => model.Executed))
                .ReverseMap()
                .ForMember(model => model.Data, options => options.MapFrom(entity => entity.OUTPUT_DATA == null ? null : JsonHelper.Deserialize(entity.OUTPUT_DATA)));

            CreateMap<Domain.Interface.Models.Protocol, T_INF_HIS>()
                .ForMember(entity => entity.INF_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.INF_SCOPE, options => options.MapFrom(model => model.Scope))
                .ForMember(entity => entity.INF_FUNC, options => options.MapFrom(model => model.Function))
                .ForMember(entity => entity.INF_UNIQUE, options => options.MapFrom(model => model.Unique))
                .ForMember(entity => entity.INF_MSG, options => options.MapFrom(model => model.Message))
                .ForMember(entity => entity.INF_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.LOG_TIME, options => options.MapFrom(model => DateTime.Now))
                .ForMember(entity => entity.EXEC_INDEX, options => options.MapFrom(model => model.Executed))
                .ForMember(entity => entity.EXEC_RESULT, options => options.MapFrom(model => model.Result))
                .ForMember(entity => entity.EXEC_REMARK, options => options.MapFrom(model => model.Remark));

            CreateMap<Domain.Device.Models.Device, T_DEVICE_MAIN>()
                .ForMember(entity => entity.DEVICE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.DEVICE_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.DEVICE_DRIVER, options => options.MapFrom(model => model.Driver.Name))
                .ForMember(entity => entity.DEVICE_PARAMS, options => options.MapFrom(model => model.Parameter.ToString(Formatting.None)))
                .ForMember(entity => entity.DEVICE_ROUTES, options => options.MapFrom(model => string.Join(',', model.Routes)))
                .ForMember(entity => entity.DEVICE_CAPACITY, options => options.MapFrom(model => model.Capacity))
                .ForMember(entity => entity.DEVICE_LOADED, options => options.MapFrom(model => model.Loaded))
                .ForMember(entity => entity.DEVICE_STATUS, options => options.MapFrom(model => model.State))
                .ForMember(entity => entity.DEVICE_ERROR, options => options.MapFrom(model => model.Error))
                .ForMember(entity => entity.DEVICE_TASK, options => options.MapFrom(model => model.Instruction))
                .ForMember(entity => entity.DEVICE_LOCK, options => options.MapFrom(model => string.Join(',', model.Locked)))
                .ForMember(entity => entity.DEVICE_DATA, options => options.MapFrom(model => model.Data.ToString(Formatting.None)))
                .ForMember(entity => entity.DEVICE_FLAG, options => options.MapFrom(model => model.Enabled))
                .ForMember(entity => entity.DEVICE_STATUS_TIME, options => options.MapFrom(model => model.Timestamp))
                .ReverseMap()
                .ForMember(model => model.Driver, options => options.MapFrom(entity => Domain.Device.Drivers.Factory.LoadDriverType(entity.DEVICE_DRIVER)))
                .ForMember(model => model.Parameter, options => options.MapFrom(entity => JObject.Parse(entity.DEVICE_PARAMS)))
                .ForMember(model => model.Routes, options => options.MapFrom(entity => JsonHelper.Deserialize<int[]>($"[{entity.DEVICE_ROUTES}]")))
                .ForMember(model => model.Locked, options => options.MapFrom(entity => JsonHelper.Deserialize<int[]>($"[{entity.DEVICE_LOCK}]")))
                .ForMember(model => model.Data, options => options.MapFrom(entity => JObject.Parse(entity.DEVICE_DATA)));

            CreateMap<Domain.Device.Models.Route, T_DEVICE_ROUTE>()
                .ForMember(entity => entity.ROUTE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.ROUTE_START, options => options.MapFrom(model => model.Start))
                .ForMember(entity => entity.ROUTE_END, options => options.MapFrom(model => model.End))
                .ForMember(entity => entity.ROUTE_WEIGHT, options => options.MapFrom(model => model.Weight))
                .ForMember(entity => entity.ROUTE_LOAD, options => options.MapFrom(model => model.Load))
                .ForMember(entity => entity.ROUTE_STATUS, options => options.MapFrom(model => model.Healthy))
                .ForMember(entity => entity.ROUTE_FLAG, options => options.MapFrom(model => model.Enabled))
                .ReverseMap();

            CreateMap<Domain.Device.Models.Instruction, T_DEVICE_TASK>()
                .ForMember(entity => entity.TASK_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.TASK_GROUP, options => options.MapFrom(model => model.Task))
                .ForMember(entity => entity.TASK_BARCODE, options => options.MapFrom(model => model.Barcode))
                .ForMember(entity => entity.TASK_LEVEL, options => options.MapFrom(model => model.Priority))
                .ForMember(entity => entity.TASK_ORDER, options => options.MapFrom(model => model.Index))
                .ForMember(entity => entity.TASK_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(entity => entity.TASK_START, options => options.MapFrom(model => model.Original))
                .ForMember(entity => entity.TASK_START_DEVICE, options => options.MapFrom(model => model.OriginalDevice))
                .ForMember(entity => entity.TASK_START_COORD, options => options.MapFrom(model => model.OriginalCoordinate))
                .ForMember(entity => entity.TASK_END, options => options.MapFrom(model => model.Destination))
                .ForMember(entity => entity.TASK_END_DEVICE, options => options.MapFrom(model => model.DestinationDevice))
                .ForMember(entity => entity.TASK_END_COORD, options => options.MapFrom(model => model.DestinationCoordinate))
                .ForMember(entity => entity.TASK_PARAMS, options => options.MapFrom(model => model.Parameters))
                .ForMember(entity => entity.TASK_ROUTE, options => options.MapFrom(model => model.Route))
                .ForMember(entity => entity.TASK_NUMBER, options => options.MapFrom(model => model.Number))
                .ForMember(entity => entity.TASK_TIME, options => options.MapFrom(model => model.Timestamp))
                .ForMember(entity => entity.TASK_STATUS, options => options.MapFrom(model => model.State))
                .ReverseMap()
                .ForMember(model => model.Feedback, options => options.MapFrom(entity => entity.START_TIME));

            CreateMap<Domain.Basic.Models.Warehouse, T_WH_WAREHOUSE>()
                .ForMember(entity => entity.WAREHOUSE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.WAREHOUSE_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(entity => entity.WAREHOUSE_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.WAREHOUSE_REMARK, options => options.MapFrom(model => model.Remark));

            CreateMap<Domain.Basic.Models.Area, T_WH_AREA>()
                .ForMember(viewmodel => viewmodel.AREA_ID, options => options.MapFrom(model => model.Id))
                .ForMember(viewmodel => viewmodel.WAREHOUSE_ID, options => options.MapFrom(model => model.Warehouse))
                .ForMember(viewmodel => viewmodel.AREA_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(viewmodel => viewmodel.AREA_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(viewmodel => viewmodel.AREA_REMARK, options => options.MapFrom(model => model.Remark));

            CreateMap<ClientCate, T_SYS_CLIENT_CATE>()
                .ForMember(entity => entity.CLIENTCATE_ID, options => options.MapFrom(model => model.Id))
                .ForMember(entity => entity.CLIENTCATE_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(entity => entity.CLIENTCATE_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(entity => entity.CLIENTCATE_BEGIN_DATE_COUNT, options => options.MapFrom(model => model.BeginDateCount))
                .ForMember(entity => entity.CLIENTCATE_FINAL_DATE_COUNT, options => options.MapFrom(model => model.FinalDateCount))
                .ForMember(entity => entity.CLIENTCATE_MODEL, options => options.MapFrom(model => model.Model));

            CreateMap<ClientInfo, T_SYS_CLIENT_INFO>()
                .ForMember(viewmodel => viewmodel.CLIENTINFO_ID, options => options.MapFrom(model => model.Id))
                .ForMember(viewmodel => viewmodel.CLIENTINFO_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(viewmodel => viewmodel.CLIENTINFO_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(viewmodel => viewmodel.CLIENTINFO_SHORTNAME, options => options.MapFrom(model => model.ShortName))
                .ForMember(viewmodel => viewmodel.CLIENTCATE_ID, options => options.MapFrom(model => model.ClientCateId));

            CreateMap<Platform, T_SYS_PLATFORM>()
                .ForMember(viewmodel => viewmodel.PLATFORM_ID, options => options.MapFrom(model => model.Id))
                .ForMember(viewmodel => viewmodel.PLATFORM_CODE, options => options.MapFrom(model => model.Code))
                .ForMember(viewmodel => viewmodel.PLATFORM_NAME, options => options.MapFrom(model => model.Name))
                .ForMember(viewmodel => viewmodel.PLATFORM_STATUS, options => options.MapFrom(model => model.Status));

            CreateMap<Apply, IO_CONTROL_APPLY>()
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_ID, options => options.MapFrom(model => model.Id))
                .ForMember(viewmodel => viewmodel.CONTROL_ID, options => options.MapFrom(model => model.ControlId))
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(viewmodel => viewmodel.WAREHOUSE_CODE, options => options.MapFrom(model => model.WarehouseCode))
                .ForMember(viewmodel => viewmodel.DEVICE_CODE, options => options.MapFrom(model => model.DeviceCode))
                .ForMember(viewmodel => viewmodel.STOCK_BARCODE, options => options.MapFrom(model => model.Stockbarcode))
                .ForMember(viewmodel => viewmodel.APPLY_TASK_STATUS, options => options.MapFrom(model => model.Status))
                .ForMember(viewmodel => viewmodel.CREATE_TIME, options => options.MapFrom(model => model.CreateTime))
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_PARAMETER, options => options.MapFrom(model => model.Parameter))
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_REMARK, options => options.MapFrom(model => model.Remark))
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_PARA01, options => options.MapFrom(model => model.Para01))
                .ForMember(viewmodel => viewmodel.CONTROL_APPLY_PARA02, options => options.MapFrom(model => model.Para02));
            CreateMap<T_PLAN_LIST, T_MANAGE_LIST>();
            CreateMap<Control, IO_CONTROL>()
                .ForMember(viewmodel => viewmodel.CONTROL_ID, options => options.MapFrom(model => model.Id))
                .ForMember(viewmodel => viewmodel.RELATIVE_CONTROL_ID, options => options.MapFrom(model => model.RelativeControlId))
                .ForMember(viewmodel => viewmodel.MANAGE_ID, options => options.MapFrom(model => model.ManageId))
                .ForMember(viewmodel => viewmodel.STOCK_BARCODE, options => options.MapFrom(model => model.Stockbarcode))
                .ForMember(viewmodel => viewmodel.CONTROL_TASK_TYPE, options => options.MapFrom(model => model.Type))
                .ForMember(viewmodel => viewmodel.CONTROL_TASK_LEVEL, options => options.MapFrom(model => model.Level))
                .ForMember(viewmodel => viewmodel.START_WAREHOUSE_CODE, options => options.MapFrom(model => model.StartWarehouseCode))
                .ForMember(viewmodel => viewmodel.START_DEVICE_CODE, options => options.MapFrom(model => model.StartDeviceCode))
                .ForMember(viewmodel => viewmodel.END_WAREHOUSE_CODE, options => options.MapFrom(model => model.EndWarehouseCode))
                .ForMember(viewmodel => viewmodel.END_DEVICE_CODE, options => options.MapFrom(model => model.EndDeviceCode))
                .ForMember(viewmodel => viewmodel.PRE_CONTROL_STATUS, options => options.MapFrom(model => model.PreStatus))
                .ForMember(viewmodel => viewmodel.CONTROL_STATUS, options => options.MapFrom(model => model.Status))
                .ForMember(viewmodel => viewmodel.ERROR_TEXT, options => options.MapFrom(model => model.ErrorText))
                .ForMember(viewmodel => viewmodel.CONTROL_BEGIN_TIME, options => options.MapFrom(model => model.BeginTime))
                .ForMember(viewmodel => viewmodel.CONTROL_END_TIME, options => options.MapFrom(model => model.EndTime))
                .ForMember(viewmodel => viewmodel.CONTROL_REMARK, options => options.MapFrom(model => model.Remark));




        }
    }
}
