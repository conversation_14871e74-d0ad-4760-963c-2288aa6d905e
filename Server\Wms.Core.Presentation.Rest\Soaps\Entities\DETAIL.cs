﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Drawing;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using static Org.BouncyCastle.Pqc.Crypto.Utilities.PqcOtherInfoGenerator;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    public class DETAIL
    {
        [DataMember] public string EBELN { get; set; }//单据号码
        [DataMember] public string EINDT { get; set; }//项目收货日期（ETA）
        [DataMember] public string POSNR { get; set; } //项目
        [DataMember] public string LTEXT { get; set; } //项目文本
        [DataMember] public string PSTYV { get; set; } //项目类别
        [DataMember] public string MATNR { get; set; } //物料
        [DataMember] public string MENGE { get; set; }//数量
        [DataMember] public string MEINS { get; set; }//单据单位
        [DataMember] public string NETPR { get; set; } //单价
        [DataMember] public string WERKS { get; set; }//工厂
        [DataMember] public string NAME1 { get; set; }//工厂名称
        [DataMember] public string LGORT { get; set; }//库存地点
        [DataMember] public string LGOBE { get; set; }//库存地点描述
        [DataMember] public string INSMK { get; set; }//库存类型
        [DataMember] public string CHARG { get; set; }//建议批号

    }
}
