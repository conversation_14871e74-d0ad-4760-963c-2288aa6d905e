﻿using AutoMapper;
using Kean.Application.Query.Interfaces;
using Kean.Application.Query.ViewModels;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.Utilities;
using MySqlX.XDevAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lines = Kean.Application.Command.ViewModels.Stock.Line;

namespace Kean.Application.Query.Implements
{
    /// <summary>
    /// 库存信息查询服务实现
    /// </summary>
    public sealed class StockService(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : IStockService
    {
        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetStockCount 方法
         */
        public async Task<int> GetStockCount(int[] area, bool? pallet, bool? palletized, int[] category, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled, bool? excludeTask)
        {
            return (int)(await GetStockSchema(area, pallet, palletized, category, code, name, batch, barcode, cell, qc, inboundFrom, inboundTo, inventoryFrom, inventoryTo, overdue, enabled, excludeTask)
                .Single(s => new { Count = Function.Count(s.STORAGE_LIST_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetStockList 方法
         */
        public async Task<IEnumerable<Stock>> GetStockList(int[] area, bool? pallet, bool? palletized, int[] category, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled, bool? excludeTask, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Stock>>(await GetStockSchema(area, pallet, palletized, category, code, name, batch, barcode, cell, qc, inboundFrom, inboundTo, inventoryFrom, inventoryTo, overdue, enabled, excludeTask)
                .Sort<V_STORAGE_LIST, Stock>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetStock 相关方法的条件
         */
        private ISchema<V_STORAGE_LIST> GetStockSchema(int[] area, bool? pallet, bool? palletized, int[] category, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled, bool? excludeTask)
        {
            var schema = database.From<V_STORAGE_LIST>();
            if (area != null)
            {
                schema = schema.Where(s => area.Contains(s.AREA_ID));
            }
            if (pallet.HasValue)
            {
                schema = pallet.Value ?
                    schema.Where(s => s.STORAGE_LIST_ID == null && !s.STOCK_BARCODE.StartsWith("#@")) :
                    schema.Where(s => s.STORAGE_LIST_ID != null);
            }
            if (palletized.HasValue)
            {
                schema = palletized.Value ?
                    schema.Where(s => !s.STOCK_BARCODE.StartsWith("#@")) :
                    schema.Where(s => s.STOCK_BARCODE.StartsWith("#@"));
            }
            if (category != null)
            {
                schema = schema.Where(s => category.Contains(s.CLASS_ID.Value));
            }
            if (code != null)
            {
                schema = schema.Where(s => s.GOODS_CODE.Contains(code));
            }
            if (name != null)
            {
                schema = schema.Where(s => s.GOODS_NAME.Contains(name));
            }
            if (batch != null)
            {
                schema = schema.Where(s => s.GOODS_BATCH_NO.Contains(batch));
            }
            if (barcode != null)
            {
                schema = barcode == string.Empty ?
                    schema.Where(s => s.STOCK_BARCODE == barcode) :
                    schema = schema.Where(s => s.STOCK_BARCODE.Contains(barcode));
            }
            if (cell != null)
            {
                schema = schema.Where(s => s.CELL_NAME == cell);
            }
            if (qc != null)
            {
                schema = schema.Where(s => s.GOODS_QC_STATE == qc);
            }
            if (inboundFrom.HasValue)
            {
                schema = schema.Where(s => s.INBOUND_TIME >= inboundFrom.Value);
            }
            if (inboundTo.HasValue)
            {
                schema = schema.Where(s => s.INBOUND_TIME <= inboundTo.Value.AddDays(1));
            }
            if (inventoryFrom.HasValue)
            {
                schema = schema.Where(s => s.INVENTORY_TIME >= inventoryFrom.Value);
            }
            if (inventoryTo.HasValue)
            {
                schema = schema.Where(s => s.INVENTORY_TIME <= inventoryTo.Value.AddDays(1));
            }
            if (overdue != null)
            {
                schema = overdue.Value ?
                    schema.Where(s => s.OVERDUE_TIME != null && s.OVERDUE_TIME != 0) :
                    schema.Where(s => s.OVERDUE_TIME == null || s.OVERDUE_TIME == 0);
            }
            if (enabled != null)
            {
                schema = enabled.Value ?
                    schema.Where(s => s.STORAGE_LIST_FLAG == true || (s.STORAGE_LIST_FLAG == null && (s.GOODS_QC_STATE == null || s.GOODS_QC_STATE == "ok") && (s.OVERDUE_TIME == null || s.OVERDUE_TIME == 0))) :
                    schema.Where(s => s.STORAGE_LIST_FLAG == false || (s.STORAGE_LIST_FLAG == null && (s.GOODS_QC_STATE == "na" || s.GOODS_QC_STATE == "ng" || s.OVERDUE_TIME != 0)));
            }
            if (excludeTask == true)
            {
                var query = database.From<T_MANAGE_MAIN>().Query(m => m.STOCK_BARCODE);
                schema = schema.Where(s => !query.Contains(s.STOCK_BARCODE));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetPalletCount 方法
         */
        public async Task<int> GetPalletCount(int[] area, bool? pallet, bool? palletized, string barcode, string cell, bool? excludeTask)
        {
            return (int)(await GetPalletSchema(area, pallet, palletized, barcode, cell, excludeTask)
                .Single(s => new { Count = Function.Count(s.STORAGE_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetPalletList 方法
         */
        public async Task<IEnumerable<Stock>> GetPalletList(int[] area, bool? pallet, bool? palletized, string barcode, string cell, bool? excludeTask, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Stock>>(await GetPalletSchema(area, pallet, palletized, barcode, cell, excludeTask)
                .Sort<V_STORAGE_MAIN, Stock>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetPallet 相关方法的条件
         */
        private ISchema<V_STORAGE_MAIN> GetPalletSchema(int[] area, bool? pallet, bool? palletized, string barcode, string cell, bool? excludeTask)
        {
            var schema = database.From<V_STORAGE_MAIN>();
            if (area != null)
            {
                schema = schema.Where(s => area.Contains(s.AREA_ID));
            }
            if (pallet.HasValue)
            {
                schema = pallet.Value ?
                    schema.Where(s => s.STORAGE_LIST_COUNT == 0 && !s.STOCK_BARCODE.StartsWith("#@")) :
                    schema.Where(s => s.STORAGE_LIST_COUNT > 0);
            }
            if (palletized.HasValue)
            {
                schema = palletized.Value ?
                    schema.Where(s => !s.STOCK_BARCODE.StartsWith("#@")) :
                    schema.Where(s => s.STOCK_BARCODE.StartsWith("#@"));
            }
            if (barcode != null)
            {
                schema = barcode == string.Empty ?
                    schema.Where(s => s.STOCK_BARCODE == barcode) :
                    schema = schema.Where(s => s.STOCK_BARCODE.Contains(barcode));
            }
            if (cell != null)
            {
                schema = schema.Where(s => s.CELL_NAME == cell);
            }
            if (excludeTask == true)
            {
                var query = database.From<T_MANAGE_MAIN>().Query(m => m.STOCK_BARCODE);
                schema = schema.Where(s => !query.Contains(s.STOCK_BARCODE));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetLockedCount 方法
         */
        public async Task<int> GetLockedCount(int[] area, int[] category, string areatype, string qrcode, string plancode, int? planid, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled)
        {
            return (int)(await GetLockedSchema(area, category, areatype, qrcode, plancode, planid, code, name, batch, barcode, cell, qc, inboundFrom, inboundTo, inventoryFrom, inventoryTo, overdue, enabled)
                .Single(l => new { Count = Function.Count(l.LOCK_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetLockedList 方法
         */
        public async Task<IEnumerable<Stock>> GetLockedList(int[] area, int[] category, string areatype, string qrcode, string plancode, int? planid, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Stock>>(await GetLockedSchema(area, category, areatype, qrcode, plancode, planid, code, name, batch, barcode, cell, qc, inboundFrom, inboundTo, inventoryFrom, inventoryTo, overdue, enabled)
                .Sort<V_STORAGE_LOCK, Stock>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetLocked 相关方法的条件
         */
        private ISchema<V_STORAGE_LOCK> GetLockedSchema(int[] area, int[] category, string areatype, string qrcode, string plancode, int? planid, string code, string name, string batch, string barcode, string cell, string qc, DateTime? inboundFrom, DateTime? inboundTo, DateTime? inventoryFrom, DateTime? inventoryTo, bool? overdue, bool? enabled)
        {
            var schema = database.From<V_STORAGE_LOCK>();
            if (area != null)
            {
                schema = schema.Where(s => area.Contains(s.AREA_ID));
            }
            if (category != null)
            {
                schema = schema.Where(s => category.Contains(s.CLASS_ID.Value));
            }
            if (areatype != null)
            {
                schema = schema.Where(s => s.AREA_TYPE.Contains(areatype));
            }
            if (planid != null)
            {
                schema = schema.Where(s => s.PLAN_ID == planid);
            }
            if (plancode != null)
            {
                schema = plancode == string.Empty ?
                    schema.Where(s => s.PLAN_CODE.Contains("")) :
                    //schema.Where(s => s.PLAN_CODE == plancode) :
                    schema = schema.Where(s => s.PLAN_CODE.Contains(plancode) || s.PLAN_CODE_40.Contains(plancode) || s.PLAN_CODE_99.Contains(plancode));
            }
            if (code != null)
            {
                schema = schema.Where(s => s.GOODS_CODE.Contains(code));
            }
            if (name != null)
            {
                schema = schema.Where(s => s.GOODS_NAME.Contains(name));
            }
            if (batch != null)
            {
                schema = schema.Where(s => s.GOODS_BATCH_NO.Contains(batch));
            }
            if (barcode != null)
            {
                schema = barcode == string.Empty ?
                    schema.Where(s => s.STOCK_BARCODE == barcode) :
                    schema = schema.Where(s => s.STOCK_BARCODE.Contains(barcode));
            }
            if (cell != null)
            {
                schema = schema.Where(s => s.CELL_NAME == cell);
            }
            if (qc != null)
            {
                schema = schema.Where(s => s.GOODS_QC_STATE == qc);
            }
            if (inboundFrom.HasValue)
            {
                schema = schema.Where(s => s.INBOUND_TIME >= inboundFrom.Value);
            }
            if (inboundTo.HasValue)
            {
                schema = schema.Where(s => s.INBOUND_TIME <= inboundTo.Value.AddDays(1));
            }
            if (inventoryFrom.HasValue)
            {
                schema = schema.Where(s => s.INVENTORY_TIME >= inventoryFrom.Value);
            }
            if (inventoryTo.HasValue)
            {
                schema = schema.Where(s => s.INVENTORY_TIME <= inventoryTo.Value.AddDays(1));
            }
            if (overdue != null)
            {
                schema = overdue.Value ?
                    schema.Where(s => s.OVERDUE_TIME != null && s.OVERDUE_TIME != 0) :
                    schema.Where(s => s.OVERDUE_TIME == null || s.OVERDUE_TIME == 0);
            }
            if (enabled != null)
            {
                schema = enabled.Value ?
                    schema.Where(s => s.STORAGE_LIST_FLAG == true || (s.STORAGE_LIST_FLAG == null && (s.GOODS_QC_STATE == null || s.GOODS_QC_STATE == "ok") && (s.OVERDUE_TIME == null || s.OVERDUE_TIME == 0))) :
                    schema.Where(s => s.STORAGE_LIST_FLAG == false || (s.STORAGE_LIST_FLAG == null && (s.GOODS_QC_STATE == "na" || s.GOODS_QC_STATE == "ng" || s.OVERDUE_TIME != 0)));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetSafetyCount 方法
         */
        public async Task<int> GetSafetyCount(string type, int[] warehouse, int[] category, string code, string name)
        {
            return (int)(await GetSafetySchema(type, warehouse, category, code, name)
                .Single(s => new { Count = Function.Count(s.SAFETY_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetSafetyList 方法
         */
        public async Task<IEnumerable<Safety>> GetSafetyList(string type, int[] warehouse, int[] category, string code, string name, string sort, int? offset, int? limit)
        {
            var schema = GetSafetySchema(type, warehouse, category, code, name)
                .Page(offset, limit);
            schema = sort == null ?
                schema.OrderBy(s => s.OVERFLOW_PERCENT, Infrastructure.Database.Order.Descending) :
                schema.Sort<V_STORAGE_SAFETY, Safety>(sort, mapper);
            return mapper.Map<IEnumerable<Safety>>(await schema.Select());
        }

        /*
         * 组织 GetSafety 相关方法的条件
         */
        private ISchema<V_STORAGE_SAFETY> GetSafetySchema(string type, int[] warehouse, int[] category, string code, string name)
        {
            var schema = database.From<V_STORAGE_SAFETY>()
                .Where(s => s.GOODS_FLAG == true || s.STORAGE_QUANTITY > 0);
            switch (type)
            {
                case null:
                    break;
                case "safety":
                    schema = schema.Where(s => (s.LOWER_LIMIT == null || s.STORAGE_QUANTITY >= s.LOWER_LIMIT) && (s.UPPER_LIMIT == null || s.STORAGE_QUANTITY <= s.UPPER_LIMIT));
                    break;
                case "warning":
                    schema = schema.Where(s => s.STORAGE_QUANTITY < s.LOWER_LIMIT || s.STORAGE_QUANTITY > s.UPPER_LIMIT);
                    break;
                case "shortage":
                    schema = schema.Where(s => s.STORAGE_QUANTITY < s.LOWER_LIMIT);
                    break;
                case "excess":
                    schema = schema.Where(s => s.STORAGE_QUANTITY > s.UPPER_LIMIT);
                    break;
                default:
                    schema = schema.Where(s => s.STORAGE_QUANTITY < 0);
                    break;
            }
            if (warehouse != null)
            {
                var query = database.From<T_GOODS_SAFETY_LIST>().Where(s => warehouse.Contains(s.WAREHOUSE_ID)).Query(s => s.SAFETY_ID);
                schema = schema.Where(s => query.Contains(s.SAFETY_ID));
            }
            if (category != null)
            {
                schema = schema.Where(s => category.Contains(s.CLASS_ID));
            }
            if (code != null)
            {
                schema = schema.Where(s => s.GOODS_CODE.Contains(code));
            }
            if (name != null)
            {
                schema = schema.Where(s => s.GOODS_NAME.Contains(name));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetRecordCount 方法
         */
        public async Task<int> GetRecordCount(int? record, int[] area, int[] category, string code, string name, string batch, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo)
        {
            return (int)(await GetRecordSchema(record, area, category, code, name, batch, transaction, barcode, cell, original, destination, beginFrom, beginTo, endFrom, endTo)
                .Single(r => new { Count = Function.Count(r.RECORD_LIST_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetRecordList 方法
         */
        public async Task<IEnumerable<Record>> GetRecordList(int? record, int[] area, int[] category, string code, string name, string batch, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo, string sort, int? offset, int? limit)
        {
            var schema = GetRecordSchema(record, area, category, code, name, batch, transaction, barcode, cell, original, destination, beginFrom, beginTo, endFrom, endTo)
                .Page(offset, limit);
            schema = sort == null ?
                schema.OrderBy(r => r.BEGIN_TIME, Infrastructure.Database.Order.Descending).OrderBy(r => r.RECORD_ID, Infrastructure.Database.Order.Descending) :
                schema.Sort<V_RECORD_LIST, Record>(sort, mapper);
            var properties = new Dictionary<string, string>();
            return mapper.Map<IEnumerable<Record>>(await schema.Select())
                .Select(r =>
                {
                    if (r.Remark != null)
                    {
                        r.Remark = JsonHelper.Deserialize<IDictionary<string, object>>(r.Remark);
                        string column = r.Remark["property"];
                        if (!string.IsNullOrEmpty(column))
                        {
                            if (!properties.ContainsKey(column))
                            {
                                var property = (mapper.ConfigurationProvider as global::AutoMapper.Internal.IGlobalConfiguration)?
                                    .FindTypeMapFor<V_STORAGE_LIST, Stock>()?.PropertyMaps
                                    .FirstOrDefault(p => p.SourceMember.Name == column)?
                                    .DestinationName ?? string.Empty;
                                if (property.Length > 0)
                                {
                                    property = $"{property[..1].ToLower()}{property[1..]}";
                                }
                                properties.Add(column, property);
                            }
                            r.Remark["property"] = properties[column];
                        }
                    }
                    return r;
                });
        }

        /*
         * 组织 GetRecord 相关方法的条件
         */
        private ISchema<V_RECORD_LIST> GetRecordSchema(int? record, int[] area, int[] category, string code, string name, string batch, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo)
        {
            var schema = database.From<V_RECORD_LIST>();
            if (record != null)
            {
                schema = schema.Where(r => r.RECORD_ID == record.Value);
            }
            if (area != null)
            {
                schema = schema.Where(r => (area.Contains(r.START_AREA_ID) || area.Contains(r.END_AREA_ID)));
            }
            if (category != null)
            {
                schema = schema.Where(r => category.Contains(r.CLASS_ID.Value));
            }
            if (code != null)
            {
                schema = schema.Where(r => r.GOODS_CODE.Contains(code));
            }
            if (name != null)
            {
                schema = schema.Where(r => r.GOODS_NAME.Contains(name));
            }
            if (batch != null)
            {
                schema = schema.Where(r => r.GOODS_BATCH_NO.Contains(batch));
            }
            if (transaction != null)
            {
                schema = schema.Where(r => transaction.Contains(r.RECORD_TYPE));
            }
            if (barcode != null)
            {
                schema = schema.Where(r => r.STOCK_BARCODE.Contains(barcode));
            }
            if (cell != null)
            {
                schema = schema.Where(r => r.START_CELL_NAME == cell || r.END_CELL_NAME == cell);
            }
            if (original != null)
            {
                schema = schema.Where(r => r.START_CELL_NAME == original);
            }
            if (destination != null)
            {
                schema = schema.Where(r => r.END_CELL_NAME == destination);
            }
            if (beginFrom.HasValue)
            {
                schema = schema.Where(r => r.BEGIN_TIME >= beginFrom.Value);
            }
            if (beginTo.HasValue)
            {
                schema = schema.Where(r => r.BEGIN_TIME <= beginTo.Value.AddDays(1));
            }
            if (endFrom.HasValue)
            {
                schema = schema.Where(r => r.END_TIME >= endFrom.Value);
            }
            if (endTo.HasValue)
            {
                schema = schema.Where(r => r.END_TIME <= endTo.Value.AddDays(1));
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetPalletRecordCount 方法
         */
        public async Task<int> GetPalletRecordCount(int[] area, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo)
        {
            return (int)(await GetPalletRecordSchema(area, transaction, barcode, cell, original, destination, beginFrom, beginTo, endFrom, endTo)
                .Single(r => new { Count = Function.Count(r.RECORD_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetPalletRecordList 方法
         */
        public async Task<IEnumerable<Record>> GetPalletRecordList(int[] area, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo, string sort, int? offset, int? limit)
        {
            var schema = GetPalletRecordSchema(area, transaction, barcode, cell, original, destination, beginFrom, beginTo, endFrom, endTo)
                .Page(offset, limit);
            schema = sort == null ?
                schema.OrderBy(r => r.BEGIN_TIME, Infrastructure.Database.Order.Descending).OrderBy(r => r.RECORD_ID, Infrastructure.Database.Order.Descending) :
                schema.Sort<V_RECORD_MAIN, Record>(sort, mapper);
            var properties = new Dictionary<string, string>();
            return mapper.Map<IEnumerable<Record>>(await schema.Select());
        }

        /*
         * 组织 GetRecord 相关方法的条件
         */
        private ISchema<V_RECORD_MAIN> GetPalletRecordSchema(int[] area, string[] transaction, string barcode, string cell, string original, string destination, DateTime? beginFrom, DateTime? beginTo, DateTime? endFrom, DateTime? endTo)
        {
            var schema = database.From<V_RECORD_MAIN>();
            if (area != null)
            {
                schema = schema.Where(r => (area.Contains(r.START_AREA_ID) || area.Contains(r.END_AREA_ID)));
            }
            if (transaction != null)
            {
                schema = schema.Where(r => transaction.Contains(r.RECORD_TYPE));
            }
            if (barcode != null)
            {
                schema = schema.Where(r => r.STOCK_BARCODE.Contains(barcode));
            }
            if (cell != null)
            {
                schema = schema.Where(r => r.START_CELL_NAME == cell || r.END_CELL_NAME == cell);
            }
            if (original != null)
            {
                schema = schema.Where(r => r.START_CELL_NAME == original);
            }
            if (destination != null)
            {
                schema = schema.Where(r => r.END_CELL_NAME == destination);
            }
            if (beginFrom.HasValue)
            {
                schema = schema.Where(r => r.BEGIN_TIME >= beginFrom.Value);
            }
            if (beginTo.HasValue)
            {
                schema = schema.Where(r => r.BEGIN_TIME <= beginTo.Value.AddDays(1));
            }
            if (endFrom.HasValue)
            {
                schema = schema.Where(r => r.END_TIME >= endFrom.Value);
            }
            if (endTo.HasValue)
            {
                schema = schema.Where(r => r.END_TIME <= endTo.Value.AddDays(1));
            }
            return schema;
        }


        /*
        * 实现 Kean.Application.Query.Interfaces.IStockService.GetDistributeList 方法
        */
        public async Task<IEnumerable<Stock>> GetDistributeList(Application.Command.ViewModels.Stock stock)
        {
            IEnumerable<Stock> stocklist = new List<Stock>();
            List<Stock> newstocklist = new List<Stock>();

            foreach (var orderitem in stock.Orders)
            {
                var planlist = await database.From<T_PLAN_LIST>()
                .Where(s => s.PLAN_LIST_ID == orderitem)
                .Single();

                decimal dPlanUnfinish = planlist.PLANNED_QUANTITY - planlist.ORDERED_QUANTITY - planlist.FINISHED_QUANTITY;

                if (dPlanUnfinish > 0)
                {
                    foreach (var item in stock.Lines)
                    {
                        //库存行，非订单行
                        if (item.MainId > 0)
                        {
                            var storagelist = await database.From<V_STORAGE_LIST>()
                                .Where(s => s.STORAGE_LIST_ID == item.Id)
                                .Single();
                            if (storagelist != null)
                            {
                                if (planlist.GOODS_ID == storagelist.GOODS_ID && planlist.GOODS_BATCH_NO == storagelist.GOODS_BATCH_NO)
                                {
                                    decimal qty = (decimal)(dPlanUnfinish > storagelist.STORAGE_LIST_UNLOCK_QUANTITY ? storagelist.STORAGE_LIST_UNLOCK_QUANTITY : dPlanUnfinish);
                                    storagelist.MANAGE_LIST_QUANTITY = qty;
                                    dPlanUnfinish -= qty;

                                    Application.Query.ViewModels.Stock singlestock = mapper.Map<Stock>(storagelist);
                                    singlestock.Order = planlist.PLAN_LIST_ID;
                                    newstocklist.Add(singlestock);

                                    if (dPlanUnfinish <= 0)
                                    {
                                        break;//跳出库存的循环分配，进入下一条计划明细
                                    }
                                }
                            }
                        }
                    }
                }
            }

            stocklist = newstocklist;
            return stocklist;
        }


        /*
         * 实现 Kean.Application.Query.Interfaces.IStockService.GetStockCommandBatch 方法
         */
        public async Task<IEnumerable<Application.Command.ViewModels.Stock>> GetStockCommandBatch(Application.Command.ViewModels.Stock stocks, int session)
        {
            List<Application.Command.ViewModels.Stock> liststock = new List<Application.Command.ViewModels.Stock>();

            foreach (var stockline in stocks.Lines)
            {
                if (stockline.MainId != 0)
                {
                    var storagemain = await database.From<T_STORAGE_MAIN>()
                             .Where(s => s.STORAGE_ID == stockline.MainId)
                             .Single();
                    if (stocks.Tag == "outbound")
                    {
                        stockline.Quantity = stockline.Quantity;
                    }
                    else if (stocks.Tag == "outboundplan")
                    {
                        stockline.Quantity = stockline.ManagelistQuantity;
                    }
                    else
                    {
                        stockline.Quantity = stockline.ManagelistQuantity;
                    }

                    bool insert = false;

                    foreach (var exstock in liststock)
                    {
                        if (exstock.Barcode == storagemain.STOCK_BARCODE)
                        {
                            List<Lines> lines = exstock.Lines.ToList();
                            lines.Add(stockline);
                            IEnumerable<Lines> lines2 = lines;
                            exstock.Lines = lines2;
                            insert = true;
                            break;
                        }
                    }
                    if (!insert)
                    {
                        var newstock = new Application.Command.ViewModels.Stock();
                        newstock.Barcode = storagemain.STOCK_BARCODE;
                        newstock.Tag = stocks.Tag;
                        newstock.Operator = session;
                        newstock.Orders = stocks.Orders;
                        List<Lines> lines = new List<Lines>();
                        lines.Add(stockline);
                        IEnumerable<Lines> lines2 = lines;
                        newstock.Lines = lines2;

                        liststock.Add(newstock);
                    }
                }
            }

            return liststock;
        }

        /*
        * 实现 Kean.Application.Query.Interfaces.IStockService.GetStockList 方法
        */
        public async Task<IEnumerable<Stock>> GetStockListByPlanCode(IEnumerable<Application.Query.ViewModels.Order> lstPlanList)
        {
            List<V_STORAGE_LIST> lstStorageList = new List<V_STORAGE_LIST>();
            foreach (var order in lstPlanList)
            {
                lstStorageList.AddRange(await database.From<V_STORAGE_LIST>().Where(c => c.GOODS_ID == order.Material && c.RUN_STATUS == "Enabled" && c.CELL_STATUS == "Full").Select());
            }

            return mapper.Map<IEnumerable<Stock>>(lstStorageList.DistinctBy(l => l.STORAGE_LIST_ID));
        }

        /*
        * 实现 Kean.Application.Query.Interfaces.IStockService.GetStockList 方法
        */
        public async Task<IEnumerable<Stock>> GetStockListByPlanList(IEnumerable<Application.Query.ViewModels.Order> lstPlanList, bool half, bool full, string crane, string dock, string truck, int allowdays)
        {
            //验证同一单据
            var schamaPlan = database.From<T_PLAN_MAIN>();
            if (lstPlanList.Count() > 1)
            {
                int plan40quantity = lstPlanList.DistinctBy(p => p.Plancode40).ToList().Count;
                if (plan40quantity != 1)
                {
                    List<Stock> lstResult = new List<Stock>();
                    lstResult.Add(new Stock() { Barcode = "0", Remark = "请选择相同的40单进行出库" });
                    return lstResult;
                }
                int plan99quantity = lstPlanList.DistinctBy(p => p.Plancode99).ToList().Count;
                if (plan99quantity != 1)
                {
                    List<Stock> lstResult = new List<Stock>();
                    lstResult.Add(new Stock() { Barcode = "0", Remark = "请选择相同的99单进行出库" });
                    return lstResult;
                }
                if (string.IsNullOrEmpty(lstPlanList.First().Platform))
                {
                    List<Stock> lstResult = new List<Stock>();
                    lstResult.Add(new Stock() { Barcode = "0", Remark = "所选订单未配置出库月台" });
                    return lstResult;
                }
                int platform = lstPlanList.DistinctBy(p => p.Platform).ToList().Count;
                if (platform != 1)
                {
                    List<Stock> lstResult = new List<Stock>();
                    lstResult.Add(new Stock() { Barcode = "0", Remark = "所选订单已配置了不同的出库月台" });
                    return lstResult;
                }
            }
            else
            {
                if (string.IsNullOrEmpty(lstPlanList.First().Platform))
                {
                    List<Stock> lstResult = new List<Stock>();
                    lstResult.Add(new Stock() { Barcode = "0", Remark = "所选订单未配置出库月台" });
                    return lstResult;
                }
            }

            lstPlanList = lstPlanList.GroupBy(p => p.Material).Select(p => new ViewModels.Order()
            {
                Material = p.Key,
                Quantity = p.Sum(k => k.Quantity),
                Executing = p.Sum(k => k.Executing),
                Finished = p.Sum(k => k.Finished)
            }).ToList();

            List<V_STORAGE_LIST> lstStorageList = new List<V_STORAGE_LIST>();
            var schemaLK = database.From<V_STORAGE_LIST>();

            foreach (var order in lstPlanList)
            {
                //根据物料获取满托数量
                Task<T_GOODS_MAIN> mGoodsMain = database.From<T_GOODS_MAIN>().Where(g => g.GOODS_ID == order.Material).Single();
                decimal FullPalletQuantity = 0;
                if (mGoodsMain != null)
                {
                    FullPalletQuantity = mGoodsMain.Result.PALLET_QUANTITY;
                }
                //根据不同类型计算所需托盘数
                int palletQuantity = 0;
                //计算立体库整托出库数量
                if (full)
                {
                    palletQuantity = (int)((order.Quantity - order.Executing - order.Finished) / FullPalletQuantity);
                    if (palletQuantity != 0)
                    {
                        schemaLK = schemaLK.Where(s => s.AREA_TYPE == "LiKu" && s.RUN_STATUS == "Enabled" && s.CELL_STATUS == "Full" && s.GOODS_ID == order.Material && s.FULL_FLAG == true);
                        if (!string.IsNullOrEmpty(crane))
                        {
                            schemaLK.Where(c => c.CELL_GROUP == crane);
                        }
                        DateTime daysAccept = (DateTime.Now);
                        if (allowdays > 0)
                        {
                            daysAccept = daysAccept.AddDays(-allowdays);

                            schemaLK.Where(c => c.GOODS_MFG > daysAccept);
                        }
                        else
                        {
                            daysAccept = daysAccept.AddDays(-59);

                            schemaLK.Where(c => c.GOODS_MFG > daysAccept);
                        }
                        var tmp = await schemaLK.OrderBy(k => k.GOODS_MFG, Infrastructure.Database.Order.Ascending).Take(palletQuantity).Select();

                        lstStorageList.AddRange(tmp.Select(p => { p.STORAGE_LIST_REMARK = "LK"; return p; }));
                    }
                }
                //计算零头库需要补零的数量
                if (half)
                {
                    int lingQuantity = (int)((order.Quantity - order.Executing - order.Finished) % FullPalletQuantity);
                    //1.先查当前零头库库存(未锁)
                    schemaLK = schemaLK.Where(s => s.AREA_ID == 2 && s.CELL_TYPE == "Station" && s.GOODS_ID == order.Material && s.STORAGE_LIST_UNLOCK_QUANTITY > 0);
                    if (!string.IsNullOrEmpty(crane))
                    {
                        schemaLK.Where(c => c.CELL_GROUP == crane);
                    }
                    DateTime daysAccept = (DateTime.Now);
                    if (allowdays > 0)
                    {
                        daysAccept = daysAccept.AddDays(-allowdays);

                        schemaLK.Where(c => c.GOODS_MFG > daysAccept);
                    }
                    else
                    {
                        daysAccept = daysAccept.AddDays(-59);

                        schemaLK.Where(c => c.GOODS_MFG > daysAccept);
                    }
                    IEnumerable<V_STORAGE_LIST> lst = await schemaLK.OrderBy(k => k.GOODS_MFG, Infrastructure.Database.Order.Ascending).Select();
                    decimal pkQuantity = (decimal)lst.Sum(t => t.STORAGE_LIST_UNLOCK_QUANTITY);
                    if (pkQuantity > lingQuantity)
                    {
                        int toLock = lingQuantity;
                        int lockPalletQuantity = 0;
                        //库存够 锁库存,绑定库存,等出库确认
                        foreach (var storage in lst)
                        {
                            if (toLock > 0)
                            {
                                lockPalletQuantity++;
                                if (toLock - storage.STORAGE_LIST_UNLOCK_QUANTITY > 0)
                                {
                                    toLock -= (int)storage.STORAGE_LIST_UNLOCK_QUANTITY;
                                    storage.MANAGE_LIST_QUANTITY = storage.STORAGE_LIST_UNLOCK_QUANTITY;
                                }
                                else
                                {
                                    storage.MANAGE_LIST_QUANTITY = toLock;
                                    toLock = 0;
                                    break;
                                }
                            }
                            else
                            {
                                break;
                            }
                        }
                        lstStorageList.AddRange(lst.Take(lockPalletQuantity).Select(p => { p.STORAGE_LIST_REMARK = "LOCK"; return p; }));
                    }
                    else
                    {
                        int toTransfer = (int)(lingQuantity - pkQuantity);
                        int lingPalletQuantity = 0;
                        //库存不够,计算所需立库库存数量
                        schemaLK = schemaLK.Where(s => s.AREA_TYPE == "LiKu" && s.RUN_STATUS == "Enabled" && s.CELL_STATUS == "Full" && s.GOODS_ID == order.Material && s.FULL_FLAG == false).OrderBy(k => k.GOODS_MFG, Infrastructure.Database.Order.Ascending);
                        List<V_STORAGE_LIST> lstLingPallet = (await schemaLK.Select()).ToList();
                        //残盘数量
                        foreach (var storage in lstLingPallet)
                        {
                            if (toTransfer - storage.STORAGE_LIST_QUANTITY >= 0)
                            {
                                lingPalletQuantity++;
                                toTransfer -= (int)storage.STORAGE_LIST_QUANTITY;
                            }
                            else
                            {
                                break;
                            }
                        }
                        lstStorageList.AddRange(lstLingPallet.Take(lingPalletQuantity).Select(p => { p.STORAGE_LIST_REMARK = "PK"; return p; }));

                        int fullPallet = 0;
                        if (toTransfer > 0)
                        {
                            fullPallet = (int)Math.Ceiling(toTransfer / FullPalletQuantity);
                        }


                        if (fullPallet != 0)
                        {
                            schemaLK = schemaLK.Where(s => s.AREA_TYPE == "LiKu" && s.RUN_STATUS == "Enabled" && s.CELL_STATUS == "Full" && s.GOODS_ID == order.Material);
                            var tmp = await schemaLK.OrderBy(k => k.GOODS_MFG, Infrastructure.Database.Order.Ascending).Take(fullPallet).Select();
                            lstStorageList.AddRange(tmp.Select(p => { p.STORAGE_LIST_REMARK = "PK"; return p; }));
                        }
                    }
                }
            }

            return mapper.Map<IEnumerable<Stock>>(lstStorageList.DistinctBy(l => l.STORAGE_LIST_ID));
        }
    }
}
