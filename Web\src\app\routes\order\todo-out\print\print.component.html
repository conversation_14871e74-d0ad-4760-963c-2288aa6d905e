<h2 mat-dialog-title>
    <button mat-button color="primary" [loading]="loading" (click)="poin()">{{ 'shared.operation.print' | translate |
        uppercase }}</button>
</h2>
<div class="print-doc">
    <div style="transform: scale(0.8);">
        <div id="printable-content">
            <ng-container *ngFor="let item of list">
                <div id="title">
                    <div class="title">
                        <div>
                            <!-- <img src="\assets\images\logo.png" alt="二维码" style=" -->
                            <img [attr.src]="url" alt="二维码" style="
                width: 100px;
                height: 100px;
                /* position: relative;
                z-index: 1; */
                margin-top: -35px;
                margin-left: 80px;
                float: left;
            "><span style="font-size: 20px; float: left">{{this.data.projectName}}</span>
                        </div>
                        <div>
                            <!-- <img src="\assets\images\logo.png" alt="二维码" style=" -->
                            <img [attr.src]="url" alt="二维码" style="
                width: 100px;
                height: 100px;
                /* position: relative;
                z-index: 1; */
                margin-top: -35px;
                margin-left: 80px;
                float: right;
                visibility: hidden;
            "><span style="font-size: 20px; float: right;visibility: hidden;">{{this.data.projectName}}</span>
                        </div>
                        <!-- <div>
                            <p>{{orderType}}</p>
                        </div> -->
                    </div>
                </div>
                <div id="table">
                    <div class="table-container">
                        <table mat-table [dataSource]="item.data" matSort (matSortChange)="refresh($event)">
                            <ng-container [matColumnDef]="item.id" *ngFor="let item of columns;">
                                <div>
                                    <th mat-header-cell *matHeaderCellDef style="text-align: center;">{{ item.header |
                                        translate
                                        }}</th>
                                    <td mat-cell *matCellDef="let row" style="text-align: center;">{{ row[item.id]| translate }}
                                    </td>
                                </div>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef=" columns | field:'id';"></tr>
                            <tr class="text" mat-row *matRowDef="let row; columns: columns | field:'id';"></tr>
                        </table>


                    </div>
                </div>
                <div id="page" style="
            text-align: center;
        " class="page-break-after">
                    <span>{{item.page}}</span>
                </div>
            </ng-container>
        </div>

    </div>
</div>