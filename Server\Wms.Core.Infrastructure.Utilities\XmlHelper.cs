﻿using System;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace Kean.Infrastructure.Utilities
{
    /// <summary>
    /// XML 序列化和反序列化辅助类
    /// </summary>
    public sealed class XmlHelper
    {
        /// <summary>
        /// Xml 序列化
        /// </summary>
        /// <param name="value">序列化元素</param>
        /// <returns>Xml 表达式</returns>
        public static string Serialize(object value, XmlSerializerNamespaces namespaces = null, XmlWriterSettings settings = null)
        {
            namespaces ??= new XmlSerializerNamespaces(new XmlQualifiedName[] { new XmlQualifiedName(string.Empty, string.Empty) });
            settings ??= new XmlWriterSettings { Encoding = Encoding.UTF8, OmitXmlDeclaration = true };
            var builder = new StringBuilder();
            using var writer = XmlWriter.Create(builder, settings);
            new XmlSerializer(value.GetType()).Serialize(writer, value, namespaces);
            return builder.ToString();
        }

        public static string Serializer<T>(T t)
        {
            StringBuilder output = new StringBuilder();
            XmlWriterSettings setting = new XmlWriterSettings();

            setting.OmitXmlDeclaration = true;
            setting.Indent = true;

            string str = string.Empty;

            try
            {
                using (XmlWriter writer = XmlWriter.Create(output, setting))
                {
                    XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
                    //Add an empty namespace and empty value
                    ns.Add("", "");

                    //创建序列化对象 
                    XmlSerializer xml = new XmlSerializer(typeof(T));
                    //序列化对象 
                    xml.Serialize(writer, t, ns);
                    str = output.ToString();
                }
            }
            catch (InvalidOperationException ex)
            {
                throw;
            }

            return str;
        }

        /// <summary>
        /// Xml 反序列化
        /// </summary>
        /// <typeparam name="T">序列化元素类型</typeparam>
        /// <param name="xmlString">Xml 表达式</param>
        /// <returns>序列化元素</returns>
        public static T Deserialize<T>(string xmlString)
        {
            using var reader = new StringReader(xmlString);
            return (T)new XmlSerializer(typeof(T)).Deserialize(reader);

        }
    }
}