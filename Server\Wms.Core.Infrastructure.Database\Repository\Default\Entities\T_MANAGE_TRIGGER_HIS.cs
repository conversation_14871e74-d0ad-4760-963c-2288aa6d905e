﻿using System;

namespace Kean.Infrastructure.Database.Repository.Default.Entities
{
    public class T_MANAGE_TRIGGER_HIS : IEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Identifier]
        public int TRIGGER_ID { get; set; }

        /// <summary>
        /// 触发时间
        /// </summary>
        public DateTime TRIGGER_TIME { get; set; }

        /// <summary>
        /// 超时时间（ms）
        /// </summary>
        public int? TRIGGER_TIMEOUT { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int TRIGGER_TYPE { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public int TRIGGER_WAREHOUSE { get; set; }

        /// <summary>
        /// 设备
        /// </summary>
        public string TRIGGER_DEVICE { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public string? TRIGGER_PARAM { get; set; }

        public string? TRIGGER_PARAM_01 { get; set; }
        public string? TRIGGER_PARAM_02 { get; set; }
        /// <summary>
        /// 执行结果
        /// </summary>
        public string EXEC_RESULT { get; set; }

        /// <summary>
        /// 执行计数
        /// </summary>
        public int EXEC_COUNT { get; set; }

        /// <summary>
        /// 执行备注
        /// </summary>
        public string? EXEC_REMARK { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CREATE_TIME { get; set; }

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UPDATE_TIME { get; set; }

    }
}
