﻿using Kean.Domain.Order.Events;
using Kean.Domain.Order.Models;
using Kean.Domain.Order.Repositories;
using Kean.Domain.Shared;
using Kean.Infrastructure.Utilities;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Order.EventHandlers
{
    /// <summary>
    /// 删除成功时，归档订单
    /// </summary>
    public sealed class DeleteSuccessEventHandler_Archive(
        IServiceProvider serviceProvider,   // 服务供应商
        IOrderRepository orderRepository    // 订单仓库
    ) : EventHandler<DeleteSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(DeleteSuccessEvent @event, CancellationToken cancellationToken)
        {
            await orderRepository.ArchiveOrder(@event.Order, @event.State ? 1 : 0);
            if (@event.State)
            {
                ZALLSAP_UPLOAD_GOODSMOV tmp = new ZALLSAP_UPLOAD_GOODSMOV();
                tmp.TARGETSYS = "1101";
                tmp.SOURCESYS = "2306";
                tmp.UPDATETIME = DateTime.Now.ToString("yyyyMMddHHmmss");

                List<ZALLSAP_UPLOAD_GOODSMOV_1> lst1 = new List<ZALLSAP_UPLOAD_GOODSMOV_1>();
                List<ZALLSAP_UPLOAD_GOODSMOV_2> lst2 = new List<ZALLSAP_UPLOAD_GOODSMOV_2>();
                List<ZALLSAP_UPLOAD_GOODSMOV_3> lst3 = new List<ZALLSAP_UPLOAD_GOODSMOV_3>();
                ZALLSAP_UPLOAD_GOODSMOV_1 arr1 = new ZALLSAP_UPLOAD_GOODSMOV_1()
                {
                    BSART = @event.Order.SapType,//收发货类型 "RC6",
                    BUDAT = DateTime.Now.ToString("yyyyMMdd"), //抬头发货日期 "20250908",
                    EBELN = @event.Order.Number, //单号 "110406149",
                    HTEXT = "",//抬头文本
                    VTXTK = "",//货主代码
                    LGPLA = "",//仓库代码
                    I_TIME = DateTime.Now.ToString("HHmmss"),//发送时间 "164500",
                    I_DATE = DateTime.Now.ToString("yyyyMMdd")//发送日期 "20250908"
                };
                lst1.Add(arr1);
                ZALLSAP_UPLOAD_GOODSMOV_2 arr2 = new ZALLSAP_UPLOAD_GOODSMOV_2()
                {
                    CHARG = "",//批号 
                    POSNR = "",//项目 来源SAP"000001",
                    SGTXT = "",//项目文本 来源SAP
                    MATNR = "17AW582409H",//物料号
                    MENGE = 1.0m,//数量
                    MEINS = "CS",//单位
                    WERKS = "ZTY1",//工厂
                    LGORT = "2088",//库存地
                    INSMK = "",//库存类型
                    HSDAT = "20250908",//生产日期
                    LICHA = "",//供应商批号
                    EBELN = "110406149"//单号
                };
                lst2.Add(arr2);

                tmp.IT_MATDOC_HEAD = lst1.ToArray();
                tmp.IT_MATDOC_DETAILS = lst2.ToArray();
                tmp.OT_MATDOC = lst3.ToArray();

                string task = XmlHelper.Serialize(tmp);
                await serviceProvider.GetRequiredService<IInterfaceService>().SyncOutput("sap", "UploadTask", null, $"test", task);
            }
        }
    }
}
