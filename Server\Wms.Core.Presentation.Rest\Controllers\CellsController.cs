﻿
using Kean.Application.Command.ViewModels;
using Kean.Application.Query.ViewModels;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Controllers
{
    /// <summary>
    /// 货位服务
    /// </summary>
    [ApiController, Route("api/cells")]
    public class CellsController(
        Application.Command.Interfaces.ITaskService taskCommandService,         // 任务命令服务
        Application.Query.Interfaces.IWarehouseService warehouseQueryService    // 库房信息查询服务
    ) : ControllerBase
    {
        /// <summary>
        /// 获取货位列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetList(
            [FromQuery] int[] area,
            [FromQuery] string type,
            [FromQuery] bool? @in,
            [FromQuery] bool? @out,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            if (area.Length == 0)
            {
                area = null;
            }
            var items = await warehouseQueryService.GetCellList(area, type, @in, @out, null, null, sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await warehouseQueryService.GetCellCount(area, type, @in, @out, null, null);
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 获取禁用货位列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("disablements")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetList(
            [FromQuery] int[] area,
            [FromQuery] string name,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            if (area.Length == 0)
            {
                area = null;
            }
            var items = await warehouseQueryService.GetCellList(area, nameof(Domain.Task.Models.Cell), null, null, name, nameof(Domain.Task.Enums.CellState.Disabled), sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await warehouseQueryService.GetCellCount(area, nameof(Domain.Task.Models.Cell), null, null, name, nameof(Domain.Task.Enums.CellState.Disabled));
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 获取禁用货位列表
        /// </summary>
        /// <response code="200">成功</response>
        [HttpGet("nodata")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetListNodata(
            [FromQuery] int[] area,
            [FromQuery] string name,
            [FromQuery] string sort,
            [FromQuery] int? offset,
            [FromQuery] int? limit)
        {
            if (area.Length == 0)
            {
                area = null;
            }
            var items = await warehouseQueryService.GetCellListNodata(area, nameof(Domain.Task.Models.Cell), null, null, name, nameof(Domain.Task.Enums.CellState.Disabled), sort, offset, limit);
            if (offset.HasValue || limit.HasValue)
            {
                var total = await warehouseQueryService.GetCellCount(area, nameof(Domain.Task.Models.Cell), null, null, name, nameof(Domain.Task.Enums.CellState.Disabled));
                return StatusCode(200, new { items, total });
            }
            else
            {
                return StatusCode(200, new { items, total = items.Count() });
            }
        }

        /// <summary>
        /// 批量处理禁用货位
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="405">非法操作</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost("disablements/batch")]
        [ProducesResponseType(200)]
        [ProducesResponseType(405)]
        [ProducesResponseType(422)]
        [Log("设置货位禁用状态", Data = ["warehouse", "remark", "batch"])]
        public async Task<IActionResult> Batch(
            [FromMember] int warehouse,
            [FromMember] string remark,
            [FromBody] Batch<string> batch)
        {
            (IEnumerable<bool?> States, Failure Failure) result;
            switch (batch.Method)
            {
                case BatchMethod.Create:
                    result = await taskCommandService.Cell(warehouse, batch.Data, false, remark);
                    break;
                case BatchMethod.Delete:
                    result = await taskCommandService.Cell(warehouse, batch.Data, true, remark);
                    break;
                default:
                    return StatusCode(405);
            }
            if (result.Failure == null)
            {
                return StatusCode(200, result.States);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }

        /// <summary>
        /// 批量处理禁用货位
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="405">非法操作</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost("nodata/controlout")]
        [ProducesResponseType(200)]
        [ProducesResponseType(405)]
        [ProducesResponseType(422)]
        [Log("先入品货位出库", Data = ["warehouse", "remark", "batch"])]
        public async Task<IActionResult> SendOutControl(
            [FromMember] int warehouse,
            [FromMember] string remark,
            [FromBody] Batch<string> batch)
        {
            (int id, Failure Failure) result;
            switch (batch.Method)
            {
                case BatchMethod.Create:
                    //result = await taskCommandService.Cell(warehouse, batch.Data, false, remark);
                    break;
                case BatchMethod.Delete:
                    foreach (var start in batch.Data.ToList())
                    {
                        result = await taskCommandService.SendControlTask(start, await warehouseQueryService.getOutPutDestinationCode(null, start, 0), "2", null);

                    }

                    break;
                default:
                    return StatusCode(405);
            }
            return StatusCode(200);
            //if (result. != null && result.Failure == null)
            //{
            //    return StatusCode(200, "");
            //}
            //else
            //{
            //    return StatusCode(422, result.Failure);
            //}

        }

        /// <summary>
        /// 批量更新货位热度
        /// </summary>
        /// <response code="200">成功</response>
        /// <response code="405">非法操作</response>
        /// <response code="422">请求内容错误</response>
        [HttpPost("degreeupdate")]
        [ProducesResponseType(200)]
        [ProducesResponseType(405)]
        [ProducesResponseType(422)]
        [Log("批量更新货位热度", Data = ["warehouse", "startCellCode", "endCellCode", "degree"])]
        public async Task<IActionResult> UpdateHotDegree(
            [FromMember] int warehouse,
            [FromMember] string startCellCode,
            [FromMember] string endCellCode,
            [FromMember] string degree)
        {
            var result = await taskCommandService.CellDegree(warehouse, startCellCode, endCellCode, degree);
            if (result.Failure == null)
            {
                return StatusCode(200);
            }
            else
            {
                return StatusCode(422, result.Failure);
            }
        }
    }
}
