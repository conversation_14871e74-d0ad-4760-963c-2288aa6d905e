﻿using Kean.Domain.Shared;
using Kean.Domain.Stock.Repositories;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Models;
using Kean.Domain.Task.Repositories;
using Kean.Domain.Task.Strategies;
using Kean.Infrastructure.Database.Repository;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 设备触发命令执行时，处理类型 3
    /// </summary>
    public sealed class TriggerExecutingEventHandler_Type_3(
        IOrderService orderService,
        IStockService stockService,
        ITaskService taskService,
        IServiceProvider serviceProvider,           // 服务供应商
        Task.Repositories.IWarehouseRepository warehouseRepository,
        ILogger<TriggerExecutingEventHandler_Type_1> logger,    // 日志
        ICommandBus commandBus,                                 // 命令总线
        INotification notification,                              // 总线通知
        IDefaultDb database
    ) : EventHandler<TriggerExecutingEvent>
    {
        private readonly InfeedContext<InfeedStrategy_Simple> _infeedContext = new(serviceProvider);
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(TriggerExecutingEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Type == 3)
            {
                switch (@event.Parameter.Value<string>("fallback"))
                {


                    // 上架
                    default:

                        T_MANAGE_MAIN mManageMain = await database.From<T_MANAGE_MAIN>().Where(p => p.STOCK_BARCODE == @event.Parameter.Value<string>("Barcode")).Single();
                        if (mManageMain == null)
                        {
                            //未找到对应的管理任务
                            IO_CONTROL mIoControl = await database.From<IO_CONTROL>().Where(p => p.STOCK_BARCODE == @event.Parameter.Value<string>("Barcode")).Single();
                            if (mIoControl != null)
                            {
                                mIoControl.CONTROL_STATUS = 40;
                                await database.From<IO_CONTROL>().Update(mIoControl);
                            }
                        }
                        else
                        {
                            IO_CONTROL mIoControl = await database.From<IO_CONTROL>().Where(p => p.STOCK_BARCODE == @event.Parameter.Value<string>("Barcode")).Single();
                            if (mIoControl != null)
                            {
                                string startDevice = mIoControl.START_DEVICE_CODE;
                                IO_CONTROL_ROUTE mRout = await database.From<IO_CONTROL_ROUTE>().Where(k => k.CONTROL_ROUTE_TYPE == 5 && k.START_DEVICE == @event.Device && k.CONTROL_ROUTE_STATUS == 1).OrderBy(k => k.END_DEVICE, Infrastructure.Database.Order.Descending).Single();
                                if (mRout == null)
                                {
                                    //无可用路径
                                    mIoControl.CONTROL_STATUS = 40;
                                    await database.From<IO_CONTROL>().Update(mIoControl);
                                }
                                else
                                {
                                    Station startStation = await warehouseRepository.GetStationByCode(@event.Device, 1);
                                    if (startStation != null)
                                    {
                                        List<string> lstLaneway = new List<string>();
                                        lstLaneway.Add(mRout.END_DEVICE);
                                        //指定巷道分配货位
                                        Cell endCell = await _infeedContext.AutoDestinationWithLaneway(@event.Parameter.Value<string>("Barcode"), startStation, lstLaneway, null);
                                        mIoControl.CONTROL_STATUS = 40;
                                        if (endCell != null)
                                        {
                                            mIoControl.END_DEVICE_CODE = endCell.Device;
                                            T_WH_CELL orgEndCell = await database.From<T_WH_CELL>().Where(p => p.CELL_ID == mManageMain.END_CELL_ID).Single();
                                            orgEndCell.RUN_STATUS = "Enabled";
                                            await database.From<T_WH_CELL>().Update(orgEndCell);

                                            mManageMain.END_CELL_ID = endCell.Id;
                                            T_WH_CELL newEndCell = await database.From<T_WH_CELL>().Where(p => p.CELL_ID == mManageMain.END_CELL_ID).Single();
                                            newEndCell.RUN_STATUS = "Selected";
                                            await database.From<T_WH_CELL>().Update(newEndCell);

                                            await database.From<IO_CONTROL>().Update(mIoControl);

                                            await database.From<T_MANAGE_MAIN>().Update(mManageMain);

                                            return;
                                        }
                                        else
                                        {
                                            return;
                                        }
                                    }

                                }
                            }
                        }

                        break;

                    // 回退
                    case "Reverse":

                        // TODO：实现回退逻辑。若回退失败可继续降级（ @event.Fallback("..."); ）

                        break;

                    // 其他降级操作
                    case "...":

                        // TODO

                        break;
                }
            }
        }
    }
}
