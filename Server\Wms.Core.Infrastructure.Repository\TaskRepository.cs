﻿using AutoMapper;
using Dapper;
using Kean.Domain;

using Kean.Domain.Shared;
using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Models;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.NoSql.Repository.Default;
using Kean.Infrastructure.Utilities;
using Newtonsoft.Json.Linq;
using Npgsql.Replication;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Dapper.SqlMapper;


namespace Kean.Infrastructure.Repository
{
    /// <summary>
    /// 任务仓库
    /// </summary>
    public class TaskRepository(
        IMapper mapper,         // 模型映射
        IUnitOfWork unitOfWork, // 工作单元
        IDefaultDb database,    // 默认数据库
        IDefaultRedis redis,     // 默认 Redis
        IOrderService orderService,
        IStockService stockService
    ) : Domain.Task.Repositories.ITaskRepository
    {
        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.AcquireLock 方法
         */
        public async Task<IDisposable> AcquireLock(string barcode)
        {
            return await redis.Lock(barcode, unitOfWork.Number.ToString(), 2000, 5000);
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.HasTask 方法
         */
        public async Task<bool> HasTask(int[] cell)
        {
            return (await database.From<T_MANAGE_MAIN>()
                .Where(m => cell.Contains(m.START_CELL_ID) || cell.Contains(m.END_CELL_ID))
                .Single(m => new { Count = Function.Count(m.MANAGE_ID) }))
                .Count > 0;
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.HasTask 方法
         */
        public async Task<bool> HasTask(int? original, int? destination, TaskType? type = null)
        {
            var schema = database.From<T_MANAGE_MAIN>();
            if (original.HasValue)
            {
                schema.Where(m => m.START_CELL_ID == original);
            }
            if (destination.HasValue)
            {
                schema.Where(m => m.END_CELL_ID == destination);
            }
            if (type.HasValue)
            {
                var manageType = type.ToString();
                schema.Where(m => m.MANAGE_TYPE == manageType);
            }
            return (await schema.Single(m => new { Count = Function.Count(m.MANAGE_ID) })).Count > 0;
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.HasTask 方法
         */
        public async Task<bool> HasTask(string barcode, string originalGroup = null, string destinationGroup = null)
        {
            var schema = database.From<T_MANAGE_MAIN>().Where(m => m.STOCK_BARCODE == barcode);
            if (originalGroup != null)
            {
                var query = database.From<T_WH_CELL>().Where(c => c.CELL_GROUP == originalGroup).Query(c => new { c.CELL_ID });
                schema.Where(m => query.Contains(m.START_CELL_ID));
            }
            if (destinationGroup != null)
            {
                var query = database.From<T_WH_CELL>().Where(c => c.CELL_GROUP == destinationGroup).Query(c => new { c.CELL_ID });
                schema.Where(m => query.Contains(m.END_CELL_ID));
            }
            return (await schema.Single(m => new { Count = Function.Count(m.MANAGE_ID) })).Count > 0;
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.IsExist 方法
         */
        public async Task<bool> IsExist(int id)
        {
            return (await database.From<T_MANAGE_MAIN>()
                .Where(m => m.MANAGE_ID == id)
                .Single(m => new { Count = Function.Count(m.MANAGE_ID) })).Count > 0;
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTasks 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Task>> GetTasks(int? warehouse)
        {
            var schema = database.From<T_MANAGE_MAIN>();
            if (warehouse.HasValue)
            {
                schema.Where(m => m.WAREHOUSE_ID == warehouse);
            }
            return mapper.Map<IEnumerable<Domain.Task.Models.Task>>(await schema.Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTasks 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Task>> GetTasks(int? original, int? destination, TaskType? type = null)
        {
            var schema = database.From<T_MANAGE_MAIN>();
            if (original.HasValue)
            {
                schema.Where(m => m.START_CELL_ID == original);
            }
            if (destination.HasValue)
            {
                schema.Where(m => m.END_CELL_ID == destination);
            }
            if (type.HasValue)
            {
                var manageType = type.ToString();
                schema.Where(m => m.MANAGE_TYPE == manageType);
            }
            return mapper.Map<IEnumerable<Domain.Task.Models.Task>>(await schema.Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTask 方法
         */
        public async Task<Domain.Task.Models.Task> GetTask(int id)
        {
            var task = await database.From<T_MANAGE_MAIN>()
                .Where(m => m.MANAGE_ID == id)
                .Single();
            return task == null ? null : mapper.Map<Domain.Task.Models.Task>(task);
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTask 方法
         */
        public async Task<Domain.Task.Models.Task> GetTask(Cell cell)
        {
            var task = await database.From<T_MANAGE_MAIN>()
                .Where(m => m.START_CELL_ID == cell.Id || m.END_CELL_ID == cell.Id)
                .Lock(Lock.Slock)
                .Single();
            return task == null ? null : mapper.Map<Domain.Task.Models.Task>(task);
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetFollowed 方法
         */
        public async Task<IEnumerable<Domain.Task.Models.Task>> GetFollows(int id)
        {
            return mapper.Map<IEnumerable<Domain.Task.Models.Task>>(await database.From<T_MANAGE_MAIN>()
                .Where(m => m.PREVIOUS_ID == id)
                .Select());
        }



        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.CreateTask 方法
         */
        public async Task<int> CreateTask(Domain.Task.Models.Task task)
        {
            var timestamp = DateTime.Now;
            var entity = mapper.Map<T_MANAGE_MAIN>(task);
            entity.MANAGE_STATUS = nameof(TaskState.Created);
            entity.UPDATE_TIME = entity.CREATE_TIME = DateTime.Now;
            if (task.Type.ToString() == "Outfeed")
            {
                var array = await stockService.GetStockLine(task.Barcode);
                foreach (var item in array)
                {
                    item["Quantity"] = task.ManageListQuantity;
                    item["ManageListQuantity"] = task.ManageListQuantity;
                    item["Order"] = 0;
                }

                entity.MANAGE_REMARK = array.ToString(Newtonsoft.Json.Formatting.None);
            }
            else if (task.Type.ToString() == "Infeed")
            {
                var tmpPlanList = await database.From<T_PLAN_LIST>().Where(c => c.PLAN_LIST_ID == task.PlanListId).Select();
                if (tmpPlanList.Count() == 0)
                {
                    return 0;
                }
                //前台创建任务无remark
                if (string.IsNullOrEmpty(task.Remark))
                {
                    var array = await orderService.GetOrderLine(task.PlanListId);
                    foreach (var item in array)
                    {
                        item["Order"] = item["Id"];
                        item["Id"] = 0;
                        item["Quantity"] = task.ManageListQuantity; ;
                        item["ManageListQuantity"] = task.ManageListQuantity;
                    }

                    entity.MANAGE_REMARK = array.ToString(Newtonsoft.Json.Formatting.None);
                }
                //后台申请任务带remark
                else
                {
                    var remark = JArray.Parse(task.Remark).First;
                    var array = await orderService.GetOrderLine(task.PlanListId);
                    foreach (var item in array)
                    {
                        item["Order"] = item["Id"];
                        item["Id"] = 0;
                        item["Quantity"] = task.ManageListQuantity; ;
                        item["ManageListQuantity"] = task.ManageListQuantity;
                        item["PlantCode"] = remark["PlantCode"];
                        item["WorkGroup"] = remark["WorkGroup"];
                        item["ProductionLine"] = remark["ProductionLine"];
                    }

                    entity.MANAGE_REMARK = array.ToString(Newtonsoft.Json.Formatting.None);
                }

            }

            int iManageId = Convert.ToInt32(await database.From<T_MANAGE_MAIN>().Add(entity));

            //if (iManageId > 0)
            //{
            //    //创建MANAGE_LIST
            //    var plan_list = await database.From<T_PLAN_LIST>().Where(s => s.PLAN_ID == entity.PLAN_ID).Select();
            //    foreach (var plan in plan_list)
            //    {
            //        T_MANAGE_LIST t_MANAGE_LIST = mapper.Map<T_MANAGE_LIST>(plan);
            //        t_MANAGE_LIST.MANAGE_ID = entity.MANAGE_ID;
            //        await database.From<T_MANAGE_LIST>().Add(t_MANAGE_LIST);
            //    }
            //}
            return iManageId;
            //if (task.Lines != null)
            //{
            //    foreach (var item in stock.Lines)
            //    {
            //        var storageList = mapper.Map<T_MANAGE_LIST>(item);
            //        if (storageList.STORAGE_LIST_ID == 0)
            //        {
            //            storageList.STORAGE_ID = id.Value;
            //            storageList.INBOUND_TIME = item.InboundTime == default ? (item.InboundTime = stock.Timestamp) : item.InboundTime;
            //            storageList.INVENTORY_TIME = stock.Timestamp;
            //            storageList.AVAILABLE_TIME ??= item.MinimumStockPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.MinimumStockPeriod.Value) : null;
            //            storageList.UNAVAILABLE_TIME ??= item.MaximumStockPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.MaximumStockPeriod.Value) : null;
            //            storageList.GOODS_EFF_TIME ??= item.EffectivePeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.EffectivePeriod.Value) : null;
            //            storageList.GOODS_EXP_TIME ??= item.ExpirationPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.ExpirationPeriod.Value) : null;
            //            storageList.STORAGE_LIST_REMARK = Convert.ToString(item.Remark);
            //            storageList.CREATE_TIME = timestamp;
            //            storageList.UPDATE_TIME = timestamp;
            //            item.Id = Convert.ToInt32(await database.From<T_MANAGE_LIST>().Add(storageList));
            //        }
            //        else
            //        {
            //            var existence = await database.From<T_MANAGE_LIST>()
            //                .Where(s => s.STORAGE_LIST_ID == storageList.STORAGE_LIST_ID && s.STORAGE_ID == id.Value)
            //                .Single()
            //                ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
            //            existence.STORAGE_LIST_QUANTITY += storageList.STORAGE_LIST_QUANTITY;
            //            existence.INVENTORY_TIME = stock.Timestamp;
            //            existence.UPDATE_TIME = timestamp;
            //            await database.From<T_MANAGE_LIST>().Update(existence);
            //            mapper.Map(existence, item, opts => opts.BeforeMap((source, _) =>
            //            {
            //                source.STORAGE_LIST_QUANTITY = item.Quantity;
            //                source.PLAN_LIST_ID = item.Order;
            //            }));
            //        }
            //    }
            //}
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.DeleteTask 方法
         */
        public async System.Threading.Tasks.Task DeleteTask(int id)
        {
            await database.From<T_MANAGE_MAIN>()
                .Where(m => m.MANAGE_ID == id)
                .Delete();
        }
        public async System.Threading.Tasks.Task UpdateOrderQuantity(string manageRemark)
        {
            try
            {
                decimal executingQuantity = JArray.Parse(manageRemark).AsList().ToList()[0]["ManageListQuantity"].Value<decimal>();
                int planListId = JArray.Parse(manageRemark).AsList().ToList()[0]["Order"].Value<int>();
                if (planListId == 0)
                {
                    List<JToken> lst = JArray.Parse(manageRemark).AsList();
                    foreach (var obj in lst)
                    {
                        IEnumerable<T_STORAGE_LOCK> tmp = await database.From<T_STORAGE_LOCK>().Where(p => p.STORAGE_LIST_ID == obj["Id"].Value<int>()).Select();
                        if (tmp.Count() > 0)
                        {
                            foreach (var storageLock in tmp)
                            {
                                T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == storageLock.PLAN_LIST_ID).Single();
                                if (planList != null)
                                {
                                    planList.ORDERED_QUANTITY -= storageLock.LOCK_QUANTITY;
                                    await database.From<T_PLAN_LIST>().Update(planList);
                                }
                            }
                        }

                    }
                }
                else
                {
                    T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == planListId).Single();
                    if (planList != null)
                    {
                        planList.ORDERED_QUANTITY -= executingQuantity;
                        planList.FINISHED_QUANTITY += executingQuantity;
                        await database.From<T_PLAN_LIST>().Update(planList);
                    }
                }
            }
            catch (Exception) { }
        }


        public async System.Threading.Tasks.Task UpdateOrderQuantity(string manageRemark, bool isComplete)
        {
            try
            {
                decimal executingQuantity = JArray.Parse(manageRemark).AsList().ToList()[0]["ManageListQuantity"].Value<decimal>();
                int planListId = JArray.Parse(manageRemark).AsList().ToList()[0]["Order"].Value<int>();
                if (planListId == 0)
                {
                    List<JToken> lst = JArray.Parse(manageRemark).AsList();
                    foreach (var obj in lst)
                    {
                        IEnumerable<T_STORAGE_LOCK> tmp = await database.From<T_STORAGE_LOCK>().Where(p => p.STORAGE_LIST_ID == obj["Id"].Value<int>()).Select();
                        if (tmp.Count() > 0)
                        {
                            foreach (var storageLock in tmp)
                            {
                                T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == storageLock.PLAN_LIST_ID).Single();
                                if (planList != null)
                                {
                                    planList.ORDERED_QUANTITY -= storageLock.LOCK_QUANTITY;
                                    await database.From<T_PLAN_LIST>().Update(planList);
                                }
                            }
                        }

                    }
                }
                else
                {
                    T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(p => p.PLAN_LIST_ID == planListId).Single();
                    if (planList != null)
                    {
                        planList.ORDERED_QUANTITY -= executingQuantity;
                        if (isComplete)
                        {
                            planList.FINISHED_QUANTITY += executingQuantity;
                        }
                        await database.From<T_PLAN_LIST>().Update(planList);
                    }
                }
            }
            catch (Exception) { }
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.UpdatePrevious 方法
         */
        public async System.Threading.Tasks.Task UpdatePrevious(int old, int @new)
        {
            await database.From<T_MANAGE_MAIN>()
                .Where(m => m.PREVIOUS_ID == old)
                .Update(new
                {
                    PREVIOUS_ID = @new,
                    UPDATE_TIME = DateTime.Now
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.UpdateStatus 方法
         */
        public async System.Threading.Tasks.Task UpdateStatus(int id, TaskState state, string message = null)
        {
            await database.From<T_MANAGE_MAIN>()
                .Where(m => m.MANAGE_ID == id)
                .Update(new
                {
                    MANAGE_STATUS = state.ToString(),
                    MANAGE_MSG = message,
                    UPDATE_TIME = DateTime.Now
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.UpdatePriority 方法
         */
        public async System.Threading.Tasks.Task UpdatePriority(int id, int priority)
        {
            await database.From<T_MANAGE_MAIN>()
                .Where(m => m.MANAGE_ID == id)
                .Update(new
                {
                    PRIORITY = priority,
                    UPDATE_TIME = DateTime.Now
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTriggers 方法
         */
        public async Task<IEnumerable<Trigger>> GetTriggers(int? warehouse = null, string device = null)
        {
            var schema = database.From<T_MANAGE_TRIGGER>();
            if (warehouse.HasValue)
            {
                schema.Where(t => t.TRIGGER_WAREHOUSE == warehouse);
            }
            if (device != null)
            {
                schema.Where(t => t.TRIGGER_DEVICE == device);
            }
            return mapper.Map<IEnumerable<Trigger>>(await schema.Select());
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.GetTrigger 方法
         */
        public async Task<Trigger> GetTrigger(int id)
        {
            var trigger = await database.From<T_MANAGE_TRIGGER>()
                .Where(t => t.TRIGGER_ID == id)
                .Single();
            return trigger == null ? null : mapper.Map<Trigger>(trigger);
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.CreateTrigger 方法
         */
        public async Task<int> CreateTrigger(Trigger trigger)
        {
            var entity = mapper.Map<T_MANAGE_TRIGGER>(trigger);
            entity.UPDATE_TIME = entity.CREATE_TIME = DateTime.Now;
            return Convert.ToInt32(await database.From<T_MANAGE_TRIGGER>().Add(entity));
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.DeleteTrigger 方法
         */
        public async System.Threading.Tasks.Task DeleteTrigger(int id)
        {
            await database.From<T_MANAGE_TRIGGER>()
                .Where(t => t.TRIGGER_ID == id)
                .Delete();
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.UpdateTrigger 方法
         */
        public async System.Threading.Tasks.Task UpdateTrigger(int id, int count, string remark)
        {
            await database.From<T_MANAGE_TRIGGER>()
                .Where(t => t.TRIGGER_ID == id)
                .Update(new
                {
                    EXEC_COUNT = count,
                    EXEC_REMARK = remark,
                    UPDATE_TIME = DateTime.Now
                });
        }

        /*
         * 实现 Kean.Domain.Task.Repositories.ITaskRepository.LogTrigger 方法
         */
        public async System.Threading.Tasks.Task LogTrigger(Trigger trigger)
        {
            var entity = mapper.Map<T_MANAGE_TRIGGER_HIS>(trigger);
            entity.UPDATE_TIME = entity.CREATE_TIME = DateTime.Now;
            await database.From<T_MANAGE_TRIGGER_HIS>().Add(entity);
        }

        public Dictionary<string, decimal> GetMaterialCountLaneway(int planListId)
        {
            Dictionary<string, decimal> dic = new Dictionary<string, decimal>();
            var storageQuantity = database.Context.Query($"select T_WH_CELL.CELL_LANEWAY,COALESCE(sum(V_STORAGE_LIST.STORAGE_LIST_QUANTITY),0) AS QUANTITY from V_STORAGE_LIST right join T_WH_CELL on T_WH_CELL.CELL_ID = V_STORAGE_LIST.CELL_ID LEFT JOIN T_PLAN_LIST ON T_PLAN_LIST.PLAN_LIST_ID = {planListId} WHERE T_WH_CELL.CELL_TYPE='Cell' AND T_WH_CELL.AREA_ID = 1  group by T_WH_CELL.CELL_LANEWAY", transaction: database.Context.Transaction);
            foreach (var item in storageQuantity)
            {
                dic.Add(item.CELL_LANEWAY, item.QUANTITY);
            }
            return dic;
        }
    }
}
