import { Component, Inject, ViewChild } from "@angular/core";
import { FormGroup, Validators, FormBuilder } from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";

@Component({
  selector: 'app-printconfirm',
  templateUrl: './printconfirm.component.html',
  styleUrls: ['./printconfirm.component.scss']
})
export class PrintconfirmComponent {
  private _form: FormGroup;
  private _printerIpList: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA)
    private _data: any,
    private _formBuilder: FormBuilder,
    private _dialogRef: MatDialogRef<PrintconfirmComponent>
  ) {
    console.log(this._data);
    this._printerIpList = this._data.info.items;
    this._form = this._formBuilder.group({
      printerIp: [this._data.info.bindPrinter?.printerIp ?? null, [Validators.required]],
    });
  }
  public get data() {
    return this._data;
  }
  public get form() {
    return this._form;
  }
  public get printerIpList() {
    return this._printerIpList;
  }

  public save = async () => {
    const printerIp: string = this._form.controls.printerIp.value;
    // var printerIpS = this.printerIpList.find((x) => x.printerIp == printerIp);
    this._dialogRef.close({
      printerIp: printerIp
    });
  };
}
