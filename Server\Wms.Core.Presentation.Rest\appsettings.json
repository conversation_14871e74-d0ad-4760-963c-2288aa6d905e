{
  "AllowedHosts": "*",
  "AllowedOrigins": "http://localhost:4200",
  "Swagger": {
    "Title": "Kean.Presentation.Rest",
    "Version": "v*",
    "Contact": {  
      "Name": "<PERSON>an",
      "Email": "<EMAIL>"
    }
  },
  "Database": {
    "Default": {
      "DriverClass": "Kean.Infrastructure.Database.MssqlDapperDriver",
      "ConnectionString": "Data Source=localhost\\SQL20*9;Initial Catalog=KSF_WMS_BS;User ID=sa;Password=*;Encrypt=False;",
      "MigrationType": "SqlServer"
    }
  },
  "NoSql": {
    "Redis": {
      "Default": {
        "DriverClass": "Kean.Infrastructure.NoSql.Redis.StackExchangeDriver",
        "ConnectionString": "*27.0.0.*:6379",
        "Database": 0
      }
    }
  },
  "Connections": {
    "OpcUa": {
      "Simulator": "opc.tcp://*27.0.0.*:49320"
    }
  },
  "Hangfire": {
    "RedisStorage": {
      "ConnectionString": "*27.0.0.*:6379",
      "Database": 2
    },
    "RecurringJobs": {
      "InstructionGenerateJob": "0/5 * * * * ?",
      "InstructionSendJob": "0/5 * * * * ?",
      "InstructionCompleteJob": "0/5 * * * * ?",
      "TaskTriggerJob": "0/5 * * * * ?",
      "WcsApplyJob": "0/5 * * * * ?",
      "WcsFeedbackJob": "0/5 * * * * ?"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug"
      //"Default": "Information",
      //"Override": {
      //  "Microsoft": "Warning",
      //  "System": "Warning",
      //  "Hangfire": "Warning"
      //}
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "Configure": [
            {
              "Name": "Logger",
              "Args": {
                "ConfigureLogger": {
                  "WriteTo": [
                    { "Name": "Console" },
                    {
                      "Name": "File",
                      "Args": {
                        "Path": "logs/general/.log",
                        "RollingInterval": "Day",
                        "OutputTemplate": "{Timestamp:HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}",
                        "RetainedFileCountLimit": 30
                      }
                    }
                  ],
                  "Filter": [
                    {
                      "Name": "ByIncludingOnly",
                      "Args": { "Expression": "EventId is null" }
                    }
                  ]
                }
              }
            },
            {
              "Name": "Logger",
              "Args": {
                "ConfigureLogger": {
                  "WriteTo": [
                    { "Name": "Console" },
                    {
                      "Name": "File",
                      "Args": {
                        "Path": "logs/device/.log",
                        "RollingInterval": "Day",
                        "OutputTemplate": "{Timestamp:HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}",
                        "RetainedFileCountLimit": 30
                      }
                    }
                  ],
                  "Filter": [
                    {
                      "Name": "ByIncludingOnly",
                      "Args": { "Expression": "EventId.Id=*" }
                    }
                  ]
                }
              }
            },
            {
              "Name": "Logger",
              "Args": {
                "ConfigureLogger": {
                  "WriteTo": [
                    { "Name": "Console" },
                    {
                      "Name": "File",
                      "Args": {
                        "Path": "logs/interface/.log",
                        "RollingInterval": "Day",
                        "OutputTemplate": "{Timestamp:HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}",
                        "RetainedFileCountLimit": 30
                      }
                    }
                  ],
                  "Filter": [
                    {
                      "Name": "ByIncludingOnly",
                      "Args": { "Expression": "EventId.Id=2" }
                    }
                  ]
                }
              }
            },
            {
              "Name": "Logger",
              "Args": {
                "ConfigureLogger": {
                  "WriteTo": [
                    {
                      "Name": "File",
                      "Args": {
                        "Path": "logs/error/error.log",
                        "rollOnFileSizeLimit": true,
                        "fileSizeLimitBytes": *048576,
                        "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Message:lj} (at {SourceContext}){NewLine}{Exception}"
                      }
                    }
                  ],
                  "Filter": [
                    {
                      "Name": "ByIncludingOnly",
                      "Args": { "Expression": "@x is not null" }
                    }
                  ]
                }
              }
            }
            //{
            //  "Name": "Elasticsearch",
            //  "Args": {
            //    "nodeUris": "http://localhost:9200",
            //    "indexFormat": "kean-{0:yyyy-MM-dd}",
            //    "autoRegisterTemplate": true
            //  }
            //}
          ]
        }
      }
    ]
  }
}
