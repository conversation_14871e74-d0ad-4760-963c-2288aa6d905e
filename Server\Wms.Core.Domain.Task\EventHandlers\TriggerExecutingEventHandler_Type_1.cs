﻿using Kean.Domain.Shared;
using Kean.Domain.Stock.Repositories;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Models;
using Kean.Domain.Task.Repositories;
using Kean.Infrastructure.Database.Repository;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 设备触发命令执行时，处理类型 1
    /// </summary>
    public sealed class TriggerExecutingEventHandler_Type_1(
        IOrderService orderService,
        IStockService stockService,
        ITaskService taskService,
        Task.Repositories.IWarehouseRepository warehouseRepository,
        ILogger<TriggerExecutingEventHandler_Type_1> logger,    // 日志
        ICommandBus commandBus,                                 // 命令总线
        INotification notification,                              // 总线通知
        IDefaultDb database
    ) : EventHandler<TriggerExecutingEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(TriggerExecutingEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Type == 1)
            {
                switch (@event.Parameter.Value<string>("fallback"))
                {


                    // 上架
                    default:

                        var data = @event.Parameter as JObject;
                        int plan_id = 0;
                        int plan_list_id = 0;
                        if (string.IsNullOrEmpty(data.Value<string>("Parameter01")))
                        {
                            await commandBus.Notify("申请参数", "Parameter01为空", null,
                                     cancellationToken: cancellationToken);
                            return;
                        }
                        if (string.IsNullOrEmpty(data.Value<string>("Parameter02")))
                        {
                            await commandBus.Notify("申请参数", "Parameter02为空", null,
                                     cancellationToken: cancellationToken);
                            return;
                        }
                        if (data.Value<string>("Parameter02") == "0" || data.Value<string>("Parameter01") == "0")
                        {
                            IO_CONTROL_ROUTE mIoControlRoute = await database.From<IO_CONTROL_ROUTE>().Where(p => (p.START_DEVICE == @event.Device && p.CONTROL_ROUTE_STATUS == 1 && p.CONTROL_ROUTE_TYPE == 4)).Single();
                            if (mIoControlRoute != null)
                            {
                                //判断申请位置,生成对应到异常口的任务
                                await taskService.Convey(@event.Warehouse, data.Value<string>("Barcode"), null, $"device:{mIoControlRoute.START_DEVICE}", $"device:{mIoControlRoute.END_DEVICE}", null, -1, null, null, null);
                                return;
                            }


                        }

                        // 查找并更新任务
                        bool isPlanUpdateSuccess = orderService.PlanSelectAndUpdate(data.Value<string>("Parameters"), out plan_id, out plan_list_id);
                        if (!isPlanUpdateSuccess)
                        {
                            //创建一个本地计划





                            await commandBus.Notify("目标位置", "未找到对应订单", null,
                                     cancellationToken: cancellationToken);
                            return;
                        }
                        var tmpPlanList = await database.From<T_PLAN_LIST>().Where(c => c.PLAN_LIST_ID == plan_list_id).Select();
                        if (tmpPlanList.Count() == 0)
                        {
                            await commandBus.Notify("订单id", "未找到对应订单行", null,
                                     cancellationToken: cancellationToken);
                            return;
                        }

                        var array = await orderService.GetOrderLine(plan_list_id);
                        foreach (var item in array)
                        {
                            item["Order"] = item["Id"];
                            item["Id"] = 0;
                            item["Quantity"] = data.Value<string>("Parameters").Split("|")[0];
                            item["ManageListQuantity"] = data.Value<string>("Parameters").Split("|")[0];
                            item["PlantCode"] = data.Value<string>("Parameters").Split("|")[1];
                            item["WorkGroup"] = data.Value<string>("Parameters").Split("|")[4];
                            item["ProductionLine"] = data.Value<string>("Parameters").Split("|")[3];

                        }

                        Station des = await warehouseRepository.GetStationByCode(@event.Device, 1);
                        if (des != null)
                        {
                            await stockService.Inbound(data.Value<string>("Barcode"), des.Id, array, true, 1, "infeed", true, DateTime.Now);
                        }

                        //string manageRemark = array.ToString(Newtonsoft.Json.Formatting.None); 

                        //await commandBus.Execute(new InfeedCommand
                        //{
                        //    Warehouse = @event.Warehouse,// int.TryParse(data.Value<string>("Warehouse"), out var w) ? w : 1,
                        //    Barcode = data.Value<string>("Barcode"),
                        //    Spec = null, // 如果需要接检尺信息，则从 data.Value<string>("Parameter") 中取
                        //    Original = $"device:{@event.Device}",
                        //    Destination = null,
                        //    Priority = null,
                        //    Operator = -1,
                        //    Tag = $"Stock",//1:创建库存 
                        //    Remark = manageRemark,
                        //    PlanId = plan_id,
                        //    PlanListId = plan_list_id,
                        //    ManageListQuantity = Convert.ToInt32(data.Value<string>("Parameters").Split("|")[0])//120|S|20250606|01|1515|0001|69001|1 -- 数量 | 工厂别 | 批次 | 产线别 | 生产时间 | 箱流水号 | 69码 | 识别码

                        //}, cancellationToken).ContinueWith(async t =>
                        //{
                        //    if (t.IsFaulted)
                        //    {
                        //        logger.LogWarning(t.Exception.InnerException, "申请处理异常，详见 InnerException：");
                        //        await commandBus.Notify(nameof(TriggerExecutingEvent), t.Exception.InnerException.Message, @event, cancellationToken: cancellationToken);
                        //    }

                        //    // 上架处理失败时，可根据具体情况指定降级操作，如回退、重试、蜂鸣、LED显示等形式
                        //    if (notification.Count > 0)
                        //    {
                        //        @event.Fallback("Reverse"); // 自定义 "Reverse" 字符串表示回退操作，这个字符串与下面的 case "Reverse":... 对应
                        //    }

                        //});
                        break;

                    // 回退
                    case "Reverse":

                        // TODO：实现回退逻辑。若回退失败可继续降级（ @event.Fallback("..."); ）

                        break;

                    // 其他降级操作
                    case "...":

                        // TODO

                        break;
                }
            }
        }
    }
}
