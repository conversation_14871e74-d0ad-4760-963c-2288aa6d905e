﻿using AutoMapper;
using <PERSON>pper;
using Kean.Domain;
using Kean.Domain.Stock;
using Kean.Domain.Stock.Enums;
using Kean.Domain.Stock.Models;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.NoSql.Repository.Default;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;

namespace Kean.Infrastructure.Repository
{
    /// <summary>
    /// 存储仓库
    /// </summary>
    public class StockRepository(
        IMapper mapper,         // 模型映射
        IUnitOfWork unitOfWork, // 工作单元
        IDefaultDb database,    // 默认数据库
        IDefaultRedis redis     // 默认 Redis
    ) : Domain.Stock.Repositories.IStockRepository
    {
        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.AcquireLock 方法
         */
        public async Task<IDisposable> AcquireLock(string barcode)
        {
            return await redis.Lock(barcode, unitOfWork.Number.ToString());
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetBarcode 方法
         */
        public async Task<string> GetBarcode(int cell)
        {
            return (await database.From<T_STORAGE_MAIN>()
                .Where(s => s.CELL_ID == cell)
                .Single(s => new { s.STOCK_BARCODE }))?.STOCK_BARCODE;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetCell 方法
         */
        public async Task<int?> GetCell(string barcode)
        {
            return (await database.From<T_STORAGE_MAIN>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Single(s => new { s.CELL_ID }))?.CELL_ID;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetSpec 方法
         */
        public async Task<int?> GetSpec(string barcode)
        {
            return (await database.From<T_STORAGE_MAIN>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Single(s => new { s.STOCK_SPEC }))?.STOCK_SPEC;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetStock 方法
         */
        public async Task<Stock> GetStock(string barcode)
        {
            var storageMain = await database.From<T_STORAGE_MAIN>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Single();
            if (storageMain == null)
            {
                return null;
            }
            var stock = mapper.Map<Stock>(storageMain);
            stock.Lines = mapper.Map<IEnumerable<StockLine>>(await database.From<T_STORAGE_LIST>()
                .Where(s => s.STORAGE_ID == storageMain.STORAGE_ID)
                .Select());
            return stock;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetLines 方法
         */
        public async Task<IEnumerable<StockLine>> GetLines(string barcode)
        {
            var query = database.From<T_STORAGE_MAIN>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Query(s => s.STORAGE_ID);
            return mapper.Map<IEnumerable<StockLine>>(await database.From<T_STORAGE_LIST>()
                .Where(s => query.Contains(s.STORAGE_ID))
                .Select());
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetLocks 方法
         */
        public async Task<IEnumerable<KeyValuePair<object, decimal>>> GetLocks(object locker)
        {
            var schema = database.From<T_STORAGE_LOCK>();
            schema = locker switch
            {
                int i => schema.Where(k => k.PLAN_LIST_ID == i),
                long l => schema.Where(k => k.PLAN_LIST_ID == l),
                string s => schema.Where(k => k.LOCK_CODE == s),
                _ => schema
            };
            return (await schema.Select()).Select(l => KeyValuePair.Create(locker, l.LOCK_QUANTITY));
            //    .ToDictionary(_ => locker, k => k.LOCK_QUANTITY);
            //return (await schema.Select(l => KeyValuePair.Create(locker, l.LOCK_QUANTITY))).ToDictionary(_ => locker, k => k.LOCK_QUANTITY);
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.CreateStock 方法
         */
        public async Task CreateStock(Stock stock)
        {
            var timestamp = DateTime.Now;
            bool isPK = false;
            if (stock.Cell != 0)
            {
                T_WH_CELL cell = await database.From<T_WH_CELL>().Where(p => p.CELL_ID == stock.Cell).Single();
                if (cell != null)
                {
                    if (cell.AREA_ID == 2)
                        isPK = true;
                }
            }
            int? id = null;
            int storageListId = 0;
            if (isPK)
            {
                int planlistId = (int)stock.Lines.First().Order;
                if (planlistId != 0)
                {
                    T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(g => g.PLAN_LIST_ID == planlistId).Single();
                    if (planList != null)
                    {
                        string batchNo = planList.GOODS_BATCH_NO;
                        int goodsId = planList.GOODS_ID;
                        var storagelist = (await database.From<V_STORAGE_LIST>()
                            .Where(s => s.GOODS_BATCH_NO == batchNo && s.GOODS_ID == goodsId && s.AREA_ID == 2)
                            .Single(s => new { s.STORAGE_ID, s.STORAGE_LIST_ID }));
                        if (storagelist != null)
                        {
                            id = storagelist.STORAGE_ID;
                            storageListId = storagelist.STORAGE_LIST_ID;
                        }
                    }
                }
            }
            else
            {
                id = (await database.From<T_STORAGE_MAIN>()
                    .Where(s => s.STOCK_BARCODE == stock.Barcode)
                    .Single(s => new { s.STORAGE_ID }))?
                    .STORAGE_ID;
            }
            if (!id.HasValue)
            {
                var storageMain = mapper.Map<T_STORAGE_MAIN>(stock);
                storageMain.CREATE_TIME = timestamp;
                storageMain.UPDATE_TIME = timestamp;
                id = Convert.ToInt32(await database.From<T_STORAGE_MAIN>().Add(storageMain));
            }
            if (stock.Lines != null)
            {
                foreach (var item in stock.Lines)
                {
                    if (stock.Lines.Count() == 1 && storageListId != 0)
                    {
                        item.Id = storageListId;
                    }
                    var storageList = mapper.Map<T_STORAGE_LIST>(item);
                    if (storageList.STORAGE_LIST_ID == 0)
                    {
                        storageList.STORAGE_ID = id.Value;
                        storageList.INBOUND_TIME = item.InboundTime == default ? (item.InboundTime = stock.Timestamp) : item.InboundTime;
                        storageList.INVENTORY_TIME = stock.Timestamp;
                        storageList.AVAILABLE_TIME ??= item.MinimumStockPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.MinimumStockPeriod.Value) : null;
                        storageList.UNAVAILABLE_TIME ??= item.MaximumStockPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.MaximumStockPeriod.Value) : null;
                        storageList.GOODS_EFF_TIME ??= item.EffectivePeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.EffectivePeriod.Value) : null;
                        storageList.GOODS_EXP_TIME ??= item.ExpirationPeriod.HasValue ? storageList.INBOUND_TIME.AddHours(item.ExpirationPeriod.Value) : null;
                        storageList.STORAGE_LIST_REMARK = Convert.ToString(item.Remark);
                        storageList.CREATE_TIME = timestamp;
                        storageList.UPDATE_TIME = timestamp;
                        storageList.PLANT_CODE = item.PlantCode;
                        storageList.CLIENT_CODE = item.ClientCode;
                        storageList.PLAN_LIST_ID = item.Order;
                        storageList.WORK_GROUP = item.WorkGroup;
                        storageList.PRODUCTION_LINE = item.ProductionLine;
                        try
                        {
                            T_STORAGE_MAIN storage = null;
                            if (storageList.GOODS_ID == 0 && storageList.PLAN_LIST_ID != 0)
                            {
                                T_PLAN_LIST planList = await database.From<T_PLAN_LIST>().Where(g => g.PLAN_LIST_ID == storageList.PLAN_LIST_ID).Single();
                                if (planList != null)
                                {
                                    item.Material = planList.GOODS_ID;
                                    storageList.GOODS_ID = planList.GOODS_ID;
                                    storageList.PLANT_CODE = planList.PLANT_CODE;
                                    storageList.CLIENT_CODE = planList.CLIENT_CODE;
                                    storageList.WORK_GROUP = planList.WORK_GROUP;
                                    storageList.PRODUCTION_LINE = planList.PRODUCTION_LINE;
                                    storageList.GOODS_BATCH_NO = planList.GOODS_BATCH_NO;
                                    storageList.GOODS_MFG = planList.GOODS_MFG;
                                    storageList.WAREHOUSE_CODE = planList.WAREHOUSE_CODE;
                                    storageList.TRANSFER_TYPE = planList.TRANSFER_TYPE;
                                }
                            }
                            T_GOODS_MAIN goodsMain = await database.From<T_GOODS_MAIN>().Where(g => g.GOODS_ID == storageList.GOODS_ID).Single();
                            if (goodsMain != null && goodsMain.PALLET_QUANTITY != 0)
                            {
                                storage = await database.From<T_STORAGE_MAIN>().Where(k => k.STORAGE_ID == id).Single();
                                if (storage != null && storageList.STORAGE_LIST_QUANTITY >= goodsMain.PALLET_QUANTITY)
                                {
                                    storage.FULL_FLAG = true;
                                }
                                else
                                {
                                    storage.FULL_FLAG = false;
                                }
                                await database.From<T_STORAGE_MAIN>().Update(storage);
                            }
                        }
                        catch (Exception)
                        {

                        }

                        item.Id = Convert.ToInt32(await database.From<T_STORAGE_LIST>().Add(storageList));
                    }
                    else
                    {
                        var existence = await database.From<T_STORAGE_LIST>()
                            .Where(s => s.STORAGE_LIST_ID == storageList.STORAGE_LIST_ID && s.STORAGE_ID == id.Value)
                            .Single()
                            ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                        existence.STORAGE_LIST_QUANTITY += storageList.STORAGE_LIST_QUANTITY;
                        existence.INVENTORY_TIME = stock.Timestamp;
                        existence.UPDATE_TIME = timestamp;
                        await database.From<T_STORAGE_LIST>().Update(existence);
                        mapper.Map(existence, item, opts => opts.BeforeMap((source, _) =>
                        {
                            source.STORAGE_LIST_QUANTITY = item.Quantity;
                            source.PLAN_LIST_ID = item.Order;
                        }));
                    }
                }
            }
            else
            {

            }
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.DeleteStock 方法
         */
        public async Task DeleteStock(Stock stock)
        {
            var timestamp = DateTime.Now;
            if (stock.Lines == null)
            {
                await database.From<T_STORAGE_MAIN>()
                    .Where(s => s.STOCK_BARCODE == stock.Barcode)
                    .Delete();
            }
            else
            {
                await database.From<T_STORAGE_MAIN>()
                    .Where(s => s.STOCK_BARCODE == stock.Barcode)
                    .Update(new
                    {
                        FULL_FLAG = false,
                        UPDATE_TIME = timestamp
                    });
                foreach (var item in stock.Lines)
                {
                    var existence = await database.From<T_STORAGE_LIST>()
                        .Where(s => s.STORAGE_LIST_ID == item.Id)
                        .Single()
                        ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                    if ((existence.STORAGE_LIST_QUANTITY += item.Quantity) < 0)
                    {
                        throw new RepositoryException(ErrorEnum.出库数量大于库存数量, new(nameof(item.Quantity), item.Quantity));
                    }
                    var locked = await database.From<T_STORAGE_LOCK>()
                        .Where(l => l.STORAGE_LIST_ID == item.Id).OrderBy(o => o.LOCK_ID, Order.Ascending)
                        .Select();
                    decimal k = locked.Sum(l => l.LOCK_ID <= item.Lock ? 0 : l.LOCK_QUANTITY);
                    if (locked.Any() && existence.STORAGE_LIST_QUANTITY < locked.Sum(l => l.LOCK_ID <= item.Lock ? 0 : l.LOCK_QUANTITY))
                    {
                        throw new RepositoryException(ErrorEnum.出库数量大于可用库存数量, new(nameof(item.Quantity), item.Quantity));
                    }
                    if (existence.STORAGE_LIST_QUANTITY == 0)
                    {
                        await database.From<T_STORAGE_LIST>()
                            .Delete(existence);
                    }
                    else
                    {
                        existence.INVENTORY_TIME = stock.Timestamp;
                        existence.UPDATE_TIME = timestamp;
                        await database.From<T_STORAGE_LIST>().Update(existence);
                    }
                    mapper.Map(existence, item, opts => opts.BeforeMap((source, _) =>
                    {
                        source.STORAGE_LIST_QUANTITY = item.Quantity;
                        source.PLAN_LIST_ID = item.Order;
                    }));
                }
                if ((await database.From<T_STORAGE_MAIN, T_STORAGE_LIST>()
                    .Join(Join.Inner, (m, l) => m.STORAGE_ID == l.STORAGE_ID)
                    .Where((m, l) => m.STOCK_BARCODE == stock.Barcode)
                    .Single((m, l) => new { Count = Function.Count(l.STORAGE_LIST_ID) }))
                    .Count == 0
                    &&
                    (await database.From<T_WH_CELL>()
                    .Where(c => c.CELL_ID == stock.Cell)
                    .Single(c => new { c.CELL_TYPE }))?.CELL_TYPE != "Cell")
                {
                    await database.From<T_STORAGE_MAIN>()
                        .Where(s => s.STOCK_BARCODE == stock.Barcode)
                        .Delete();
                }
            }
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.UpdateStock 方法
         */
        public async Task UpdateStock(Stock stock)
        {
            var timestamp = DateTime.Now;
            var properties = new Dictionary<string, (PropertyInfo entity, PropertyInfo model)>();
            foreach (var item in stock.Lines)
            {
                var existence = await database.From<T_STORAGE_LIST>()
                    .Where(s => s.STORAGE_LIST_ID == item.Id)
                    .Single()
                    ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                var remark = item.Remark as JObject;
                var key = remark["property"]?.Value<string>();
                if (!properties.ContainsKey(key))
                {
                    if (!string.IsNullOrEmpty(key) && (mapper.ConfigurationProvider as global::AutoMapper.Internal.IGlobalConfiguration)?
                        .FindTypeMapFor<T_STORAGE_LIST, StockLine>()?.PropertyMaps
                        .FirstOrDefault(p => p.DestinationName.Equals(key, StringComparison.OrdinalIgnoreCase))?
                        .CustomMapExpression.Body is MemberExpression expression)
                    {
                        properties.Add(key, (
                            typeof(T_STORAGE_LIST).GetProperty(expression.Member.Name),
                            typeof(StockLine).GetProperty(key, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance))
                        );
                    }
                    else
                    {
                        throw new RepositoryException(ErrorEnum.找不到属性, new(nameof(item.Remark), item.Remark));
                    }
                }
                var (entity, model) = properties[key];
                var oldValue = entity.GetValue(existence);
                var newValue = model.GetValue(item);
                if (newValue?.Equals(oldValue) == true)
                {
                    var message = remark["message"]?.Value<string>();
                    if (!string.IsNullOrEmpty(message))
                    {
                        existence.STORAGE_LIST_REMARK = message;
                        existence.UPDATE_TIME = timestamp;
                        await database.From<T_STORAGE_LIST>().Update(existence);
                    }
                    item.Id = -item.Id;
                }
                else
                {
                    entity.SetValue(existence, newValue);
                    existence.INVENTORY_TIME = stock.Timestamp;
                    existence.STORAGE_LIST_REMARK = remark["message"]?.Value<string>();
                    existence.UPDATE_TIME = timestamp;
                    await database.From<T_STORAGE_LIST>().Update(existence);
                    mapper.Map(existence, item);
                    remark["@property"] = entity.Name;
                    remark["@value"] = oldValue == null ? null : JToken.FromObject(oldValue);
                    remark["@message"] = remark["message"];
                }
            }
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.SplitStock 方法
         */
        public async Task<IEnumerable<Stock>> SplitStock(IEnumerable<StockLine> lines)
        {
            var stocks = new Dictionary<int, Stock>();
            foreach (var item in lines)
            {
                var existence = await database.From<T_STORAGE_LIST>()
                    .Where(s => s.STORAGE_LIST_ID == item.Id)
                    .Single()
                    ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                if (!stocks.TryGetValue(existence.STORAGE_ID, out var stock))
                {
                    stock = mapper.Map<Stock>(await database.From<T_STORAGE_MAIN>()
                        .Where(s => s.STORAGE_ID == existence.STORAGE_ID)
                        .Single());
                    stock.Lines = new List<StockLine>();
                    stocks.Add(existence.STORAGE_ID, stock);
                }
                var quantity = item.Quantity;
                mapper.Map(existence, item).Quantity = quantity;
                (stock.Lines as List<StockLine>).Add(item);
            }
            return stocks.Values;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.TakeStock 方法
         */
        public async Task<IEnumerable<StockLine>> TakeStock(IEnumerable<StockLine> lines)
        {
            var dictionary = lines.ToDictionary(l => l.Id, l => l);
            foreach (var item in await database.From<T_STORAGE_LIST>()
                .Where(l => dictionary.Keys.Contains(l.STORAGE_LIST_ID))
                .Select())
            {
                var quantity = dictionary[item.STORAGE_LIST_ID].Quantity;
                mapper.Map(item, dictionary[item.STORAGE_LIST_ID]).Quantity = quantity - item.STORAGE_LIST_QUANTITY;
            }
            return lines;
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.LockStock 方法
         */
        public async Task LockStock(Stock stock)
        {
            var timestamp = DateTime.Now;
            foreach (var item in stock.Lines)
            {
                var storageLock = await database.From<T_STORAGE_LOCK>()
                    .Where(l => l.STORAGE_LIST_ID == item.Id)
                    .Select();
                var quantity = item.Quantity;
                if (item.Lock > 0)
                {
                    if (quantity < 0)
                    {
                        quantity += storageLock.FirstOrDefault(l => l.LOCK_ID == item.Lock.Value)?.LOCK_QUANTITY ?? 0;
                        if (quantity < 0)
                        {
                            throw new RepositoryException(ErrorEnum.解锁数量大于锁定数量, new(nameof(item.Quantity), item.Quantity));
                        }
                    }
                    if (quantity == 0)
                    {
                        await database.From<T_STORAGE_LOCK>()
                            .Where(l => l.LOCK_ID == item.Lock.Value)
                            .Delete();
                    }
                    else
                    {
                        var storageList = await database.From<T_STORAGE_LIST>()
                            .Where(s => s.STORAGE_LIST_ID == item.Id)
                            .Single()
                            ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                        if (quantity > storageList.STORAGE_LIST_QUANTITY - storageLock.Sum(l => l.LOCK_ID == item.Lock.Value ? 0 : l.LOCK_QUANTITY))
                        {
                            throw new RepositoryException(ErrorEnum.锁定数量大于可用数量, new(nameof(item.Quantity), item.Quantity));
                        }
                        await database.From<T_STORAGE_LOCK>()
                            .Where(l => l.LOCK_ID == item.Lock.Value)
                            .Update(new
                            {
                                LOCK_QUANTITY = quantity,
                                UPDATE_TIME = timestamp
                            });
                    }
                }
                else
                {
                    var storageList = await database.From<T_STORAGE_LIST>()
                        .Where(s => s.STORAGE_LIST_ID == item.Id)
                        .Single()
                        ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                    if (quantity > storageList.STORAGE_LIST_QUANTITY - storageLock.Sum(l => l.LOCK_QUANTITY))
                    {
                        throw new RepositoryException(ErrorEnum.锁定数量大于可用数量, new(nameof(item.Quantity), item.Quantity));
                    }
                    await database.From<T_STORAGE_LOCK>().Add(new()
                    {
                        STORAGE_LIST_ID = item.Id,
                        PLAN_LIST_ID = item.Order,
                        LOCK_CODE = item.Remark != null ? item.Remark.ToString() : "",
                        LOCK_QUANTITY = item.Quantity,
                        CREATE_TIME = timestamp,
                        UPDATE_TIME = timestamp
                    });
                }
            }
        }

        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.TransferStock 方法
         */
        public async Task TransferStock(string barcode, int? spec, int destination)
        {
            await database.From<T_STORAGE_MAIN>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Update(spec.HasValue ?
                    new
                    {
                        STOCK_SPEC = spec,
                        CELL_ID = destination,
                        UPDATE_TIME = DateTime.Now
                    } :
                    new
                    {
                        CELL_ID = destination,
                        UPDATE_TIME = DateTime.Now
                    });
        }



        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.OrderStock 方法
         */
        public async Task OrderStock(Stock stock)
        {
            //计划出库，包含计划单信息
            if (stock.Orders != null)
            {
                if (stock.Orders.Count() != 0)
                {
                    foreach (var item in stock.Lines)
                    {
                        if (item.Order == null)
                        {
                            foreach (var order in stock.Orders)
                            {
                                var Planlist = await database.From<T_PLAN_LIST>()
                                        .Where(p => p.PLAN_LIST_ID == order).Single();
                                if (Planlist == null)
                                {
                                    throw new RepositoryException(ErrorEnum.未匹配到计划单行, new(null, order));
                                }

                                //出库条件：物料+批次+SN
                                if (Planlist.GOODS_ID == item.Material && Planlist.GOODS_BATCH_NO == item.Batch)
                                {
                                    if (Planlist.PLANNED_QUANTITY - Planlist.ORDERED_QUANTITY - Planlist.FINISHED_QUANTITY >= item.Quantity)
                                    {
                                        Planlist.ORDERED_QUANTITY += item.Quantity;
                                        item.Order = Planlist.PLAN_LIST_ID;
                                        break;
                                    }
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            if (item.Order == null)
                            {
                                throw new RepositoryException(ErrorEnum.未匹配到计划单行, new(null, item));
                            }
                        }
                    }
                }
            }
        }


        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.LockStockReturn 方法
         */
        public async Task<IEnumerable<StockLine>> LockStockReturn(Stock stock)
        {
            var timestamp = DateTime.Now;
            List<StockLine> lines = new List<StockLine>();
            foreach (var item in stock.Lines)
            {
                StockLine stockLine = new StockLine();
                var storageLock = await database.From<T_STORAGE_LOCK>()
                    .Where(l => l.STORAGE_LIST_ID == item.Id)
                    .Select();
                var quantity = item.Quantity;
                if (item.Lock > 0)
                {
                    if (quantity < 0)
                    {
                        quantity += storageLock.FirstOrDefault(l => l.LOCK_ID == item.Lock.Value)?.LOCK_QUANTITY ?? 0;
                        if (quantity < 0)
                        {
                            throw new RepositoryException(ErrorEnum.解锁数量大于锁定数量, new(nameof(item.Quantity), item.Quantity));
                        }
                    }
                    if (quantity == 0)
                    {
                        await database.From<T_STORAGE_LOCK>()
                            .Where(l => l.LOCK_ID == item.Lock.Value)
                            .Delete();
                    }
                    else
                    {
                        var storageList = await database.From<T_STORAGE_LIST>()
                            .Where(s => s.STORAGE_LIST_ID == item.Id)
                            .Single()
                            ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                        if (quantity > storageList.STORAGE_LIST_QUANTITY - storageLock.Sum(l => l.LOCK_ID == item.Lock.Value ? 0 : l.LOCK_QUANTITY))
                        {
                            throw new RepositoryException(ErrorEnum.锁定数量大于可用数量, new(nameof(item.Quantity), item.Quantity));
                        }
                        await database.From<T_STORAGE_LOCK>()
                            .Where(l => l.LOCK_ID == item.Lock.Value)
                            .Update(new
                            {
                                LOCK_QUANTITY = quantity,
                                UPDATE_TIME = timestamp
                            });
                    }
                }
                else
                {
                    var storageList = await database.From<T_STORAGE_LIST>()
                        .Where(s => s.STORAGE_LIST_ID == item.Id)
                        .Single()
                        ?? throw new RepositoryException(ErrorEnum.找不到库存行记录, new(nameof(item.Id), item.Id));
                    if (quantity > storageList.STORAGE_LIST_QUANTITY - storageLock.Sum(l => l.LOCK_QUANTITY))
                    {
                        throw new RepositoryException(ErrorEnum.锁定数量大于可用数量, new(nameof(item.Quantity), item.Quantity));
                    }
                    var lockid = await database.From<T_STORAGE_LOCK>().Add(new()
                    {
                        STORAGE_LIST_ID = item.Id,
                        PLAN_LIST_ID = item.Order,
                        LOCK_CODE = item.Remark == null ? "" : item.Remark.ToString(),
                        LOCK_QUANTITY = item.Quantity,
                        CREATE_TIME = timestamp,
                        UPDATE_TIME = timestamp
                    });

                    stockLine = mapper.Map<StockLine>(await database.From<V_STORAGE_LOCK>()
                                 .Where(l => l.LOCK_ID == Convert.ToInt32(lockid))
                                 .Single());
                    lines.Add(stockLine);
                }
            }

            return lines;
        }


        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.CellLK 方法
         */
        public async Task<bool> CellLK(string barcode)
        {
            var storage = await database.From<V_STORAGE_LIST>()
                .Where(s => s.STOCK_BARCODE == barcode && s.AREA_TYPE == "LiKu" && s.CELL_TYPE == "Cell")
                .Single();
            return storage != null;

        }



        /*
         * 实现 Kean.Domain.Stock.Repositories.IStockRepository.GetLockedLines 方法
         */
        public async Task<IEnumerable<StockLine>> GetLockedLines(string barcode)
        {
            var lst = await database.From<V_STORAGE_LOCK>()
                .Where(s => s.STOCK_BARCODE == (barcode))
                .Select();
            return mapper.Map<IEnumerable<StockLine>>(lst);
            return mapper.Map<IEnumerable<StockLine>>(await database.From<V_STORAGE_LOCK>()
                .Where(s => s.STOCK_BARCODE == barcode)
                .Select());
        }


    }
}
