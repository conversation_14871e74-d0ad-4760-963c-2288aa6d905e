﻿using Kean.Infrastructure.Configuration;
using System;
using System.Collections.Generic;

namespace Kean.Application.Query.ViewModels
{
    /// <summary>
    /// 订单视图
    /// </summary>
    public sealed class Order : MaterialProperty
    {
        /// <summary>
        /// 标识
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 行为
        /// </summary>
        public IEnumerable<string> Action { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Creater { get; set; }

        /// <summary>
        /// 制单时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 终结时间
        /// </summary>
        public DateTime? FinalTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 行标识
        /// </summary>
        public int? Line { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        public int? Material { get; set; }

        /// <summary>
        /// 料号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 物料名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 品类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 物料组
        /// </summary>
        public string Group { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 执行数量
        /// </summary>
        public decimal? Executing { get; set; }

        /// <summary>
        /// 完成数量
        /// </summary>
        public decimal? Finished { get; set; }

        /// <summary>
        /// 40单号
        /// </summary>
        public string Plancode40 { get; set; }

        /// <summary>
        /// 99单号
        /// </summary>
        public string Plancode99 { get; set; }

        /// <summary>
        /// 月台
        /// </summary>
        public string Platform { get; set; }


        /// <summary>
        /// 库存
        /// </summary>
        public int? StorageLine { get; set; }
        /// <summary>
        /// 标记
        /// </summary>
        public bool PlanListFlag { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string PlanListRemark { get; set; }
        /// <summary>
        /// 仓库确认
        /// </summary>
        public string PlanListRepo { get; set; }

        /// <summary>
        /// 生产确认
        /// </summary>
        public string PlanListProd { get; set; }

        /// <summary>
        /// 最小库存时间
        /// </summary>
        public string MinStorageTime { get; set; }
        /// <summary>
        /// 最大库存时间
        /// </summary>
        public string MaxStorageTime { get; set; }
        public string SapType { get; set; }
    }
}
