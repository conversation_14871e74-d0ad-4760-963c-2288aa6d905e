﻿using System;

namespace Kean.Infrastructure.Database.Repository.Default.Entities
{
    public class T_PLAN_SAP_TYPE : IEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Identifier(true)]
        public int PLAN_SAP_TYPE_ID { get; set; }

        /// <summary>
        /// 类型编码
        /// </summary>
        public string PLAN_SAP_TYPE_CODE { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string PLAN_SAP_TYPE_NAME { get; set; }

        /// <summary>
        /// 类型出入
        /// </summary>
        public string PLAN_SAP_TYPE_INOUT { get; set; }

        /// <summary>
        /// 确认单类型编码
        /// </summary>
        public string PLAN_SAP_TYPE_RETURN { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool PLAN_SAP_TYPE_FLAG { get; set; }


    }
}
