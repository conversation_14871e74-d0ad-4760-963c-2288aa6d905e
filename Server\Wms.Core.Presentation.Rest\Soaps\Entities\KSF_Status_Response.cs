﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    [XmlRoot("SAPPushInventoryStatusResult")]
    public class KSF_Status_Response
    {
        public string STATUS { get; set; }

        public string INFOTEXT { get; set; }

    }
}
