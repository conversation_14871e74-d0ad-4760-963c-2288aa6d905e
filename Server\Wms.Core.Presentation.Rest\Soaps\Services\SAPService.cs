﻿/*
 * 这是一个例子，对外发布一个 WebService 接口，路径为 http://xxxxx/soap/public
 * 这里是具体实现
 */

using AutoMapper;
using Kean.Application.Command.Interfaces;
using Kean.Application.Command.ViewModels;
using Kean.Application.Query.Interfaces;
using Kean.Infrastructure.Soap;
using Kean.Infrastructure.Utilities;
using Kean.Presentation.Rest.Controllers.Public;
using Kean.Presentation.Rest.Soaps.Contracts;
using Kean.Presentation.Rest.Soaps.Entities;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Soaps.Services
{
    [Route("soap/sap")]
    public class SAPService : SAPServiceContract
    {
        private readonly ILogger<DemoController> _logger;
        private readonly Application.Command.Interfaces.IInterfaceService _interfaceService;
        private readonly Application.Command.Interfaces.IMaterialService _materialCommandService;
        private readonly Application.Query.Interfaces.IMaterialService _materialQueryService;
        private readonly IMapper _mapper;


        // 根据实际情况依赖注入
        public SAPService(
            IMapper mapper,
            ILogger<DemoController> logger,
            Application.Command.Interfaces.IInterfaceService interfaceService,
            Application.Command.Interfaces.IMaterialService materialCommandService,
            Application.Query.Interfaces.IMaterialService materialQueryService)
        {
            _logger = logger;
            _interfaceService = interfaceService;
            _materialCommandService = materialCommandService;
            _materialQueryService = materialQueryService;
            _mapper = mapper;

        }
        public string UpdateMaterial(string xml)
        {
            string sResult = string.Empty;
            MaterialContract mSapRequest1 = new MaterialContract() { MAKTX = "1", MATNR = "2", MEABM = "3" };
            MaterialContract mSapRequest2 = new MaterialContract() { MAKTX = "4", MATNR = "5", MEABM = "6" };
            MaterialList lst = new MaterialList();
            lst.sig = "123";
            lst.lstMaterial.Add(mSapRequest1);
            lst.lstMaterial.Add(mSapRequest2);
            string abc = XmlHelper.Serialize(lst);
            MaterialList a = XmlHelper.Deserialize<MaterialList>(xml);
            foreach (MaterialContract mGood in a.lstMaterial)
            {
                Task<IEnumerable<Application.Query.ViewModels.Material>> materials = _materialQueryService.GetMaterialList(mGood.MATNR, null, null, null, null, null, null);
                if (materials != null && materials.Result.Count() > 0)
                {
                    _materialCommandService.ModifyMaterial(_mapper.Map<Material>(materials.Result));
                }
                else
                {
                    _materialCommandService.CreateMaterial(_mapper.Map<Material>(materials.Result));
                }
            }
            return sResult;
        }

        public string UpdateStorageStatus(string xml)
        {
            string sResult = string.Empty;
            MaterialContract mSapRequest1 = new MaterialContract() { MAKTX = "1", MATNR = "2", MEABM = "3" };
            MaterialContract mSapRequest2 = new MaterialContract() { MAKTX = "4", MATNR = "5", MEABM = "6" };
            MaterialList lst = new MaterialList();
            lst.sig = "123";
            lst.lstMaterial.Add(mSapRequest1);
            lst.lstMaterial.Add(mSapRequest2);
            string abc = XmlHelper.Serialize(lst);
            MaterialList a = XmlHelper.Deserialize<MaterialList>(xml);
            foreach (MaterialContract mGood in a.lstMaterial)
            {
                Task<IEnumerable<Application.Query.ViewModels.Material>> materials = _materialQueryService.GetMaterialList(mGood.MATNR, null, null, null, null, null, null);
                if (materials != null && materials.Result.Count() > 0)
                {
                    _materialCommandService.ModifyMaterial(_mapper.Map<Material>(materials.Result));
                }
                else
                {
                    _materialCommandService.CreateMaterial(_mapper.Map<Material>(materials.Result));
                }
            }
            return sResult;
        }

        public string SendTask(string xml)
        {
            string sResult = string.Empty;
            KSF_Notice sapNotice = new KSF_Notice();
            API_Message mAPI_Message = new API_Message() { SourceSys = "01", TargetSys = "wms" };
            TaskElements taskElements = new TaskElements();
            TaskElement taskElement = new TaskElement();
            List<HEADER> listHeader = new List<HEADER>();
            List<DETAIL> listDetail = new List<DETAIL>();
            HEADER t0 = new HEADER() { NAME1 = "test", BSART = "2" };
            DETAIL l0 = new DETAIL() { POSNR = "11", INSMK = "2" };
            listHeader.Add(t0);
            listDetail.Add(l0);
            listHeader.Add(t0);
            listDetail.Add(l0);
            sapNotice.API_Message = mAPI_Message;
            if (taskElement.HEADER.Count == 0)// == null)
            {
                taskElement.HEADER = listHeader.ToList();//.ToArray();
            }
            if (taskElement.DETAIL.Count == 0)// == null)
            {
                taskElement.DETAIL = listDetail.ToList();//.ToArray();
            }

            sapNotice.TaskElements = taskElements;
            taskElements.Element = taskElement;
            //sapNotice.lstDetail = listDetail;
            //sapNotice.lstHeader = listHeader;

            xml = XmlHelper.Serialize(sapNotice);

            //return abc;

            MaterialList a = XmlHelper.Deserialize<MaterialList>(xml);
            foreach (MaterialContract mGood in a.lstMaterial)
            {
                Task<IEnumerable<Application.Query.ViewModels.Material>> materials = _materialQueryService.GetMaterialList(mGood.MATNR, null, null, null, null, null, null);
                if (materials != null && materials.Result.Count() > 0)
                {
                    _materialCommandService.ModifyMaterial(_mapper.Map<Material>(materials.Result));
                }
                else
                {
                    _materialCommandService.CreateMaterial(_mapper.Map<Material>(materials.Result));
                }
            }
            return sResult;
        }

        // 这是一个接口（对 ERP 开放创建任务功能）
        public async Task<DemoResponseContract> CreateTask(DemoRequestContract request)
        {
            _logger.LogInformation("ERP 调用接口啦！");

            // 统一使用 IInterfaceService.Input 方法，重点构造 data 参数
            // 具体业务在 Domain.Interface 中，根据这里传入的 "ERP"、"CreateTask" 等参数集中处理
            // 这个方法会返回一个 Failure，可以按照协议的应答格式反馈给调用者（例如 ERP）
            await _interfaceService.Input(
                scope: "ERP",                                                               // 与谁的接口
                function: "CreateTask",                                                     // 接口的功能标识
                unique: Guid.NewGuid().ToString(),                                          // 关键字，便于关联后续接口，可能由调用者传来，也可能是自己生成，也可以为空
                message: JsonHelper.Serialize(request),                                     // 原始报文，可记可不记
                data: new Dictionary<string, string>() { ["Barcode"] = request.Barcode, },  // 接口数据，这个很重要
                async: true                                                                 // 同步还是异步（同步就是直接处理业务，并反馈结果；异步就是先缓存到数据库里，另外线程处理，无法立即反馈结果）
            );

            return new DemoResponseContract { Result = "OK" };
        }
    }
}
