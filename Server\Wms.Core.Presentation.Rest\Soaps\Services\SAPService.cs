﻿/*
 * 这是一个例子，对外发布一个 WebService 接口，路径为 http://xxxxx/soap/public
 * 这里是具体实现
 */

using AutoMapper;
using Azure;
using Kean.Application.Command.Interfaces;
using Kean.Application.Command.ViewModels;
using Kean.Application.Query.Interfaces;
using Kean.Infrastructure.Database.Repository;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using Kean.Infrastructure.Soap;
using Kean.Infrastructure.Utilities;
using Kean.Presentation.Rest.Controllers.Public;
using Kean.Presentation.Rest.Soaps.Contracts;
using Kean.Presentation.Rest.Soaps.Entities;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using static Org.BouncyCastle.Pqc.Crypto.Utilities.PqcOtherInfoGenerator;

namespace Kean.Presentation.Rest.Soaps.Services
{
    [Route("soap/sap")]
    public class SAPService : SAPServiceContract
    {
        private readonly ILogger<DemoController> _logger;
        private readonly Application.Command.Interfaces.IInterfaceService _interfaceService;
        private readonly Application.Command.Interfaces.IMaterialService _materialCommandService;
        private readonly Application.Query.Interfaces.IMaterialService _materialQueryService;
        private readonly IMapper _mapper;
        private readonly Application.Command.Interfaces.IOrderService _orderService;


        // 根据实际情况依赖注入
        public SAPService(
            IMapper mapper,
            ILogger<DemoController> logger,
            Application.Command.Interfaces.IInterfaceService interfaceService,
            Application.Command.Interfaces.IMaterialService materialCommandService,
            Application.Query.Interfaces.IMaterialService materialQueryService,
            Application.Command.Interfaces.IOrderService orderService
            )
        {
            _logger = logger;
            _interfaceService = interfaceService;
            _materialCommandService = materialCommandService;
            _materialQueryService = materialQueryService;
            _mapper = mapper;
            _orderService = orderService;

        }
        public string UpdateMaterial(string xml)
        {
            string sResult = string.Empty;
            MaterialContract mSapRequest1 = new MaterialContract() { MAKTX = "1", MATNR = "2", MEABM = "3" };
            MaterialContract mSapRequest2 = new MaterialContract() { MAKTX = "4", MATNR = "5", MEABM = "6" };
            MaterialList lst = new MaterialList();
            lst.sig = "123";
            lst.lstMaterial.Add(mSapRequest1);
            lst.lstMaterial.Add(mSapRequest2);
            string abc = XmlHelper.Serialize(lst);
            MaterialList a = XmlHelper.Deserialize<MaterialList>(xml);
            foreach (MaterialContract mGood in a.lstMaterial)
            {
                Task<IEnumerable<Application.Query.ViewModels.Material>> materials = _materialQueryService.GetMaterialList(mGood.MATNR, null, null, null, null, null, null);
                if (materials != null && materials.Result.Count() > 0)
                {
                    _materialCommandService.ModifyMaterial(_mapper.Map<Material>(materials.Result));
                }
                else
                {
                    _materialCommandService.CreateMaterial(_mapper.Map<Material>(materials.Result));
                }
            }
            return sResult;
        }

        public string UpdateStorageStatus(string xml)
        {
            string sResult = string.Empty;
            MaterialContract mSapRequest1 = new MaterialContract() { MAKTX = "1", MATNR = "2", MEABM = "3" };
            MaterialContract mSapRequest2 = new MaterialContract() { MAKTX = "4", MATNR = "5", MEABM = "6" };
            MaterialList lst = new MaterialList();
            lst.sig = "123";
            lst.lstMaterial.Add(mSapRequest1);
            lst.lstMaterial.Add(mSapRequest2);
            string abc = XmlHelper.Serialize(lst);
            MaterialList a = XmlHelper.Deserialize<MaterialList>(xml);
            foreach (MaterialContract mGood in a.lstMaterial)
            {
                Task<IEnumerable<Application.Query.ViewModels.Material>> materials = _materialQueryService.GetMaterialList(mGood.MATNR, null, null, null, null, null, null);
                if (materials != null && materials.Result.Count() > 0)
                {
                    _materialCommandService.ModifyMaterial(_mapper.Map<Material>(materials.Result));
                }
                else
                {
                    _materialCommandService.CreateMaterial(_mapper.Map<Material>(materials.Result));
                }
            }
            return sResult;
        }

        public async Task<string> SendTask(string xml)
        {
            string sResult = string.Empty;
            KSF_Notice ksfPara = XmlHelper.Deserialize<KSF_Notice>(xml);
            KSF_Notice_Response ksfResult = new KSF_Notice_Response();
            if (ksfPara.API_Message.TargetSys != "11001" || ksfPara.API_Message.SourceSys != "23006")
            {
                ksfResult.STATUS = "1";
                ksfResult.INFOTEXT = $"调用方编号:{ksfPara.API_Message.SourceSys}或目标方编号:{ksfPara.API_Message.TargetSys}不正确";
                sResult = XmlHelper.Serialize(ksfResult);
                return sResult;
            }

            foreach (HEADER mHeader in ksfPara.TaskElements.Element.HEADER)
            {
                #region HEADER数据判断
                /*
                //HEADER
                //判断 单据类型 BSART
                if (string.IsNullOrEmpty(mHeader.BSART))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"单据类型:{mHeader.BSART} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //判断 单据号码 EBELN
                if (string.IsNullOrEmpty(mHeader.EBELN))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"单据号码:{mHeader.EBELN} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //判断 仓库代码 LGPLA
                if (string.IsNullOrEmpty(mHeader.LGPLA))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"仓库代码:{mHeader.LGPLA} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //判断 货主代码 VTXTK
                if (string.IsNullOrEmpty(mHeader.VTXTK))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"货主代码:{mHeader.VTXTK} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //判断 供应商/送达方 LIFNR
                if (string.IsNullOrEmpty(mHeader.LIFNR))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"供应商:{mHeader.LIFNR} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //判断 供应商/送达方名称 NAME1
                if (string.IsNullOrEmpty(mHeader.NAME1))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"供应商名称:{mHeader.NAME1} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                //无需判断
                //------------------//
                //售达方 KUNAG
                //承运商 KUNNR
                //承运商名称 NAME2
                //抬头发货日期 （ETD）	BUDAT
                //------------------//

                //判断 抬头文本	HTEXT
                if (string.IsNullOrEmpty(mHeader.HTEXT))
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"抬头文本:{mHeader.HTEXT} 不正确";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
                */
                #endregion
                List<DETAIL> lstDetail = ksfPara.TaskElements.Element.DETAIL.Where(p => p.EBELN == mHeader.EBELN).ToList();
                if (lstDetail != null && lstDetail.Count != 0)
                {
                    Order mOrder = new Order() { Number = mHeader.EBELN, SapType = mHeader.BSART, Creater = "SAP", CreateTime = DateTime.Now, PlanCode40 = mHeader.HTEXT, Type = mHeader.BSART.Contains("R") ? 1 : 2 };

                    List<Order.Line> lines = new List<Order.Line>(); // Order.Line();
                    foreach (DETAIL mDetail in lstDetail)
                    {
                        Order.Line line = new Order.Line();

                        line.Material = -1;
                        line.PlanListRemark = mDetail.MATNR;
                        //数量
                        line.Quantity = Convert.ToDecimal(mDetail.MENGE);
                        //批号
                        line.Batch = mDetail.CHARG;

                        lines.Add(line);
                    }
                    mOrder.Lines = lines;
                    var result = await _orderService.Create(mOrder);
                    if (result.Failure == null)
                    {
                        //成功
                        ksfResult.STATUS = "0";
                        ksfResult.INFOTEXT = "通知单接收成功";
                        return XmlHelper.Serialize(ksfResult);
                    }
                    else
                    {
                        //失败
                        ksfResult.STATUS = "1";
                        ksfResult.INFOTEXT = result.Failure.ErrorMessage;
                        return XmlHelper.Serialize(ksfResult);
                    }
                }
                else
                {
                    ksfResult.STATUS = "1";
                    ksfResult.INFOTEXT = $"单据号码:{mHeader.EBELN} 没有对应的DETAIL";
                    sResult = XmlHelper.Serialize(ksfResult);
                    return sResult;
                }
            }

            //DETAIL
            //单据号码 EBELN
            //项目收货日期（ETA）	EINDT
            //项目  POSNR
            //无需判断 项目文本    LTEXT
            //无需判断 项目类别    PSTYV
            //物料  MATNR
            //数量  MENGE
            //单据单位    MEINS
            //空 单价  NETPR
            //工厂  WERKS
            //工厂名称    NAME1
            //库存地点    LGORT
            //库存地点描述  LGOBE
            //库存类型    INSMK
            //建议批号    CHARG


            return sResult;
        }

        // 这是一个接口（对 ERP 开放创建任务功能）
        public async Task<DemoResponseContract> CreateTask(DemoRequestContract request)
        {
            _logger.LogInformation("ERP 调用接口啦！");

            // 统一使用 IInterfaceService.Input 方法，重点构造 data 参数
            // 具体业务在 Domain.Interface 中，根据这里传入的 "ERP"、"CreateTask" 等参数集中处理
            // 这个方法会返回一个 Failure，可以按照协议的应答格式反馈给调用者（例如 ERP）
            await _interfaceService.Input(
                scope: "ERP",                                                               // 与谁的接口
                function: "CreateTask",                                                     // 接口的功能标识
                unique: Guid.NewGuid().ToString(),                                          // 关键字，便于关联后续接口，可能由调用者传来，也可能是自己生成，也可以为空
                message: JsonHelper.Serialize(request),                                     // 原始报文，可记可不记
                data: new Dictionary<string, string>() { ["Barcode"] = request.Barcode, },  // 接口数据，这个很重要
                async: true                                                                 // 同步还是异步（同步就是直接处理业务，并反馈结果；异步就是先缓存到数据库里，另外线程处理，无法立即反馈结果）
            );

            return new DemoResponseContract { Result = "OK" };
        }
    }
}
