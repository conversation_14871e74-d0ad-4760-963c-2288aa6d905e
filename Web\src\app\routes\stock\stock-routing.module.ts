import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { InboundComponent } from './inbound/inbound.component';
import { OutboundComponent } from './outbound/outbound.component';
import { PickingComponent } from './picking/picking.component';
import { CombineComponent } from './combine/combine.component';
import { PalletizeComponent } from './palletize/palletize.component';
import { DepalletizeComponent } from './depalletize/depalletize.component';
import { InventoryComponent } from './inventory/inventory.component';
import { BlockComponent } from './block/block.component';
import { PkPickingComponent} from "./pkpicking/pkpicking.component";

const routes: Routes = [
  {
    path: 'inbound',
    component: InboundComponent
  },
  {
    path: 'outbound',
    component: OutboundComponent
  },
  {
    path: 'picking',
    component: PickingComponent
  },
  {
    path: 'combine',
    component: CombineComponent
  },
  {
    path: 'palletize',
    component: PalletizeComponent
  },
  {
    path: 'depalletize',
    component: DepalletizeComponent
  },
  {
    path: 'block',
    component: BlockComponent
  },
  {
    path: "pkpicking",
    component: PkPickingComponent,
  },
  {
    path: 'inventory',
    component: InventoryComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StockRoutingModule { }
