﻿using Kean.Application.Command.Interfaces;
using Kean.Domain;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Hangfire;
using Kean.Infrastructure.Interface.Wcs.Entities;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Jobs
{
    /// <summary>
    /// WCS 申请处理作业
    /// </summary>
    public class WcsApplyJob(
        ILogger<WcsApplyJob> logger,        // 日志
        IDefaultDb database,                // 数据库连接
        IInterfaceService interfaceService, // 接口服务
        INotification notification          // 总线通知
    ) : IRecurringJob
    {
        /*
         * 实现 Kean.Infrastructure.Hangfire.IRecurringJob.Execute 方法
         */
        [DisallowConcurrentExecution]
        public async Task Execute()
        {
            // 遍历中间表 IO_CONTROL_APPLY
            var applies = await database.From<IO_CONTROL_APPLY>().Select();
            database.Flush();
            foreach (var item in applies)
            {
                // 如果存在一个目标设备为当前申请设备的未完成任务，则认为作业拥塞，后续周期再做处理
                if ((await database.From<IO_CONTROL>()
                    .Where(c => c.STOCK_BARCODE == item.STOCK_BARCODE && c.END_DEVICE_CODE == item.DEVICE_CODE)
                    .Single(c => new { Count = Function.Count(c.CONTROL_ID) })).Count > 0)
                {
                    database.Flush();
                    continue;
                }
                logger.LogInformation("轮询 IO_CONTROL_APPLY：ID【{Id}】，类型【{Type}】，设备【{Device}】，条码【{Barcode}】，参数【{Parameters}】", item.CONTROL_APPLY_ID, item.CONTROL_APPLY_TYPE, item.DEVICE_CODE, item.STOCK_BARCODE, item.CONTROL_APPLY_PARAMETER);
                // 接口处理
                await interfaceService.Input(
                    scope: "WCS",
                    function: "Trigger",
                    unique: item.CONTROL_APPLY_ID.ToString(),
                    message: $"{{\"Type\":{item.CONTROL_APPLY_TYPE},\"Warehouse\":\"{item.WAREHOUSE_CODE}\",\"Device\":\"{item.DEVICE_CODE}\",\"Barcode\":\"{item.STOCK_BARCODE}\",\"Parameters\":\"{item.CONTROL_APPLY_PARAMETER}\",\"Parameter01\":\"{item.CONTROL_APPLY_PARA01}\",\"Parameter02\":\"{item.CONTROL_APPLY_PARA02}\"}}",
                    data: new
                    {
                        Type = int.TryParse(item.CONTROL_APPLY_TYPE, out var i) ? i : 0,
                        Warehouse = item.WAREHOUSE_CODE,
                        Device = item.DEVICE_CODE,
                        Barcode = item.STOCK_BARCODE,
                        Parameter = item.CONTROL_APPLY_PARAMETER,
                        Parameter01 = item.CONTROL_APPLY_PARA01,
                        Parameter02 = item.CONTROL_APPLY_PARA02,
                        Timestamp = DateTime.TryParse(item.CREATE_TIME, out var dt) ? dt : DateTime.Now

                    },
                    async: false).ContinueWith(_ =>
                    {
                        notification.Clear();
                    });
            }
        }
    }
}
