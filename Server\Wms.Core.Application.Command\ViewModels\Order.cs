﻿using Kean.Infrastructure.Configuration;
using System;
using System.Collections.Generic;

namespace Kean.Application.Command.ViewModels
{
    /// <summary>
    /// 订单视图
    /// </summary>
    public sealed class Order
    {
        /// <summary>
        /// 标识
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Creater { get; set; }

        /// <summary>
        /// 制单时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        public string SapType { get; set; }

        public string PlanCode40 { get; set; }

        /// <summary>
        /// 订单行
        /// </summary>
        public IEnumerable<Line> Lines { get; set; }

        /// <summary>
        /// 订单行视图
        /// </summary>
        public sealed class Line : MaterialProperty
        {
            /// <summary>
            /// 标识
            /// </summary>
            public int Id { get; set; }

            /// <summary>
            /// 物资
            /// </summary>
            public int Material { get; set; }

            public string GoodsCode { get; set; }

            /// <summary>
            /// 数量
            /// </summary>
            public decimal Quantity { get; set; }

            /// <summary>
            /// 执行数量
            /// </summary>
            public decimal Executing { get; set; }

            /// <summary>
            /// 完成数量
            /// </summary>
            public decimal Finished { get; set; }

            /// <summary>
            /// 库存行关联
            /// </summary>
            public int? Stock { get; set; }

            /// <summary>
            /// 标记
            /// </summary>
            public bool PlanListFlag { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string PlanListRemark { get; set; }
            /// <summary>
            /// 仓库确认
            /// </summary>
            public string PlanListRepo { get; set; }

            /// <summary>
            /// 生产确认
            /// </summary>
            public string PlanListProd { get; set; }

            /// <summary>
            /// 最小库存时间
            /// </summary>
            public string MinStorageTime { get; set; }
            /// <summary>
            /// 最大库存时间
            /// </summary>
            public string MaxStorageTime { get; set; }
        }
    }

    public class ConfirmInfo
    {
        public int PlanId { get; set; }

        /// <summary>
        /// 订单详情编码
        /// </summary>
        public string PlanListId { get; set; }
        /// <summary>
        /// 操作者
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 仓库/生产确认
        /// </summary>
        public string Tag { get; set; }
    }
}
