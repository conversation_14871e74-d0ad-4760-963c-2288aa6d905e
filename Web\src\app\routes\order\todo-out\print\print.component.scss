.text{
  
}

.mat-dialog-title .mat-divider {
    margin: 16px -24px -16px -24px;
  }
  
  .mat-dialog-content {
    padding: 0;
  
    .mat-table {
      width: 100%;
      tr:last-child>td {
        border-bottom-style: none;
      }
  
      th,
      td {
        padding-right: 56px;
        white-space: nowrap;
  
        &.no-data {
          position: absolute;
          width: 90vw;
          height: 48px;
          padding: 0;
          text-align: center;
          line-height: 48px;
        }
      }
    }
  
  }
  
  .mat-dialog-actions {
    margin: 0 -24px -24px -24px;
  
    .mat-divider {
      margin: -8px 0 8px 0;
      width: 100%;
    }
  
    .mat-paginator {
      width: 100%;
    }
  }
  
  .print-doc{
  
    .table{
      /* border-collapse: collapse; */
      width: 100%; 
      margin-left: auto;
      margin-right: auto; /* 水平居中 */
  }
  
  .example-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px;
  }
   
  .example-item-content {
    text-decoration: none;
  }
  .title
  { 
    font-size: 30px;
    font-weight: bold;
    margin-top: 50px;
    margin-bottom: 20px;
    text-align: center;
  }
  th{
    padding-right: 56px;
    white-space: nowrap;
    text-align: center;
  }
  td {
    padding-right: 56px;
    white-space: nowrap;
    &.no-data {
      position: absolute;
      width: 90vw;
      height: 48px;
      padding: 0;
      text-align: center;
      line-height: 48px;
    }
  }
  }

  .auto-width-table {
    width: 90%;
    table-layout: auto; /* 允许列宽根据内容大小自动调整 */
    // border: 1px solid black;
    // border-collapse: collapse;
  }

  .auto-width-table th, .auto-width-table td {
    padding-right: 56px;
    white-space: nowrap;
    // border: 1px solid black; /* 添加内部的竖边框和横边框 */
    // text-align: center;
  }


  .table-container {
    display: flex;
    justify-content: center;  /* 水平居中 */
    width: 100%;
    padding: 20px;            /* 增加一些内边距，避免贴边 */
  }


  .page-break-after {
  page-break-after: always;
}


