﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    //[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public class ZALLSAP_UPLOAD_GOODSMOV_1
    {
        private string bSARTField;

        private string eBELNField;

        private string i_DATEField;

        private string i_TIMEField;

        private string lGPLAField;

        private string vTXTKField;

        private string hTEXTField;

        private string bUDATField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string BSART
        {
            get
            {
                return this.bSARTField;
            }
            set
            {
                this.bSARTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string EBELN
        {
            get
            {
                return this.eBELNField;
            }
            set
            {
                this.eBELNField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string I_DATE
        {
            get
            {
                return this.i_DATEField;
            }
            set
            {
                this.i_DATEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string I_TIME
        {
            get
            {
                return this.i_TIMEField;
            }
            set
            {
                this.i_TIMEField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LGPLA
        {
            get
            {
                return this.lGPLAField;
            }
            set
            {
                this.lGPLAField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string VTXTK
        {
            get
            {
                return this.vTXTKField;
            }
            set
            {
                this.vTXTKField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string HTEXT
        {
            get
            {
                return this.hTEXTField;
            }
            set
            {
                this.hTEXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string BUDAT
        {
            get
            {
                return this.bUDATField;
            }
            set
            {
                this.bUDATField = value;
            }
        }
    }
}
