import { AfterViewChecked, Component, ElementRef, Inject } from "@angular/core";
import { MAT_DIALOG_DATA } from "@angular/material/dialog";
import { HttpService } from "@app/core/services/http.service";
//import * as printJS from 'print-js';
import printJS from "@app/core/print.js";
import { DomSanitizer } from "@angular/platform-browser";
import { Console } from "console";

@Component({
  selector: 'app-print',
  templateUrl: './print.component.html',
  styleUrls: ['./print.component.scss']
})
export class PrintComponent implements AfterViewChecked {
  private _columns: any[];
  private _sort: string = "";
  private _pageIndex: number = 0;
  private _pageSize: number = 200;
  private _total: number = 0;
  private _rows: any[];
  private _loading: boolean;
  public url: any;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    private _data: any,
    private _httpService: HttpService,
    private sanitizer: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    private el: ElementRef
  ) {
    this._columns = [
      {
      id: "number",
      header: "routes.order.number",
    },
    {
      id: "creater",
      header: "routes.order.creater",
    },
    {
      id: "plancode40",
      header: "routes.order.plancode40",
    },
    {
      id: "plancode99",
      header: "routes.order.plancode99",
    },
    {
      id: "platform",
      header: "routes.order.platform",
    },
    {
      id: "state",
      header: "routes.order.state",
    },
    {
      id: "createTime",
      header: "routes.order.created",
    },
    {
      id: "code",
      header: "routes.material.code",
    },
    {
      id: "name",
      header: "routes.material.name",
    },
    {
      id: "model",
      header: "routes.material.model",
    },
    {
      id: "quantity",
      header: "routes.material.qty",
    },
    {
      id: "finished",
      header: "routes.order.finished",
    },
    {
      id: "executing",
      header: "routes.order.executing",
    },
    {
      id: "unit",
      header: "routes.material.unit",
    },
    {
      id: "truckInfo",
      header: "routes.material.truckInfo",
    },
    ];
    this._sort = "";
    this._pageIndex = 0;
    this._pageSize = 200;
    this._total = 0;
    this._loading = true;
    this.refresh();
  }
  ngAfterViewChecked(): void {
    this.formateSingle();
  }

  public get barcode() {
    return this._data.barcode;
  }

  public get lines() {
    return this._data.lines;
  }
  public get columns() {
    return this._columns;
  }
  public get sort() {
    return this._sort;
  }
  public get pageIndex() {
    return this._pageIndex;
  }

  public get pageSize() {
    return this._pageSize;
  }

  public get total() {
    return this._total;
  }

  public get rows() {
    return this._rows;
  }

  public get loading() {
    return this._loading;
  }


  public get data() {
    return this._data;
  }

  public list: any[] = [];
  public async refresh(e?: any): Promise<void> {
    let res: any = null;
    let lastPage: number = -1;
    while (this._pageIndex > lastPage) {
      lastPage > -1 && (this._pageIndex = lastPage);
      //191781
      res = await this._httpService.get(
        `orders/${this._data.id}/lines?sort=${this._sort}&offset=${this._pageSize * this._pageIndex
        }&limit=${this._pageSize}`
      );
      // res = await this._httpService.get(`orders/191781/lines?barcode=${this._data.barcode}&sort=${this._sort}&offset=${this._pageSize * this._pageIndex}&limit=${this._pageSize}`);
      //res.items=this._data.lines;
      lastPage = res
        ? Math.max(0, Math.ceil(res.total / this._pageSize) - 1)
        : Number.MAX_VALUE;
    }


    var input: any = {
      resource: null,
      mcode: null,
      mname: null,
      projectcode: null,
      model: null,
      sn: null,
      quantity: null,
      batch: null,
      supplier: null,
    };
    input.mcode = this._data.projectName;
    
    var qrCodeBite: any = await this._httpService.post(
      `orders/generateQrCode`,
     input
    );
    var urlS = "data:image/png;base64, " + qrCodeBite.item;
    this.url = this.sanitizer.bypassSecurityTrustResourceUrl(urlS);

    if (e) {
      if (e.pageIndex !== undefined) {
        this._pageIndex = e.pageIndex;
        this._pageSize = e.pageSize;
      } else if (e.active !== undefined) {
        this._sort = e.direction
          ? e.direction == "desc"
            ? `~${e.active}`
            : e.active
          : "";
      }
    }

    if (res) {
      this._loading = false;
      this._total = res.total;
      this._rows = res.items.map((item: any) => {
        // if (item && item.enabled == null) {
        //   if (item.qualityState != null && item.qualityState != "ok") {
        //     item.enabled = false;
        //   } else if (item.overdue) {
        //     item.enabled = false;
        //   } else {
        //     item.enabled = true;
        //   }
        //   item.cangku = 'routes.order.cangkulist.' + item.cangku;
        //   item.cangkuout = 'routes.order.cangkulist.' + item.cangkuout;
        // }
        return item;
      });
    }

    let chunkSize = 18;


    let chunks = [];

    for (let i = 0; i < this.rows.length; i += chunkSize) {
      chunks.push(this.rows.slice(i, i + chunkSize));
    }
    for (let index = 0; index < chunks.length; index++) {
      var listSingle = { page: index + 1, data: chunks[index] };
      this.list.push(listSingle);
    }

    this.list.forEach(element => {
      const listCount = chunkSize - element.data.length;
      for (let index = 0; index < listCount; index++) {

        element.data.push({ disable: true });
      }
    });
  }
  public formateSingle() {
    for (
      let index = 0;
      index < this.el.nativeElement.querySelectorAll(".text").length;
      index++
    ) {
      const element = this.el.nativeElement.querySelectorAll(".text")[index];
      // var marginValue = basePiex * index;
      if ((element.textContent.trim() == '')) {
        element.style.visibility = 'hidden';
      }
      console.log(element.textContent);

    }
  }
  public poin = async () => {
    printJS({
      printable: "printable-content", // 这里是需要打印内容的元素的 ID
      type: "html", // 指定打印内容的类型，可以是 'html', 'pdf', 'image' 等
      style:
        "@page { size: auto;  margin: 0mm; } @media print { body { margin: 5; padding: 5; } }", // 打印样式，可以自定义页面尺寸和边距等
      scanStyles: true,
      targetStyles: ["*"], // 继承样式
      repeatTableHeader: true,
    });
  };
}
