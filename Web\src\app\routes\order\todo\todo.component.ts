import {
  After<PERSON><PERSON>w<PERSON>ni<PERSON>,
  Component,
  On<PERSON><PERSON>roy,
  Template<PERSON>ef,
  ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { DialogService } from "@app/core/services/dialog.service";
import { EventService } from "@app/core/services/event.service";
import { HttpService } from "@app/core/services/http.service";
import { I18nService } from "@app/core/services/i18n.service";
import { RouterService } from "@app/core/services/router.service";
import { ToastService } from "@app/core/services/toast.service";
import { CrudComponent } from "@app/routes/.templates/crud/crud.component";
import { TodoAction } from "./todo.action";
import { TodoEditComponent } from "./edit/edit.component";
import { QRCodeComponent } from "@app/shared/components/qrcode/qrcode.component";
import { TargetComponent } from "./target/target.component";
import * as moment from "moment";

@Component({
  selector: "app-order-todo",
  templateUrl: "./todo.component.html",
  styleUrls: ["./todo.component.scss"],
  queries: {
    _orderTable: new ViewChild("$order"),
    _lineTable: new ViewChild("$line"),
    _typeColumn: new ViewChild("$type"),
    _stateColumn: new ViewChild("$state"),
    _timeColumn: new ViewChild("$time"),
  },
})
export class TodoComponent implements AfterViewInit, OnDestroy {
  private _loading: boolean;
  private _orderTable: CrudComponent;
  private _lineTable: CrudComponent;
  private _typeColumn: TemplateRef<any>;
  private _stateColumn: TemplateRef<any>;
  private _timeColumn: TemplateRef<any>;
  private _subscription: Subscription;
  private _init: boolean;
  private _type: number;
  private _types: { [id: number]: any };
  private _states: { [id: number]: any };
  private _order: any;
  private _tab: number;
  private _action: TodoAction;

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _dialogService: DialogService,
    private _eventService: EventService,
    private _httpService: HttpService,
    private _i18nService: I18nService,
    private _routerService: RouterService,
    private _toastService: ToastService
  ) {
    this._loading = false;
    this._tab = 0;
    this._subscription = this._activatedRoute.params.subscribe((data) => {
      this._type = data.type;
      if (this._init) {
        this.ngAfterViewInit();
      }
    });
    //订单状态对象，需要根据实际情况补齐
    this._states = {
      0: {
        id: 0,
        name: this._i18nService.translate("routes.order.states.exec"),
      },
      1: {
        id: 1,
        name: this._i18nService.translate("routes.order.states.finish"),
      },
    };
    this._eventService.on("refresh", this.refresh);
    this._eventService.once("destory", this.destory);
    this._action = new TodoAction(
      this,
      _dialogService,
      _httpService,
      _i18nService,
      _routerService,
      _toastService
    );
  }
public get pro() {
    return this._routerService.current.permission['pro'];
  }

public get rep() {
    return this._routerService.current.permission['rep'];
  }
  public get loading() {
    return this._loading;
  }
  ngAfterViewInit(): void {
    if (this._init) {
      this._tab = 0;
      this._orderTable.addable = this._types[this._type];
      this._orderTable.query = `type=${this._type}`;
      this._orderTable.refresh();
    } else {
      this._httpService.get("orders/types").then((res: any) => {
        this._types = res.items.reduce((obj: any, item: any) => {
          return (obj[item.id] = item), obj;
        }, {});
        let defination: any[] = [
          {
            id: "number",
            header: "routes.order.number",
            filter: { field: "text" },
          },
          {
            id: "type",
            header: "routes.order.type",
            template: this._typeColumn,
            // filter: { field: "select", range: Object.values(this._types) },
          },
          {
            id: "creater",
            header: "routes.order.creater",
            filter: { field: "text" },
          },
          /*{
            id: "plancode40",
            header: "routes.order.plancode40",
            filter: { field: "text" },
          },
          {
            id: "plancode99",
            header: "routes.order.plancode99",
            filter: { field: "text" },
          },
          {
            id: "platform",
            header: "routes.order.platform",
            filter: { field: "text" },
          },*/
          {
            id: "state",
            header: "routes.order.state",
            template: this._stateColumn,
            filter: { field: "select", range: Object.values(this._states) },
          },
          {
            id: "createTime",
            header: "routes.order.created",
            template: this._timeColumn,
            filter: { field: "date-range" },
          },
        ];
        let addable = true;
        // if (this._type) {
        //   defination.splice(1, 1);
        //   if (!this._types[this._type]) {
        //     addable = false;
        //   }
        // }
        this._orderTable.defination = defination;
        this._orderTable.addable = addable;
        this._orderTable.query = this._type && `type=${this._type}`;
        this._orderTable.api = "orders";
        this._orderTable.edit = this.add;
        defination = [
          {
            id: "code",
            header: "routes.material.code",
            filter: { field: "text" },
          },
          {
            id: "name",
            header: "routes.material.name",
          },
          {
            id: "model",
            header: "routes.material.model",
          },
          {
            id: "quantity",
            header: "routes.material.qty",
          },
          {
            id: "finished",
            header: "routes.order.finished",
          },
          {
            id: "executing",
            header: "routes.order.executing",
          },
          {
            id: "unit",
            header: "routes.material.unit",
          },
          {
            id: "batch",
            header: "routes.material.batch",
          },
          /*{
            id: "bill",
            header: "routes.material.bill",
          },
          {
            id: "supplier",
            header: "routes.material.supplier",
          },
          {
            id: "brand",
            header: "routes.material.brand",
          },*/
          /*
          {
            id: "adjustQuantity",
            header: "routes.material.adjustQuantity",
          },
          {
            id: "adjustReason",
            header: "routes.material.adjustReason",
          },*/
          {
            id: "clientCode",
            header: "routes.material.clientCode",
          },
          /*
          {
            id: "effectivePeriod",
            header: "routes.material.effectivePeriod",
          },
          {
            id: "expirationPeriod",
            header: "routes.material.expirationPeriod",
          },
          {
            id: "financialPostLatestTime",
            header: "routes.material.financialPostLatestTime",
          },
          {
            id: "financialPostQuantity",
            header: "routes.material.financialPostQuantity",
          },
          {
            id: "financialPostStatus",
            header: "routes.material.financialPostStatus",
          },
          {
            id: "adjustRfinancialPostTimeeason",
            header: "routes.material.financialPostTime",
          },
          {
            id: "financialPostUploadTime",
            header: "routes.material.financialPostUploadTime",
          },
          {
            id: "importTime",
            header: "routes.material.importTime",
          },*/
          {
            id: "productionLine",
            header: "routes.material.productionLine",
          },
          /*
          {
            id: "sapNo",
            header: "routes.material.sapNo",
          },
          {
            id: "transferType",
            header: "routes.material.transferType",
          },
          {
            id: "truckInfo",
            header: "routes.material.truckInfo",
          },*/
          {
            id: "warehouseCode",
            header: "routes.material.warehouseCode",
          },
          {
            id: "workGroup",
            header: "routes.material.workGroup",
          },
        ];
        this._lineTable.defination = defination;
        this._init = true;
      });
    }
  }

  ngOnDestroy(): void {
    this._subscription.unsubscribe();
    this._eventService.off("refresh", this.refresh);
    this._eventService.off("destory", this.destory);
  }

  public get type() {
    return this._type;
  }

  public get types() {
    return this._types;
  }
  public get states() {
    return this._states;
  }

  public get order() {
    return this._order;
  }

  public get tab() {
    return this._tab;
  }

  public set order(value: any) {
    this._order = value;
  }

  public set tab(value: number) {
    this._tab = value;
    if (value) {
      this._lineTable.loading = true;
      this._lineTable.api = `orders/${this._order.id}/lines`;
      this._lineTable.refresh();
    } else {
      setTimeout(() => this._lineTable.empty(), 500);
    }
  }

  private refresh = () => {
    this._orderTable.refresh();
    if (this._tab == 1) {
      this._lineTable.refresh();
    }
  };

  private destory = () => {
    this._routerService.reuse(
      this._type ? `order/todo/${this._type}` : "order/todo",
      false
    );
  };

  public add = async () => {
    if (
      await this._dialogService.open(TodoEditComponent, {
        data: { type: this._type },
        autoFocus: false,
        maxWidth: "90vw",
        maxHeight: "90vh",
        width: "90vw",
        height: "90vh",
      })
    ) {
      this._toastService.show(
        this._i18nService.translate("shared.notification.success")
      );
      this._orderTable.refresh();
    }
  };

  public action = async (order: any, action: string) => {
    if (await this._action.invoke(order, action)) {
      this._toastService.show(
        this._i18nService.translate("shared.notification.success")
      );
      await this._orderTable.refresh();
      if (this._tab === 1) {
        this._order = this._orderTable.rows.find(
          (item) => item.id === this._order.id
        );
        if (this._order) {
          this._lineTable.refresh();
        } else {
          this._tab = 0;
        }
      }
    }
  };
  public print = async (info: any) => {
    var resQuantity: any = await this._dialogService.prompt("", "", {
      title: "请输入数量",
      type: "text",
      required: true,
    });
    if (resQuantity) {
      var res: any = await this._dialogService.open(QRCodeComponent, {
        data: { infos: info.number + "|" + info.code + "|" + resQuantity },
      });
    }
  };
  public inboundLine = async (info: any) => {
    let res: any = await this._dialogService.open(TargetComponent);
    if (res) {
      const original = res.original;
      const quantity = res.quantity;
      this._loading = true;
      //生成管理任务
      var resTask = await this._httpService
        .post("tasks", {
          operation: "infeed",
          task: {
            warehouse: 1, //info.warehouseCode,
            barcode: moment().format("YYYYMMDDHHmmss"),
            original: parseInt(original, 10),
            destination: null,
            priority: 0,
            planId: info.id,
            planListId: info.line,
            manageListQuantity: quantity,
            //tag: "Stock",
            tag:"infeed",
            area: res.area,
            // manual: true,
          },
        })
        .catch((e) => {
          this._toastService.show(
            e.error?.errorMessage
              ? `${this._i18nService.translate(
                  `server.${e.error.errorMessage}`
                )} (${e.error.errorMessage})`
              : this._i18nService.translate("shared.notification.unknown")
          );
        });

      if (resTask !== undefined) {
        this._toastService.show(
          this._i18nService.translate("shared.notification.success")
        );
      }
      this._loading = false;
    }
  };
  public RoPconfirm = async (info: any, tag: string) => {
    var user: any = await this._httpService.get("users/current");
    if (
      await this._dialogService.confirm(
        this._i18nService.translate("shared.notification.confirm")
      )
    ) {
      const res =
        (await this._httpService
          .post(`orders/confirm`, {
            planId:info.id,
            planListId: info.line,
            userName: user.name,
            tag: tag,
          })
          .catch((e) => {
            switch (e.status) {
              case 410:
                this._toastService.show(
                  `${this._i18nService.translate(
                    `server.${e.error.errorMessage}`
                  )} (${e.error.errorMessage})`
                );
                break;
              case 422:
                this._toastService.show(
                  e.error?.errorMessage
                    ? `${this._i18nService.translate(
                        `server.${e.error.errorMessage}`
                      )} (${e.error.errorMessage})`
                    : this._i18nService.translate("shared.notification.fail")
                );
                break;
              default:
                break;
            }
          })) !== undefined;
      if (res) {
        this._toastService.show(
          this._i18nService.translate("shared.notification.success")
        );
      }
    }
  };
  public save = async () => {
    let tree = [];
    this._orderTable.selection.selected.forEach((item) => {
      tree.push(item.id);
    });
    let res = await this._httpService
      .get(`orders/hedan?planid=${tree}`)
      .catch((e) => {
        this._toastService.show(
          e.error?.errorMessage
            ? `${this._i18nService.translate(
                `server.${e.error.errorMessage}`
              )} (${e.error.errorMessage})`
            : this._i18nService.translate("shared.notification.unknown")
        );
      });
    if (res !== undefined) {
      this._toastService.show(
        this._i18nService.translate("shared.notification.success")
      );
      await this._orderTable.refresh();
    }
  };
}
