﻿using Kean.Domain.Shared;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Enums;
using Kean.Domain.Task.Repositories;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Domain.Task
{
    /// <summary>
    /// 共享服务
    /// </summary>
    public class SharedService(
        ICommandBus commandBus,         // 命令总线
        ITaskRepository taskRepository  // 任务仓库
    ) : ITaskService
    {
        /*
         * 实现 Kean.Domain.Shared.ITaskService.TaskList 方法
         */
        public Task<IEnumerable<JObject>> TaskList() =>
            taskRepository.GetTasks(null).ContinueWith(task => task.Result.Select(i => JObject.FromObject(i)));

        /*
         * 实现 Kean.Domain.Shared.ITaskService.TaskInfo 方法
         */
        public Task<JObject> TaskInfo(int id) =>
            taskRepository.GetTask(id).ContinueWith(task => task.Result == null ? null : JObject.FromObject(task.Result));

        /*
         * 实现 Kean.Domain.Shared.ITaskService.HasTask 方法
         */
        public Task<bool> HasTask(string barcode, string originalGroup = null, string destinationGroup = null) =>
            taskRepository.HasTask(barcode, originalGroup, destinationGroup);

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Exist 方法
         */
        public Task<bool> Exist(int id) =>
            taskRepository.IsExist(id);

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Execute 方法
         */
        public System.Threading.Tasks.Task Execute(int id, bool running, string message) =>
            commandBus.Execute(new ExecuteCommand { Id = id, Running = running, Message = message });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Cancel 方法
         */
        public System.Threading.Tasks.Task Cancel(int id) =>
            commandBus.Execute(new CancelCommand { Id = id });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Complete 方法
         */
        public System.Threading.Tasks.Task Complete(int id) =>
            commandBus.Execute(new CompleteCommand { Id = id });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Infeed 方法
         */
        public System.Threading.Tasks.Task Infeed(int warehouse, string barcode, int? spec, object original, object destination, int? priority, int @operator, string tag, int? previous, string remark) =>
            commandBus.Execute(new InfeedCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Spec = spec,
                Original = original,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark
            });
        /*
         * 实现 Kean.Domain.Shared.ITaskService.Infeed 方法
         */
        public System.Threading.Tasks.Task Infeed(int warehouse, string barcode, int? spec, object original, object destination, int? priority, int @operator, string tag, int? previous, string remark, int planId, int planListId, decimal managelistquantity) =>
            commandBus.Execute(new InfeedCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Spec = spec,
                Original = original,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark,
                PlanId = planId,
                PlanListId = planListId,
                ManageListQuantity = managelistquantity
            });
        /*
         * 实现 Kean.Domain.Shared.ITaskService.Infeed 方法
         */
        public System.Threading.Tasks.Task Infeed(int warehouse, string barcode, int? spec, object original, object destination, int? priority, int @operator, string tag, int? previous, string remark, int planId, decimal managelistquantity) =>
            commandBus.Execute(new InfeedCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Spec = spec,
                Original = original,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark,
                PlanId = planId,
                ManageListQuantity = managelistquantity
            });
        /*
         * 实现 Kean.Domain.Shared.ITaskService.Outfeed 方法
         */
        public System.Threading.Tasks.Task Outfeed(int warehouse, string barcode, object destination, int? priority, int @operator, string tag, int? previous, string remark) =>
            commandBus.Execute(new OutfeedCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark
            });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Transfer 方法
         */
        public System.Threading.Tasks.Task Transfer(int warehouse, string barcode, object destination, int? priority, int @operator, string tag, int? previous, string remark) =>
            commandBus.Execute(new TransferCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark
            });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Convey 方法
         */
        public System.Threading.Tasks.Task Convey(int warehouse, string barcode, int? spec, object original, object destination, int? priority, int @operator, string tag, int? previous, string remark) =>
            commandBus.Execute(new ConveyCommand
            {
                Warehouse = warehouse,
                Barcode = barcode,
                Spec = spec,
                Original = original,
                Destination = destination,
                Priority = priority,
                Operator = @operator,
                Tag = tag,
                Previous = previous,
                Remark = remark
            });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Convey 方法
         */
        public System.Threading.Tasks.Task Trigger(int type, int warehouse, string device, JObject parameter, DateTime timestamp, int? timeout) =>
            commandBus.Execute(new TriggerCommand
            {
                State = TriggerState.Initializing,
                Type = type,
                Warehouse = warehouse,
                Device = device,
                Parameter = parameter,
                Timestamp = timestamp,
                Timeout = timeout
            });
        public System.Threading.Tasks.Task Trigger(int type, int warehouse, string device, JObject parameter, string parameter01, string parameter02, DateTime timestamp, int? timeout) =>
         commandBus.Execute(new TriggerCommand
         {
             State = TriggerState.Initializing,
             Type = type,
             Warehouse = warehouse,
             Device = device,
             Parameter = parameter,
             Parameter01 = parameter01,
             Parameter02 = parameter02,
             Timestamp = timestamp,
             Timeout = timeout
         });

        /*
         * 实现 Kean.Domain.Shared.ITaskService.Createmanage 方法
         */
        public System.Threading.Tasks.Task Createmanage(string singlestock) =>
            commandBus.Execute(new AutoDownloadCommand
            {
                Stockbarcode = singlestock
            });
    }
}
