﻿using Kean.Domain.Shared;
using Kean.Domain.Task.Events;
using Kean.Infrastructure.Utilities;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 任务完成命令成功时，移动库存
    /// </summary>
    [EventHandlerIndex(10)]
    public sealed class CompleteSuccessEventHandler_Stock(
        IStockService stockService,  // 仓储域共享服务
        IInterfaceService interfaceService
    ) : EventHandler<CompleteSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(CompleteSuccessEvent @event, CancellationToken cancellationToken)
        {
            //weijn test
            //await interfaceService.SyncOutput("erp", "方法名", "", "parameter日志用", "xml参数");
            await interfaceService.SyncOutput("sap", "UploadTask", null, "parameter日志用", "xml参数");
            //tag stock时生成库存
            if (@event.Tag != null && @event.Tag.Equals("Stock"))
            {
                JArray StockLines = JArray.Parse(@event.Remark);

                await stockService.Inbound(@event.Barcode, @event.Destination, StockLines, true, 1, "Stock", true, null);
            }
            else if (@event.Tag != null && (@event.Tag.Contains("PlanLockStockOut") || @event.Tag.Contains("PlanLockFeedOut")))
            {

                JArray StockTemplate = await stockService.GetStockLine(@event.Barcode);
                JArray StockLines = new JArray();
                JArray Locks;
                JObject StockLine;
                if (StockTemplate.Count > 0)
                {
                    Locks = await stockService.GetLockIdByStorageListId((int)StockTemplate[0]["Id"]);
                    if (Locks.Count == 1)
                    {
                        StockLines = StockTemplate;
                        StockLines[0]["Quantity"] = -StockLines[0].Value<decimal>("Quantity");
                        StockLines[0]["Lock"] = Locks[0]["LOCK_ID"];
                        StockLines[0]["Order"] = Locks[0]["PLAN_LIST_ID"];
                    }
                    else
                    {
                        foreach (var @lock in Locks)
                        {
                            StockLine = StockTemplate[0].DeepClone().ToObject<JObject>();
                            StockLine["Quantity"] = -@lock.Value<decimal>("LOCK_QUANTITY");
                            StockLine["Lock"] = @lock["LOCK_ID"];
                            StockLine["PlanListId"] = @lock["PLAN_LIST_ID"];
                            StockLine["Order"] = @lock["PLAN_LIST_ID"];
                            StockLines.Add(StockLine);
                        }


                    }
                    //其他情况 移库存
                    await stockService.Relocate(
                        @event.Type.ToString(),
                        @event.Barcode,
                        @event.Spec,
                        @event.Original,
                        @event.Destination,
                        @event.Operator,
                        @event.Tag,
                        (@event.Created, @event.Timestamp),
                        @event.Remark);
                    if (@event.Tag.Contains("PlanLockStockOut"))
                    {
                        await stockService.Outbound(@event.Barcode, StockLines, 1, null, true, null);
                    }
                }
                else
                {
                    return;
                }
            }
            else
            {
                //其他情况 移库存
                await stockService.Relocate(
                    @event.Type.ToString(),
                    @event.Barcode,
                    @event.Spec,
                    @event.Original,
                    @event.Destination,
                    @event.Operator,
                    @event.Tag,
                    (@event.Created, @event.Timestamp),
                    @event.Remark);
            }
        }
    }
}
