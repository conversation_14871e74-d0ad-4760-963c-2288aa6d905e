﻿using Kean.Domain.Task.Enums;
using Newtonsoft.Json.Linq;
using System;

namespace Kean.Domain.Task.Models
{
    /// <summary>
    /// 触发
    /// </summary>
    public class Trigger
    {
        /// <summary>
        /// 标识
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public int Warehouse { get; set; }

        /// <summary>
        /// 设备
        /// </summary>
        public string Device { get; set; }

        public string Parameter01 { get; set; }

        public string Parameter02 { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public JObject Parameter { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int? Timeout { get; set; }

        /// <summary>
        /// 执行记数
        /// </summary>
        public int Executed { get; set; }

        /// <summary>
        /// 结果
        /// </summary>
        public TriggerResult Result { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
