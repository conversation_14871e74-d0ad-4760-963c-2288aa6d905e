using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Wms.Core.Domain.VisualIntegration.Interfaces;
using Wms.Core.Domain.VisualIntegration.Models;

namespace Wms.Core.Domain.VisualIntegration.Services
{
    /// <summary>
    /// 视觉集成主服务实现类
    /// 作为系统的主要协调器，协调各个组件的工作
    /// </summary>
    public class VisualIntegrationService : IVisualIntegrationService, IDisposable
    {
        private readonly IConnectionManager _connectionManager;
        private readonly IHeartbeatMonitor _heartbeatMonitor;
        private readonly IStationManager _stationManager;
        private readonly IMessageProcessor _messageProcessor;
        private readonly IRecognitionResultProcessor _recognitionResultProcessor;
        private readonly IInboundConfirmationProcessor _inboundConfirmationProcessor;
        private readonly IOutboundProcessor _outboundProcessor;
        private readonly IErrorHandler _errorHandler;
        private readonly IVisualIntegrationConfigManager _configManager;
        private readonly ILogger<VisualIntegrationService> _logger;

        private ServiceStatus _currentStatus = ServiceStatus.Stopped;
        private readonly SemaphoreSlim _statusLock = new(1, 1);
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public VisualIntegrationService(
            IConnectionManager connectionManager,
            IHeartbeatMonitor heartbeatMonitor,
            IStationManager stationManager,
            IMessageProcessor messageProcessor,
            IRecognitionResultProcessor recognitionResultProcessor,
            IInboundConfirmationProcessor inboundConfirmationProcessor,
            IOutboundProcessor outboundProcessor,
            IErrorHandler errorHandler,
            IVisualIntegrationConfigManager configManager,
            ILogger<VisualIntegrationService> logger)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _heartbeatMonitor = heartbeatMonitor ?? throw new ArgumentNullException(nameof(heartbeatMonitor));
            _stationManager = stationManager ?? throw new ArgumentNullException(nameof(stationManager));
            _messageProcessor = messageProcessor ?? throw new ArgumentNullException(nameof(messageProcessor));
            _recognitionResultProcessor = recognitionResultProcessor ?? throw new ArgumentNullException(nameof(recognitionResultProcessor));
            _inboundConfirmationProcessor = inboundConfirmationProcessor ?? throw new ArgumentNullException(nameof(inboundConfirmationProcessor));
            _outboundProcessor = outboundProcessor ?? throw new ArgumentNullException(nameof(outboundProcessor));
            _errorHandler = errorHandler ?? throw new ArgumentNullException(nameof(errorHandler));
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 订阅组件事件
            SubscribeToComponentEvents();
        }

        #region 事件定义

        /// <summary>
        /// 服务状态变更事件
        /// </summary>
        public event EventHandler<ServiceStatusChangedEventArgs>? ServiceStatusChanged;

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        public event EventHandler<ConnectionStatusEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// 心跳状态变更事件
        /// </summary>
        public event EventHandler<HeartbeatEventArgs>? HeartbeatReceived;

        /// <summary>
        /// 系统异常事件
        /// </summary>
        public event EventHandler<SystemErrorEventArgs>? SystemError;

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动视觉集成服务
        /// 需求1.1, 1.2: 建立TCP连接并启动各个组件
        /// </summary>
        public async Task<bool> StartAsync()
        {
            await _statusLock.WaitAsync();
            try
            {
                if (_currentStatus != ServiceStatus.Stopped)
                {
                    _logger.LogWarning("服务已在运行或正在启动，当前状态: {Status}", _currentStatus);
                    return _currentStatus == ServiceStatus.Running;
                }

                await ChangeStatusAsync(ServiceStatus.Starting, "开始启动服务");
                _cancellationTokenSource = new CancellationTokenSource();

                try
                {
                    // 1. 加载配置
                    var config = await _configManager.GetConfigAsync();
                    _logger.LogInformation("配置加载完成");

                    // 2. 建立TCP连接
                    _logger.LogInformation("正在连接到视觉识别系统: {Address}:{Port}", 
                        config.Connection.PrimaryServerAddress, config.Connection.Port);
                    
                    var connected = await _connectionManager.ConnectAsync(
                        config.Connection.PrimaryServerAddress, 
                        config.Connection.Port);

                    if (!connected)
                    {
                        throw new InvalidOperationException("无法连接到视觉识别系统");
                    }

                    _logger.LogInformation("TCP连接建立成功");

                    // 3. 发送 ASRSOnline 上线消息（先于心跳）
                    try
                    {
                        var onlineReq = new Models.Messages.ASRSOnline.ASRSOnlineRequest
                        {
                            FuncSeqNo = _messageProcessor.GenerateSequenceNumber()
                        };
                        _logger.LogInformation("发送 ASRSOnline 上线请求: FuncSeqNo={FuncSeqNo}", onlineReq.FuncSeqNo);
                        var onlineResp = await _messageProcessor
                            .SendMessageAsync<Models.Messages.ASRSOnline.ASRSOnlineRequest, Models.Messages.ASRSOnline.ASRSOnlineResponse>(
                                onlineReq, 3000);

                        var result = onlineResp?.Content?.Result ?? false;
                        _logger.LogInformation("收到 ASRSOnline 响应: FuncSeqNo={FuncSeqNo}, Result={Result}", onlineReq.FuncSeqNo, result);
                        if (!result)
                        {
                            throw new InvalidOperationException("ASRSOnline 上线失败，服务端返回失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "ASRSOnline 上线过程失败");
                        throw; // 交由外层捕获，进入启动失败逻辑
                    }

                    // 4. 启动心跳监控（仅在上线成功后）
                    _logger.LogInformation("启动心跳监控");
                    await _heartbeatMonitor.StartMonitoringAsync();

                    // 5. 注册消息处理器
                    RegisterMessageHandlers();
                    _logger.LogInformation("消息处理器注册完成");

                    await ChangeStatusAsync(ServiceStatus.Running, "服务启动成功");
                    _logger.LogInformation("视觉集成服务启动成功");

                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "服务启动失败");
                    await ChangeStatusAsync(ServiceStatus.Error, $"启动失败: {ex.Message}");
                    
                    // 清理资源
                    await CleanupAsync();
                    
                    OnSystemError(ex, "服务启动", false);
                    return false;
                }
            }
            finally
            {
                _statusLock.Release();
            }
        }

        /// <summary>
        /// 停止视觉集成服务
        /// </summary>
        public async Task StopAsync()
        {
            await _statusLock.WaitAsync();
            try
            {
                if (_currentStatus == ServiceStatus.Stopped || _currentStatus == ServiceStatus.Stopping)
                {
                    _logger.LogInformation("服务已停止或正在停止，当前状态: {Status}", _currentStatus);
                    return;
                }

                await ChangeStatusAsync(ServiceStatus.Stopping, "开始停止服务");
                _logger.LogInformation("正在停止视觉集成服务");

                try
                {
                    // 取消所有操作
                    _cancellationTokenSource?.Cancel();

                    // 停止心跳监控
                    await _heartbeatMonitor.StopMonitoringAsync();
                    _logger.LogInformation("心跳监控已停止");

                    // 断开连接
                    await _connectionManager.DisconnectAsync();
                    _logger.LogInformation("TCP连接已断开");

                    await ChangeStatusAsync(ServiceStatus.Stopped, "服务停止成功");
                    _logger.LogInformation("视觉集成服务停止成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "服务停止过程中发生异常");
                    await ChangeStatusAsync(ServiceStatus.Error, $"停止异常: {ex.Message}");
                    OnSystemError(ex, "服务停止", false);
                }
                finally
                {
                    await CleanupAsync();
                }
            }
            finally
            {
                _statusLock.Release();
            }
        }

        /// <summary>
        /// 获取服务运行状态
        /// </summary>
        public async Task<ServiceStatus> GetServiceStatusAsync()
        {
            await _statusLock.WaitAsync();
            try
            {
                return _currentStatus;
            }
            finally
            {
                _statusLock.Release();
            }
        }

        /// <summary>
        /// 获取连接状态
        /// </summary>
        public async Task<bool> IsConnectedAsync()
        {
            try
            {
                return await _connectionManager.IsConnectedAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查连接状态时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取所有站位状态
        /// </summary>
        public async Task<StationStatus[]> GetAllStationStatusAsync()
        {
            try
            {
                return await _heartbeatMonitor.GetStationStatusAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取站位状态时发生异常");
                OnSystemError(ex, "获取站位状态", false);
                return Array.Empty<StationStatus>();
            }
        }

        /// <summary>
        /// 处理棧板到达事件
        /// 需求3.1: 当棧板到达站位时，系统应该检查该站位的识别设备状态
        /// </summary>
        public async Task<bool> HandlePalletArrivalAsync(string stationNo)
        {
            try
            {
                _logger.LogInformation("处理棧板到达事件，站位: {StationNo}", stationNo);

                // 检查服务状态
                if (_currentStatus != ServiceStatus.Running)
                {
                    _logger.LogWarning("服务未运行，无法处理棧板到达事件，当前状态: {Status}", _currentStatus);
                    return false;
                }

                // 检查棧板是否到达
                //var palletArrived = await _stationManager.IsPalletArrivedAsync(stationNo);
                //if (!palletArrived)
                //{
                //    _logger.LogWarning("棧板未到达站位: {StationNo}", stationNo);
                //    return false;
                //}

                // 检查识别设备状态
                var recognitionEnabled = await _stationManager.IsRecognitionEnabledAsync(stationNo);
                if (recognitionEnabled)
                {
                    // 需求3.2: 识别设备状态为ON时，发送识别请求
                    _logger.LogInformation("识别设备已启用，发送识别请求，站位: {StationNo}", stationNo);
                    await _stationManager.RequestRecognitionAsync(stationNo);
                }
                else
                {
                    // 需求3.3: 识别设备状态为OFF时，使用传统入库方式
                    _logger.LogInformation("识别设备未启用，使用传统入库方式，站位: {StationNo}", stationNo);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理棧板到达事件时发生异常，站位: {StationNo}", stationNo);
                OnSystemError(ex, $"处理棧板到达事件-{stationNo}", false);
                return false;
            }
        }

        /// <summary>
        /// 处理识别结果
        /// 需求4.1: 当视觉识别完成时，系统应该处理识别结果
        /// </summary>
        public async Task<bool> HandleRecognitionResultAsync(RecognitionResult result, string funcSeqNo)
        {
            try
            {
                _logger.LogInformation("处理识别结果，站位: {StationNo}, 序列号: {FuncSeqNo}", 
                    result.StationNo, funcSeqNo);

                // 检查服务状态
                if (_currentStatus != ServiceStatus.Running)
                {
                    _logger.LogWarning("服务未运行，无法处理识别结果，当前状态: {Status}", _currentStatus);
                    return false;
                }

                // 处理识别结果
                var success = await _recognitionResultProcessor.ProcessRecognitionResultAsync(result, funcSeqNo);
                
                if (success)
                {
                    _logger.LogInformation("识别结果处理成功，站位: {StationNo}", result.StationNo);
                }
                else
                {
                    _logger.LogWarning("识别结果处理失败，站位: {StationNo}", result.StationNo);
                    
                    // 处理识别错误
                    await _recognitionResultProcessor.HandleRecognitionErrorAsync(result);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理识别结果时发生异常，站位: {StationNo}", result.StationNo);
                OnSystemError(ex, $"处理识别结果-{result.StationNo}", false);
                return false;
            }
        }

        /// <summary>
        /// 处理入库确认
        /// 需求5.1: 当棧板成功入库到库格时，发送入库确认信息
        /// </summary>
        public async Task<bool> HandleInboundConfirmationAsync(InboundInfo inboundInfo)
        {
            try
            {
                _logger.LogInformation("处理入库确认，作业序号: {OperationNo}, 库格: {LocationNo}", 
                    inboundInfo.OperationNo, inboundInfo.LocationNo);

                // 检查服务状态
                if (_currentStatus != ServiceStatus.Running)
                {
                    _logger.LogWarning("服务未运行，无法处理入库确认，当前状态: {Status}", _currentStatus);
                    return false;
                }

                // 发送入库确认信息
                var success = await _inboundConfirmationProcessor.SendInboundConfirmationAsync(inboundInfo);
                
                if (success)
                {
                    _logger.LogInformation("入库确认发送成功，作业序号: {OperationNo}", inboundInfo.OperationNo);
                }
                else
                {
                    _logger.LogWarning("入库确认发送失败，作业序号: {OperationNo}", inboundInfo.OperationNo);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理入库确认时发生异常，作业序号: {OperationNo}", inboundInfo.OperationNo);
                OnSystemError(ex, $"处理入库确认-{inboundInfo.OperationNo}", false);
                return false;
            }
        }

        /// <summary>
        /// 处理销售出库
        /// 需求6.1: 当执行销售出库作业时，发送出库信息
        /// </summary>
        public async Task<bool> HandleSalesOutboundAsync(OutboundInfo outboundInfo)
        {
            try
            {
                _logger.LogInformation("处理销售出库，作业序号: {OperationNo}, 客户: {CustomerId}", 
                    outboundInfo.OperationNo, outboundInfo.CustomerId);

                // 检查服务状态
                if (_currentStatus != ServiceStatus.Running)
                {
                    _logger.LogWarning("服务未运行，无法处理销售出库，当前状态: {Status}", _currentStatus);
                    return false;
                }

                // 处理销售出库
                var success = await _outboundProcessor.ProcessSalesOutboundAsync(outboundInfo);
                
                if (success)
                {
                    _logger.LogInformation("销售出库处理成功，作业序号: {OperationNo}", outboundInfo.OperationNo);
                }
                else
                {
                    _logger.LogWarning("销售出库处理失败，作业序号: {OperationNo}", outboundInfo.OperationNo);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理销售出库时发生异常，作业序号: {OperationNo}", outboundInfo.OperationNo);
                OnSystemError(ex, $"处理销售出库-{outboundInfo.OperationNo}", false);
                return false;
            }
        }

        /// <summary>
        /// 重新连接到视觉识别系统
        /// </summary>
        public async Task<bool> ReconnectAsync()
        {
            try
            {
                _logger.LogInformation("开始重新连接到视觉识别系统");

                // 停止心跳监控
                await _heartbeatMonitor.StopMonitoringAsync();

                // 断开当前连接
                await _connectionManager.DisconnectAsync();

                // 重新建立连接
                var config = await _configManager.GetConfigAsync();
                var connected = await _connectionManager.ConnectAsync(
                    config.Connection.PrimaryServerAddress, 
                    config.Connection.Port);

                if (connected)
                {
                    // 重新启动心跳监控
                    await _heartbeatMonitor.StartMonitoringAsync();
                    _logger.LogInformation("重新连接成功");
                    return true;
                }
                else
                {
                    _logger.LogError("重新连接失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新连接时发生异常");
                OnSystemError(ex, "重新连接", false);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 订阅组件事件
        /// </summary>
        private void SubscribeToComponentEvents()
        {
            // 连接状态变更事件
            _connectionManager.ConnectionStatusChanged += OnConnectionStatusChanged;

            // 心跳接收事件
            _heartbeatMonitor.HeartbeatReceived += OnHeartbeatReceived;
        }

        /// <summary>
        /// 注册消息处理器
        /// </summary>
        private void RegisterMessageHandlers()
        {
            // 这里可以注册各种消息类型的处理器
            // 具体的消息处理逻辑由各个专门的处理器负责
            _logger.LogInformation("消息处理器注册完成");
        }

        /// <summary>
        /// 改变服务状态
        /// </summary>
        private async Task ChangeStatusAsync(ServiceStatus newStatus, string reason)
        {
            var previousStatus = _currentStatus;
            _currentStatus = newStatus;

            _logger.LogInformation("服务状态变更: {Previous} -> {Current}, 原因: {Reason}", 
                previousStatus, newStatus, reason);

            // 触发状态变更事件
            ServiceStatusChanged?.Invoke(this, new ServiceStatusChangedEventArgs
            {
                PreviousStatus = previousStatus,
                CurrentStatus = newStatus,
                Reason = reason,
                Timestamp = DateTime.Now
            });

            await Task.CompletedTask;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理资源时发生异常");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 连接状态变更事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, ConnectionStatusEventArgs e)
        {
            _logger.LogInformation("连接状态变更: {IsConnected}, 服务器: {ServerAddress}:{Port}", 
                e.IsConnected, e.ServerAddress, e.Port);

            // 转发事件
            ConnectionStatusChanged?.Invoke(this, e);

            // 如果连接断开且服务正在运行，尝试重连
            if (!e.IsConnected && _currentStatus == ServiceStatus.Running)
            {
                _logger.LogWarning("连接断开，将尝试重新连接");
                
                // 异步重连，不阻塞当前线程
                Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(5000); // 等待5秒后重连
                        await ReconnectAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "自动重连失败");
                        OnSystemError(ex, "自动重连", false);
                    }
                });
            }
        }

        /// <summary>
        /// 心跳接收事件处理
        /// </summary>
        private void OnHeartbeatReceived(object? sender, HeartbeatEventArgs e)
        {
            _logger.LogDebug("收到心跳响应，站位数量: {Count}, 成功: {IsSuccess}", 
                e.StationStatuses.Length, e.IsSuccess);

            // 转发事件
            HeartbeatReceived?.Invoke(this, e);
        }

        /// <summary>
        /// 系统异常事件处理
        /// </summary>
        private void OnSystemError(Exception exception, string context, bool isFatal)
        {
            var errorArgs = new SystemErrorEventArgs
            {
                Exception = exception,
                Context = context,
                IsFatal = isFatal,
                Timestamp = DateTime.Now
            };

            SystemError?.Invoke(this, errorArgs);

            // 如果是致命错误，停止服务
            if (isFatal && _currentStatus == ServiceStatus.Running)
            {
                _logger.LogCritical(exception, "发生致命错误，停止服务，上下文: {Context}", context);
                
                Task.Run(async () =>
                {
                    try
                    {
                        await StopAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "停止服务时发生异常");
                    }
                });
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 停止服务
                    StopAsync().GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放资源时停止服务发生异常");
                }

                // 取消订阅事件
                _connectionManager.ConnectionStatusChanged -= OnConnectionStatusChanged;
                _heartbeatMonitor.HeartbeatReceived -= OnHeartbeatReceived;

                // 释放信号量
                _statusLock?.Dispose();
                _cancellationTokenSource?.Dispose();

                _disposed = true;
            }
        }

        #endregion
    }
}