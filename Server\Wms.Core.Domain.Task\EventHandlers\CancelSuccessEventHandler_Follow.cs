﻿using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Repositories;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 任务取消命令成功时，处理后续
    /// </summary>
    public sealed class CancelSuccessEventHandler_Follow(
        ICommandBus commandBus,         // 命令总线
        ITaskRepository taskRepository  // 任务仓库
    ) : EventHandler<CancelSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(CancelSuccessEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Tag == "PlanLockStockOut")
            {

            }
            else { await taskRepository.UpdateOrderQuantity(@event.Remark, false); }

            foreach (var item in await taskRepository.GetFollows(@event.Id))
            {

                await commandBus.Execute(new CancelCommand { Id = item.Id }, cancellationToken);
            }
        }
    }
}
