﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    public class MaterialContract
    {
        [DataMember] public string MTART { get; set; }  //物料类型                 
        [DataMember] public string MTBEZ { get; set; }  //物料类型描述               
        [DataMember] public string MATNR { get; set; }  //物料号                  
        [DataMember] public string MAKTX { get; set; }  //物料描述                 
        [DataMember] public string NORMT { get; set; }  //物料简称                 
        [DataMember] public string LVORM { get; set; }  //删除标识                 
        [DataMember] public string MEINS { get; set; }  //基本计量单位               
        [DataMember] public string MSEHT { get; set; }  //基本计量单位描述             
        [DataMember] public string MEINS1 { get; set; }  //附加计量单位1              
        [DataMember] public string MSEHT1 { get; set; }  //附加计量单位描述1            
        [DataMember] public string RATE1 { get; set; }  //转换关系1                
        [DataMember] public string MEINS2 { get; set; }  //附加计量单位2              
        [DataMember] public string MSEHT2 { get; set; }  //附加计量单位描述2            
        [DataMember] public string RATE2 { get; set; }  //转换关系2                
        [DataMember] public string MEINS3 { get; set; }  //附加计量单位3              
        [DataMember] public string MSEHT3 { get; set; }  //附加计量单位描述3            
        [DataMember] public string RATE3 { get; set; }  //转换关系3                
        [DataMember] public string MEINS4 { get; set; }  //附加计量单位4              
        [DataMember] public string MSEHT4 { get; set; }  //附加计量单位描述4            
        [DataMember] public string RATE4 { get; set; }  //转换关系4                
        [DataMember] public string MEINS5 { get; set; }  //附加计量单位5              
        [DataMember] public string MSEHT5 { get; set; }  //附加计量单位描述5            
        [DataMember] public string RATE5 { get; set; }  //转换关系5                
        [DataMember] public string MATKL { get; set; }  //物料组                  
        [DataMember] public string WGBEZ { get; set; }  //物料组描述                
        [DataMember] public string MSTAE { get; set; }  //跨工厂物料状态              
        [DataMember] public string LABOR { get; set; }  //口味码                  
        [DataMember] public string LABTXT { get; set; }  //口味码的描述               
        [DataMember] public string PRDHA { get; set; }  //产品层次                 
        [DataMember] public string ZEINR { get; set; }  //税收分类编码               
        [DataMember] public string BRGEW { get; set; }  //毛重/运费计算分摊系数          
        [DataMember] public string GEWEI { get; set; }  //重量单位                 
        [DataMember] public string NTGEW { get; set; }  //净重                   
        [DataMember] public string VOLUM { get; set; }  //业务量                  
        [DataMember] public string GROES { get; set; }  //大小/量纲                
        [DataMember] public string EAN11 { get; set; }  //国际商品编码（欧洲商品编码/通用产品代码）
        [DataMember] public string BREIT { get; set; }  //宽度                   
        [DataMember] public string HOEHE { get; set; }  //高度                   
        [DataMember] public string LAENG { get; set; }  //长度                   
        [DataMember] public string MEABM { get; set; }  //长度/宽度/高度的尺寸单位        
        [DataMember] public string MHDRZ { get; set; }  //最短剩余货架寿命-工厂数据/存储1    
        [DataMember] public string MHDHB { get; set; }  //总货架寿命                
        [DataMember] public string IPRKZ { get; set; }  //货架寿命到期日的期间标识         
        [DataMember] public string TAXKM { get; set; }  //物料的税分类-销售：销售组织数据1    
        [DataMember] public string VTEXT { get; set; }  //税分类描述                
        [DataMember] public string MVGR1 { get; set; }  //同价汇总码-销售：销售组织数据2     
        [DataMember] public string MVGR2 { get; set; }  //正常箱折算系数              
        [DataMember] public string MVGR4 { get; set; }  //物料规格                 
        [DataMember] public string STPRS { get; set; }  //标准价格                 
        [DataMember] public string PEINH { get; set; }  //价格单位                 
        [DataMember] public string GLTS { get; set; }  //绿灯天数                 
        [DataMember] public string YLTS { get; set; }  //黄灯天数                 
        [DataMember] public string RLTS { get; set; }  //红灯天数                 
        [DataMember] public string ZDWHSZ { get; set; }  //业务量                  
        [DataMember] public string BKLAS { get; set; }  //评估分类                 
        [DataMember] public string VBAMG { get; set; }  //基准数量 

    }
}
