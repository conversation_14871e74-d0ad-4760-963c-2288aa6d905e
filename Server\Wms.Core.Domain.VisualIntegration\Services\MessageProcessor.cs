using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Wms.Core.Domain.VisualIntegration.Interfaces;
using Wms.Core.Domain.VisualIntegration.Models.Messages;

namespace Wms.Core.Domain.VisualIntegration.Services
{
    /// <summary>
    /// 消息处理器，负责消息的发送、接收和序列号管理
    /// </summary>
    public class MessageProcessor : IMessageProcessor
    {
        private readonly ILogger<MessageProcessor> _logger;
        private readonly IConnectionManager _connectionManager;
        private readonly IMessageFrameProcessor _messageFrameProcessor;
        private readonly IDataTransformer _dataTransformer;

        private readonly ConcurrentDictionary<string, TaskCompletionSource<string>> _pendingResponses;
        private readonly ConcurrentDictionary<string, Func<BaseMessage, string, Task<bool>>> _messageHandlers;

        private int _sequenceNumber = 0;
        private const int MaxSequenceNumber = 99999999;
        private readonly SemaphoreSlim _sequenceLock = new(1, 1);
        private readonly SemaphoreSlim _sendLock = new(1, 1);

        // 接收循环相关
        private Task? _receiveLoopTask;
        private CancellationTokenSource? _receiveCts;
        private readonly SemaphoreSlim _receiveStartLock = new(1, 1);

        private const int DefaultRetryCount = 2;
        private const int DefaultTimeoutMs = 3000;

        /// <summary>
        /// 消息接收事件
        /// </summary>
        public event EventHandler<MessageReceivedEventArgs>? MessageReceived;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="connectionManager">连接管理器</param>
        /// <param name="messageFrameProcessor">消息帧处理器</param>
        /// <param name="dataTransformer">数据转换器</param>
        public MessageProcessor(
            ILogger<MessageProcessor> logger,
            IConnectionManager connectionManager,
            IMessageFrameProcessor messageFrameProcessor,
            IDataTransformer dataTransformer)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _messageFrameProcessor = messageFrameProcessor ?? throw new ArgumentNullException(nameof(messageFrameProcessor));
            _dataTransformer = dataTransformer ?? throw new ArgumentNullException(nameof(dataTransformer));

            _pendingResponses = new ConcurrentDictionary<string, TaskCompletionSource<string>>();
            _messageHandlers = new ConcurrentDictionary<string, Func<BaseMessage, string, Task<bool>>>();

            // 订阅消息帧接收事件
            _messageFrameProcessor.MessageFrameReceived += OnMessageFrameReceived;

            // 订阅连接状态，连接建立后启动接收循环
            _connectionManager.ConnectionStatusChanged += async (s, e) =>
            {
                if (e.IsConnected)
                {
                    await EnsureReceiveLoopStartedAsync();
                }
                else
                {
                    StopReceiveLoop();
                }
            };
        }

        /// <summary>
        /// 异步发送消息并等待响应
        /// </summary>
        /// <typeparam name="TRequest">请求消息类型</typeparam>
        /// <typeparam name="TResponse">响应消息类型</typeparam>
        /// <param name="request">请求消息</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>响应消息</returns>
        public async Task<TResponse> SendMessageAsync<TRequest, TResponse>(TRequest request, int timeoutMs = DefaultTimeoutMs)
            where TRequest : BaseMessage
            where TResponse : BaseMessage
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (timeoutMs <= 0)
                throw new ArgumentException("超时时间必须大于0", nameof(timeoutMs));

            // 验证消息
            if (!_dataTransformer.ValidateMessage(request))
                throw new ArgumentException("消息验证失败", nameof(request));

            // 确保已连接
            if (!await _connectionManager.IsConnectedAsync())
                throw new InvalidOperationException("未连接到服务器");

            // 如果没有设置序列号，则生成一个
            if (string.IsNullOrEmpty(request.FuncSeqNo))
                request.FuncSeqNo = GenerateSequenceNumber();

            string funcSeqNo = request.FuncSeqNo;

            // 创建等待响应的任务源
            var responseTaskSource = new TaskCompletionSource<string>();
            _pendingResponses[funcSeqNo] = responseTaskSource;

            try
            {
                // 发送消息，最多重试DefaultRetryCount次
                int retryCount = 0;
                bool sent = false;

                while (!sent && retryCount <= DefaultRetryCount)
                {
                    try
                    {
                        await _sendLock.WaitAsync();
                        try
                        {
                            // 序列化消息
                            string jsonMessage = _dataTransformer.SerializeMessage(request);

                            // 封装消息帧
                            byte[] messageFrame = _messageFrameProcessor.FrameMessage(jsonMessage);

                            // 获取网络流并发送
                            var stream = _connectionManager.GetStream();
                            await stream.WriteAsync(messageFrame, 0, messageFrame.Length);
                            await stream.FlushAsync();

                            _logger.LogDebug("消息已发送: FuncID={FuncID}, FuncSeqNo={FuncSeqNo}, 尝试次数={RetryCount}",
                                request.FuncID, funcSeqNo, retryCount + 1);

                            sent = true;
                        }
                        finally
                        {
                            _sendLock.Release();
                        }
                    }
                    catch (Exception ex)
                    {
                        retryCount++;

                        if (retryCount > DefaultRetryCount)
                        {
                            _logger.LogError(ex, "发送消息失败，已达到最大重试次数: FuncID={FuncID}, FuncSeqNo={FuncSeqNo}",
                                request.FuncID, funcSeqNo);
                            throw new TimeoutException($"发送消息失败，已达到最大重试次数: {ex.Message}", ex);
                        }

                        _logger.LogWarning(ex, "发送消息失败，准备重试 ({RetryCount}/{MaxRetries}): FuncID={FuncID}, FuncSeqNo={FuncSeqNo}",
                            retryCount, DefaultRetryCount, request.FuncID, funcSeqNo);

                        // 等待一段时间后重试
                        await Task.Delay(500);
                    }
                }

                // 等待响应，带超时
                using var timeoutCts = new CancellationTokenSource(timeoutMs);
                var responseTask = responseTaskSource.Task;

                var completedTask = await Task.WhenAny(responseTask, Task.Delay(timeoutMs, timeoutCts.Token));

                if (completedTask != responseTask)
                {
                    // 超时，移除等待的响应
                    _pendingResponses.TryRemove(funcSeqNo, out _);
                    throw new TimeoutException($"等待响应超时: FuncID={request.FuncID}, FuncSeqNo={funcSeqNo}");
                }

                // 获取响应JSON
                string responseJson = await responseTask;

                // 反序列化响应
                var response = _dataTransformer.DeserializeMessage<TResponse>(responseJson);

                // 验证响应
                if (!_dataTransformer.ValidateMessage(response))
                    throw new InvalidOperationException($"响应消息验证失败: {responseJson}");

                // 验证序列号匹配
                if (response.FuncSeqNo != funcSeqNo)
                {
                    _logger.LogWarning("响应序列号不匹配: 预期={Expected}, 实际={Actual}",
                        funcSeqNo, response.FuncSeqNo);
                }

                return response;
            }
            finally
            {
                // 确保移除等待的响应
                _pendingResponses.TryRemove(funcSeqNo, out _);
            }
        }

        /// <summary>
        /// 异步发送消息，不等待响应
        /// </summary>
        /// <typeparam name="TRequest">请求消息类型</typeparam>
        /// <param name="request">请求消息</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendMessageWithoutResponseAsync<TRequest>(TRequest request)
            where TRequest : BaseMessage
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            // 验证消息
            if (!_dataTransformer.ValidateMessage(request))
                throw new ArgumentException("消息验证失败", nameof(request));

            // 确保已连接
            if (!await _connectionManager.IsConnectedAsync())
                throw new InvalidOperationException("未连接到服务器");

            // 如果没有设置序列号，则生成一个
            if (string.IsNullOrEmpty(request.FuncSeqNo))
                request.FuncSeqNo = GenerateSequenceNumber();

            try
            {
                await _sendLock.WaitAsync();
                try
                {
                    // 序列化消息
                    string jsonMessage = _dataTransformer.SerializeMessage(request);

                    // 封装消息帧
                    byte[] messageFrame = _messageFrameProcessor.FrameMessage(jsonMessage);

                    // 获取网络流并发送
                    var stream = _connectionManager.GetStream();
                    await stream.WriteAsync(messageFrame, 0, messageFrame.Length);
                    await stream.FlushAsync();

                    _logger.LogDebug("消息已发送(无需响应): FuncID={FuncID}, FuncSeqNo={FuncSeqNo}",
                        request.FuncID, request.FuncSeqNo);

                    return true;
                }
                finally
                {
                    _sendLock.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败: FuncID={FuncID}, FuncSeqNo={FuncSeqNo}",
                    request.FuncID, request.FuncSeqNo);
                return false;
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        /// <param name="jsonMessage">JSON格式的消息</param>
        /// <returns>处理任务</returns>
        public async Task ProcessIncomingMessageAsync(string jsonMessage)
        {
            if (string.IsNullOrWhiteSpace(jsonMessage))
            {
                _logger.LogWarning("收到空消息");
                return;
            }

            try
            {
                // 尝试解析基础消息以获取FuncID和FuncSeqNo
                var baseMessage = _dataTransformer.DeserializeMessage<BaseMessage>(jsonMessage);

                if (string.IsNullOrEmpty(baseMessage.FuncID))
                {
                    _logger.LogWarning("收到的消息没有FuncID: {JsonMessage}", jsonMessage);
                    return;
                }

                string funcId = baseMessage.FuncID;
                string funcSeqNo = baseMessage.FuncSeqNo ?? string.Empty;

                _logger.LogDebug("收到消息: FuncID={FuncID}, FuncSeqNo={FuncSeqNo}", funcId, funcSeqNo);

                // 创建事件参数
                var eventArgs = new MessageReceivedEventArgs
                {
                    FuncId = funcId,
                    FuncSeqNo = funcSeqNo,
                    JsonMessage = jsonMessage,
                    Message = baseMessage,
                    Handled = false
                };

                // 检查是否有等待此序列号的响应
                if (!string.IsNullOrEmpty(funcSeqNo) && _pendingResponses.TryGetValue(funcSeqNo, out var taskSource))
                {
                    // 设置响应结果
                    taskSource.TrySetResult(jsonMessage);
                    eventArgs.Handled = true;
                }

                // 检查是否有注册的消息处理器
                if (_messageHandlers.TryGetValue(funcId, out var handler))
                {
                    try
                    {
                        bool handled = await handler(baseMessage, jsonMessage);
                        eventArgs.Handled = eventArgs.Handled || handled;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "消息处理器异常: FuncID={FuncID}, FuncSeqNo={FuncSeqNo}",
                            funcId, funcSeqNo);
                    }
                }

                // 触发消息接收事件
                OnMessageReceived(eventArgs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理接收消息时发生异常: {JsonMessage}", jsonMessage);
            }
        }

        /// <summary>
        /// 生成新的消息序列号
        /// </summary>
        /// <returns>序列号字符串（1-99999999）</returns>
        public string GenerateSequenceNumber()
        {
            _sequenceLock.Wait();
            try
            {
                _sequenceNumber = (_sequenceNumber % MaxSequenceNumber) + 1;
                return _sequenceNumber.ToString();
            }
            finally
            {
                _sequenceLock.Release();
            }
        }

        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="funcId">功能ID</param>
        /// <param name="handler">消息处理委托</param>
        public void RegisterMessageHandler<TMessage>(string funcId, Func<TMessage, string, Task<bool>> handler)
            where TMessage : BaseMessage
        {
            if (string.IsNullOrWhiteSpace(funcId))
                throw new ArgumentException("功能ID不能为空", nameof(funcId));

            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            // 包装处理器以处理类型转换
            Func<BaseMessage, string, Task<bool>> wrapperHandler = async (baseMessage, jsonMessage) =>
            {
                try
                {
                    // 反序列化为具体类型
                    var message = _dataTransformer.DeserializeMessage<TMessage>(jsonMessage);

                    // 调用原始处理器，传递序列号而不是完整JSON
                    return await handler(message, baseMessage.FuncSeqNo ?? string.Empty);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "消息处理器执行异常: FuncID={FuncID}, JSON={JsonMessage}", funcId, jsonMessage);
                    return false;
                }
            };

            _messageHandlers[funcId] = wrapperHandler;
            _logger.LogInformation("已注册消息处理器: FuncID={FuncID}, MessageType={MessageType}",
                funcId, typeof(TMessage).Name);
        }

        /// <summary>
        /// 处理接收到的消息帧
        /// </summary>
        private void OnMessageFrameReceived(object? sender, MessageFrameEventArgs e)
        {
            if (!e.IsValid || string.IsNullOrEmpty(e.JsonMessage))
            {
                _logger.LogWarning("收到无效的消息帧");
                return;
            }

            // 异步处理消息
            _ = Task.Run(async () =>
            {
                try
                {
                    await ProcessIncomingMessageAsync(e.JsonMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理消息帧时发生异常");
                }
            });
        }

        /// <summary>
        /// 确保接收循环已启动
        /// </summary>
        private async Task EnsureReceiveLoopStartedAsync()
        {
            await _receiveStartLock.WaitAsync();
            try
            {
                if (_receiveLoopTask != null && !_receiveLoopTask.IsCompleted)
                    return;

                _receiveCts?.Cancel();
                _receiveCts?.Dispose();
                _receiveCts = new CancellationTokenSource();

                _logger.LogInformation("启动网络接收循环");
                _receiveLoopTask = Task.Run(() => ReceiveLoopAsync(_receiveCts.Token));
            }
            finally
            {
                _receiveStartLock.Release();
            }
        }

        /// <summary>
        /// 停止接收循环
        /// </summary>
        private void StopReceiveLoop()
        {
            try
            {
                _receiveCts?.Cancel();
                _receiveCts?.Dispose();
                _receiveCts = null;
                _receiveLoopTask = null;
                _logger.LogInformation("已停止网络接收循环");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止网络接收循环时发生异常");
            }
        }

        /// <summary>
        /// 接收循环：读取 NetworkStream，按 STX/ETX 切帧，分发 JSON
        /// </summary>
        private async Task ReceiveLoopAsync(CancellationToken ct)
        {
            byte[] buffer = new byte[8192];
            var stream = _connectionManager.GetStream();
            var leftover = new System.IO.MemoryStream();

            while (!ct.IsCancellationRequested)
            {
                try
                {
                    if (stream == null)
                    {
                        await Task.Delay(200, ct);
                        continue;
                    }

                    // 读取数据
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, ct);
                    if (bytesRead == 0)
                    {
                        // 连接关闭
                        _logger.LogWarning("接收循环检测到远端关闭连接");
                        break;
                    }

                    // 合并上次残留 + 本次读取
                    leftover.Write(buffer, 0, bytesRead);
                    var data = leftover.ToArray();

                    int start = Array.IndexOf(data, MessageFrameProcessor.STX);
                    while (start >= 0)
                    {
                        int end = Array.IndexOf(data, MessageFrameProcessor.ETX, start + 1);
                        if (end < 0)
                        {
                            // 不完整帧，保留残留
                            break;
                        }

                        int frameLen = end - start + 1; // 含 STX/ETX
                        var frameBytes = new byte[frameLen];
                        Array.Copy(data, start, frameBytes, 0, frameLen);

                        try
                        {
                            var json = _messageFrameProcessor.ExtractMessage(frameBytes);
                            _logger.LogDebug("收到并解析消息帧: {Json}", json);
                            await ProcessIncomingMessageAsync(json);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "解析消息帧失败");
                        }

                        // 截断已消费部分，继续找下一帧
                        int nextSearchPos = end + 1;
                        if (nextSearchPos >= data.Length)
                        {
                            data = Array.Empty<byte>();
                            break;
                        }
                        var remaining = new byte[data.Length - nextSearchPos];
                        Array.Copy(data, nextSearchPos, remaining, 0, remaining.Length);
                        data = remaining;
                        start = Array.IndexOf(data, MessageFrameProcessor.STX);
                    }

                    // 更新 leftover 为未完整帧部分
                    leftover.Dispose();
                    leftover = new System.IO.MemoryStream();
                    if (data.Length > 0)
                    {
                        leftover.Write(data, 0, data.Length);
                    }
                }
                catch (OperationCanceledException) when (ct.IsCancellationRequested)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "接收循环异常");
                    await Task.Delay(200, ct);
                }
            }

            leftover.Dispose();
            _logger.LogInformation("网络接收循环退出");
        }

        /// <summary>
        /// 触发消息接收事件
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        protected virtual void OnMessageReceived(MessageReceivedEventArgs eventArgs)
        {
            MessageReceived?.Invoke(this, eventArgs);
        }
    }
}