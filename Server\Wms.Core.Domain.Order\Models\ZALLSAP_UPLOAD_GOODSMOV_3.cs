﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    //[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public class ZALLSAP_UPLOAD_GOODSMOV_3
    {
        private string sTATUSField;

        private string iNFOTEXTField;

        private string mBLNRField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string STATUS
        {
            get
            {
                return this.sTATUSField;
            }
            set
            {
                this.sTATUSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string INFOTEXT
        {
            get
            {
                return this.iNFOTEXTField;
            }
            set
            {
                this.iNFOTEXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
    }
}
