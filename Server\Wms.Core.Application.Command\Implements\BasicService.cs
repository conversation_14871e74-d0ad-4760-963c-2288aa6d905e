using AutoMapper;
using Domain_KSF.Models;
using Kean.Application.Command.Interfaces;
using Kean.Application.Command.ViewModels;
using Kean.Domain;
using Kean.Domain.Basic.Commands;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Application.Command.Implements
{
    /// <summary>
    /// 基础信息命令服务实现
    /// </summary>
    public sealed class BasicService(
        IMapper mapper,             // 模型映射
        ICommandBus commandBus,     // 命令总线
        INotification notification  // 总线通知
    ) : IBasicService
    {
        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.CreateRole 方法
         */
        public async Task<(int Id, Failure Failure)> CreateRole(Role role)
        {
            var command = mapper.Map<CreateRoleCommand>(role);
            await commandBus.Execute(command);
            return (command.Id, notification.FirstOrDefault());
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.ModifyRole 方法
         */
        public async Task<(bool Success, Failure Failure)> ModifyRole(Role role)
        {
            await commandBus.Execute(mapper.Map<ModifyRoleCommand>(role));
            var failure = notification.FirstOrDefault();
            return (failure == null, failure);
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.DeleteRole 方法
         */
        public async Task<IEnumerable<int>> DeleteRole(IEnumerable<int> id)
        {
            var command = new DeleteRoleCommand { Id = id };
            await commandBus.Execute(command);
            return command.Id;
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.SetRoleMenuPermission 方法
         */
        public async Task<(bool Success, Failure Failure)> SetRoleMenuPermission(int id, IEnumerable<int> permission)
        {
            await commandBus.Execute(new SetMenuPermissionCommand
            {
                Id = id,
                Permission = permission
            });
            var failure = notification.FirstOrDefault();
            return (failure == null, failure);
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.CreateUser 方法
         */
        public async Task<(int Id, Failure Failure)> CreateUser(User user)
        {
            var command = mapper.Map<CreateUserCommand>(user);
            await commandBus.Execute(command);
            return (command.Id, notification.FirstOrDefault());
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.ModifyUser 方法
         */
        public async Task<(bool Success, Failure Failure)> ModifyUser(User user)
        {
            await commandBus.Execute(mapper.Map<ModifyUserCommand>(user));
            var failure = notification.FirstOrDefault();
            return (failure == null, failure);
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.DeleteUser 方法
         */
        public async Task<IEnumerable<int>> DeleteUser(IEnumerable<int> id)
        {
            var command = new DeleteUserCommand { Id = id };
            await commandBus.Execute(command);
            return command.Id;
        }

        /*
         * 实现 Kean.Application.Command.Interfaces.IBasicService.ResetPassword 方法
         */
        public async Task<(bool Success, Failure Failure)> ResetPassword(int id)
        {
            await commandBus.Execute(new ResetPasswordCommand { Id = id });
            var failure = notification.FirstOrDefault();
            return (failure == null, failure);
        }

        public async Task<(int Id, Failure Failure)> CreateWarehouse(Warehouse warehouse)
        {
            try
            {
                var command = mapper.Map<CreateWarehouseCommand>(warehouse);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(bool Success, Failure Failure)> ModifyWarehouse(Warehouse warehouse)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyWarehouseCommand>(warehouse));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(int Id, Failure Failure)> CreateArea(Area area)
        {
            try
            {
                var command = mapper.Map<CreateAreaCommand>(area);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(bool Success, Failure Failure)> ModifyArea(Area area)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyAreaCommand>(area));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(int Id, Failure Failure)> CreateClientCate(ClientCate clientCate)
        {
            try
            {
                var command = mapper.Map<CreateClientCateCommand>(clientCate);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(bool Success, Failure Failure)> ModifyClientInfo(ClientInfo clientInfo)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyClientInfoCommand>(clientInfo));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeleteArea(IEnumerable<int> id)
        {
            throw new System.NotImplementedException();
        }

        public async Task<(bool Success, Failure Failure)> ModifyClientCate(ClientCate clientCate)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyClientCateCommand>(clientCate));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeleteClientCate(IEnumerable<int> id)
        {
            throw new System.NotImplementedException();
        }

        public async Task<(int Id, Failure Failure)> CreateClientInfo(ClientInfo clientInfo)
        {
            try
            {
                var command = mapper.Map<CreateClientInfoCommand>(clientInfo);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeleteClientInfo(IEnumerable<int> id)
        {
            try
            {
                var command = new DeleteClientInfoCommand { Id = id };
                await commandBus.Execute(command);
                return command.Id;
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(int Id, Failure Failure)> CreatePlatform(Platform platform)
        {
            try
            {
                var command = mapper.Map<CreatePlatformCommand>(platform);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(bool Success, Failure Failure)> ModifyPlatform(Platform platform)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyPlatformCommand>(platform));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeletePlatform(IEnumerable<int> id)
        {
            try
            {
                var command = new DeletePlatformCommand { Id = id };
                await commandBus.Execute(command);
                return command.Id;
            }
            catch (System.Exception)
            {

                throw;
            }
        }
        public async Task<(int Id, Failure Failure)> CreateApply(Apply apply)
        {
            try
            {
                var command = mapper.Map<CreateApplyCommand>(apply);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeleteApply(IEnumerable<int> id)
        {
            try
            {
                var command = new DeleteApplyCommand { Id = id };
                await commandBus.Execute(command);
                return command.Id;
            }
            catch (System.Exception)
            {

                throw;
            }
        }



        public async Task<(bool Success, Failure Failure)> ModifyApply(Apply apply)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyApplyCommand>(apply));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(int Id, Failure Failure)> CreateControl(Control control)
        {
            try
            {
                var command = mapper.Map<CreateControlCommand>(control);
                await commandBus.Execute(command);
                return (command.Id, notification.FirstOrDefault());
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<(bool Success, Failure Failure)> ModifyControl(Control control)
        {
            try
            {
                await commandBus.Execute(mapper.Map<ModifyControlCommand>(control));
                var failure = notification.FirstOrDefault();
                return (failure == null, failure);
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IEnumerable<int>> DeleteControl(IEnumerable<int> id)
        {
            try
            {
                var command = new DeleteControlCommand { Id = id };
                await commandBus.Execute(command);
                return command.Id;
            }
            catch (System.Exception)
            {

                throw;
            }
        }
    }

    
}
