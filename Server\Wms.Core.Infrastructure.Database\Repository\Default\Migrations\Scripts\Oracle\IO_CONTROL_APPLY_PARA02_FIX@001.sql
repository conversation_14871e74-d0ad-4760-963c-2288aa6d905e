-- 修复 IO_CONTROL_APPLY 表中 CONTROL_APPLY_PARA02 字段长度限制问题
-- 问题：字段长度为 varchar2(50)，无法存储视觉识别系统返回的JSON数据
-- 解决：将字段类型改为 nvarchar2(500)，支持更长的数据存储

DECLARE
    v_count NUMBER;
BEGIN
    -- 检查字段是否存在并且长度不足
    SELECT COUNT(*)
    INTO v_count
    FROM USER_TAB_COLUMNS
    WHERE TABLE_NAME = 'IO_CONTROL_APPLY'
    AND COLUMN_NAME = 'CONTROL_APPLY_PARA02'
    AND DATA_LENGTH = 50;
    
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('正在修改 IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段长度...');
        
        -- 修改字段类型和长度
        EXECUTE IMMEDIATE 'ALTER TABLE IO_CONTROL_APPLY MODIFY CONTROL_APPLY_PARA02 nvarchar2(500)';
        
        DBMS_OUTPUT.PUT_LINE('IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段已成功修改为 nvarchar2(500)');
    ELSE
        DBMS_OUTPUT.PUT_LINE('IO_CONTROL_APPLY.CONTROL_APPLY_PARA02 字段已经是正确的类型和长度，无需修改');
    END IF;
    
    -- 验证修改结果
    FOR rec IN (
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            NULLABLE
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = 'IO_CONTROL_APPLY'
        AND COLUMN_NAME = 'CONTROL_APPLY_PARA02'
    ) LOOP
        DBMS_OUTPUT.PUT_LINE('字段信息: ' || rec.COLUMN_NAME || ' ' || rec.DATA_TYPE || '(' || rec.DATA_LENGTH || ') ' || rec.NULLABLE);
    END LOOP;
END;
/
