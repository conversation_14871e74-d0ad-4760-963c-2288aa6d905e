﻿using AutoMapper;
using Kean.Domain.Task.Commands;
using Kean.Domain.Task.Events;
using Kean.Domain.Task.Repositories;
using System;
using System.Reflection;
using System.Threading;

namespace Kean.Domain.Task.EventHandlers
{
    /// <summary>
    /// 任务完成命令成功时，处理后续
    /// </summary>
    [EventHandlerIndex(20)]
    public sealed class CompleteSuccessEventHandler_Follow(
        IMapper mapper,                             // 模型映射
        ICommandBus commandBus,                     // 命令总线
        INotification notification,                 // 总线通知
        ITaskRepository taskRepository,             // 任务仓库
        IWarehouseRepository warehouseRepository    // 库房仓库
    ) : EventHandler<CompleteSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async System.Threading.Tasks.Task Handle(CompleteSuccessEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Type.ToString().ToLower() == "infeed")
            {
                await taskRepository.UpdateOrderQuantity(@event.Remark, true);
            }
            foreach (var item in await taskRepository.GetFollows(@event.Id))
            {
                await taskRepository.DeleteTask(item.Id);
                await warehouseRepository.ReleaseCell(item.Original, item.Destination);
                item.Previous = null;
                var command = mapper.Map(item, typeof(Models.Task), Assembly.GetExecutingAssembly().GetType($"{typeof(CompleteCommand).Namespace}.{item.Type}Command")) as ICommand;
                await commandBus.Execute(command, cancellationToken);
                if (notification.Count == 0)
                {
                    await taskRepository.UpdatePrevious(item.Id, Convert.ToInt32(command.GetType().GetProperty("Id").GetValue(command)));
                }
            }
        }
    }
}
