﻿/*
 * 这是一个例子，描述了对 WMS 调用外部接口的具体实现
 * 所有对与 WMS 调用接口的具体操作，都建议在这个项目中做
 */

using System.ServiceModel;
using System.Threading.Tasks;

namespace Kean.Infrastructure.Interface
{
    // 假设调用目标是 ERP。注意：Kean.Infrastructure.Interface.DependencyInjection 中定义了此类的依赖注入
    public partial class ErpClient : ClientBase<IDemoService>, Domain.Interface.RemoteClients.IErpClient
    {
        // 根据实际情况注入
        public ErpClient() : base(new BasicHttpBinding(), new EndpointAddress("http://localhost:5000/soap/public"))
        { 
        }

        // 接口调用的技术实现
        public async Task CompleteTask2(string uid)
        {
            // 调用 WebService
            var response = await Channel.CompleteTask(uid);
        }
    }

    // 契约
    [ServiceContract]
    public interface IDemoService
    {
        [OperationContract(Action = "http://tempuri.org/updateMaterial", ReplyAction = "*")]
        [XmlSerializerFormat(SupportFaults = true)]
        Task<string> CompleteTask(string uid);
    }
}
