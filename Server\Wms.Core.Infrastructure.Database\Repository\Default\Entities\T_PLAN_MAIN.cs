﻿using System;

namespace Kean.Infrastructure.Database.Repository.Default.Entities
{
    public class T_PLAN_MAIN : IEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Identifier(true)]
        public int PLAN_ID { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int PLAN_TYPE { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string PLAN_CODE { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string PLAN_CREATER { get; set; }

        /// <summary>
        /// 制单时间
        /// </summary>
        public DateTime? PLAN_CREATE_TIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string PLAN_REMARK { get; set; }
        public string TRUCK_INFO { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int PLAN_STATUS { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CREATE_TIME { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UPDATE_TIME { get; set; }


        /// <summary>
        /// 40单号
        /// </summary>
        public string PLAN_CODE_40 { get; set; }

        /// <summary>
        /// 99单号
        /// </summary>
        public string PLAN_CODE_99 { get; set; }

        /// <summary>
        /// 月台
        /// </summary>
        public string PLAN_PLATFORM { get; set; }

        public string PLAN_SAP_TYPE { get; set; }

        public string PLAN_SAP_TYPE_NAME { get; set; }

    }
}
