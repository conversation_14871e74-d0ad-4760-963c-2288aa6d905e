﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    public class Order
    {
        /// <summary>
        /// 标识
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Creater { get; set; }

        /// <summary>
        /// 制单时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        public string TruckInfo { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int State { get; set; }

        public string SapType { get; set; }

        /// <summary>
        /// 订单行
        /// </summary>
        public IEnumerable<OrderLine> Lines { get; set; }
    }
}
