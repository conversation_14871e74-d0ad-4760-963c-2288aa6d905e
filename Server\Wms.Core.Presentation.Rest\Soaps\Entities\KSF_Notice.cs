﻿/*
 * 这是一个例子：表示创建任务接口中，返回类型的定义
 */

using Kean.Infrastructure.NoSql.Redis;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Kean.Presentation.Rest.Soaps.Entities
{
    [XmlRoot("KSF_Notice")]
    public class KSF_Notice
    {
        public API_Message API_Message { get; set; } = new API_Message();

        
        [XmlElement("Elements")]
        public TaskElements TaskElements { get; set; } = new TaskElements();
        
    }
}
