﻿using Kean.Domain.Shared;
using Kean.Domain.Stock.Events;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Kean.Domain.Stock.EventHandlers
{
    /// <summary>
    /// 入库成功时，处理关联订单
    /// </summary>
    public sealed class InboundSuccessEventHandler_Order(
        ITaskService taskService,
        IOrderService orderService  // 订单域共享服务
    ) : EventHandler<InboundSuccessEvent>
    {
        /// <summary>
        /// 处理程序
        /// </summary>
        public override async Task Handle(InboundSuccessEvent @event, CancellationToken cancellationToken)
        {
            if (@event.Tag == "infeed")
            {
                await taskService.Infeed(
                    warehouse: 1,
                    barcode: @event.Barcode,
                    spec: null, // 如果需要接检尺信息，则从 data.Value<string>("Parameter") 中取
                    original: (int)@event.Cell,
                    destination: null,
                    priority: null,
                    @operator: -1,
                    tag: null,//$"Stock",//1:创建库存 
                    previous: null,
                    remark: null,//manageRemark,
                    0,
                    (int)@event.Lines.First().Order,
                    @event.Lines.First().Quantity
                    //Convert.ToInt32(data.Value<string>("Parameters").Split("|")[0])
                    );//120|S|20250606|01|1515|0001|69001|1 -- 数量 | 工厂别 | 批次 | 产线别 | 生产时间 | 箱流水号 | 69码 | 识别码
                var lines = @event.Lines.Where(l => l.Order.HasValue);
                if (lines.Any())
                {
                    await orderService.Progress(lines
                        .GroupBy(l => l.Order.Value)
                        .ToDictionary(l => l.Key, l => l.Sum(l => l.Quantity)),
                        true);
                }
            }
            else
            {
                var lines = @event.Lines.Where(l => l.Order.HasValue);
                if (lines.Any())
                {
                    await orderService.Progress(lines
                        .GroupBy(l => l.Order.Value)
                        .ToDictionary(l => l.Key, l => l.Sum(l => l.Quantity)),
                        false);
                }
            }
        }
    }
}
