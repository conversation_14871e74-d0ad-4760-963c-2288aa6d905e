﻿using System.Collections.Generic;
using System;

namespace Kean.Domain.Order.Models
{
    /// <summary>
    /// 订单实例
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    //[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://Microsoft.LobServices.Sap/2007/03/Rfc/")]
    public class ZALLSAP_UPLOAD_GOODSMOV_2
    {
        private string eBELNField;

        private string pOSNRField;

        private string sGTXTField;

        private string mATNRField;

        private decimal mENGEField;

        //private bool mENGEFieldSpecified;

        private string mEINSField;

        private string wERKSField;

        private string lGORTField;

        private string iNSMKField;

        private string dZUSCHField;

        private string hSDATField;

        private string lICHAField;

        private string cHARGField;

        private string eLIKZField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string EBELN
        {
            get
            {
                return this.eBELNField;
            }
            set
            {
                this.eBELNField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string POSNR
        {
            get
            {
                return this.pOSNRField;
            }
            set
            {
                this.pOSNRField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string SGTXT
        {
            get
            {
                return this.sGTXTField;
            }
            set
            {
                this.sGTXTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = false)]
        public decimal MENGE
        {
            get
            {
                return this.mENGEField;
            }
            set
            {
                this.mENGEField = value;
            }
        }

        /// <remarks/>
        //[System.Xml.Serialization.XmlIgnoreAttribute()]
        //public bool MENGESpecified
        //{
        //    get
        //    {
        //        return this.mENGEFieldSpecified;
        //    }
        //    set
        //    {
        //        this.mENGEFieldSpecified = value;
        //    }
        //}

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string MEINS
        {
            get
            {
                return this.mEINSField;
            }
            set
            {
                this.mEINSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string WERKS
        {
            get
            {
                return this.wERKSField;
            }
            set
            {
                this.wERKSField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LGORT
        {
            get
            {
                return this.lGORTField;
            }
            set
            {
                this.lGORTField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string INSMK
        {
            get
            {
                return this.iNSMKField;
            }
            set
            {
                this.iNSMKField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string DZUSCH
        {
            get
            {
                return this.dZUSCHField;
            }
            set
            {
                this.dZUSCHField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string HSDAT
        {
            get
            {
                return this.hSDATField;
            }
            set
            {
                this.hSDATField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string LICHA
        {
            get
            {
                return this.lICHAField;
            }
            set
            {
                this.lICHAField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string CHARG
        {
            get
            {
                return this.cHARGField;
            }
            set
            {
                this.cHARGField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
        public string ELIKZ
        {
            get
            {
                return this.eLIKZField;
            }
            set
            {
                this.eLIKZField = value;
            }
        }
    }
}
