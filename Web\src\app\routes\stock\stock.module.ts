import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { StockRoutingModule } from './stock-routing.module';
import { TemplatesModule } from '../.templates/templates.module';
import { InboundComponent } from './inbound/inbound.component';
import { InboundBaseComponent } from './inbound/base/base.component';
import { InboundOrderComponent } from './inbound/order/order.component';
import { InboundEditComponent } from './inbound/edit/edit.component';
import { InboundTargetComponent } from './inbound/target/target.component';
import { OutboundComponent } from './outbound/outbound.component';
import { OutboundBaseComponent } from './outbound/base/base.component';
import { OutboundBaseBatchComponent } from './outbound/basebatch/basebatch.component';
import { OutboundOrderComponent } from './outbound/order/order.component';
import { OutboundTargetComponent } from './outbound/target/target.component';
import { OutboundTargetBatchComponent } from './outbound/targetbatch/targetbatch.component';
import { PickingComponent } from './picking/picking.component';
import { CombineComponent } from './combine/combine.component';
import { CombineTargetComponent } from './combine/target/target.component';
import { PalletizeComponent } from './palletize/palletize.component';
import { PalletizeTargetComponent } from './palletize/target/target.component';
import { DepalletizeComponent } from './depalletize/depalletize.component';
import { InventoryComponent } from './inventory/inventory.component';
import { InventoryTargetComponent } from './inventory/target/target.component';
import { BlockComponent } from './block/block.component';
import { BlockEditComponent } from './block/edit/edit.component';
import { PkpickComponent } from './pkpick/pkpick.component';
import { PkPickingComponent } from './pkpicking/pkpicking.component';
import { PkpickOrderComponent } from './pkpick/order/order.component';
import { PrintconfirmComponent } from './pkpicking/printconfirm/printconfirm.component';


@NgModule({
  declarations: [
    InboundComponent,
    InboundBaseComponent,
    InboundOrderComponent,
    InboundEditComponent,
    InboundTargetComponent,
    OutboundComponent,
    OutboundBaseComponent,
    OutboundOrderComponent,
    OutboundTargetComponent,
    OutboundBaseBatchComponent,
    OutboundTargetBatchComponent,
    PickingComponent,
    CombineComponent,
    CombineTargetComponent,
    PalletizeComponent,
    PalletizeTargetComponent,
    DepalletizeComponent,
    InventoryComponent,
    InventoryTargetComponent,
    PkpickComponent,
    PkpickOrderComponent,
    BlockComponent,
    PkPickingComponent,
    BlockEditComponent,
    PrintconfirmComponent
  ],
  imports: [
    SharedModule,
    StockRoutingModule,
    TemplatesModule
  ]
})
export class StockModule { }
