<crud-template #$crud [addable]="false" [editable]="false" [deletable]="false" [selectable]="false" [pageable]="false"
  [defination]="defination" [api]="api" [query]="'plancode=' + barcode+'&area=2'" [convertor]="convertor"
  [toolbar-template]="$barcode" [row-operation-template]="$operation">
  <!--质量状态列-->
  <ng-template #$qs let-row>{{ 'routes.material.enum.qs.' + row.qualityState | translate }}</ng-template>
  <!--库存状态列-->
  <ng-template #$enabled let-row>{{ 'routes.material.enum.enabled.' + row.enabled | translate }}</ng-template>
  <!--操作-->
  <ng-template #$operation let-row>
     <button mat-icon-button matTooltip="{{'shared.operation.print' | translate}}" (click)="pdaPrint(row)" [disabled]="!row.available">
      <mat-icon>print</mat-icon>
    </button>
    <button mat-icon-button matTooltip="{{'routes.stock.pkpicking.confirm' | translate}}" (click)="confirm(row)" [disabled]="!row.available">
      <mat-icon>flag</mat-icon>
    </button>
    <button mat-icon-button matTooltip="{{'routes.stock.pkpicking.cancel' | translate}}" (click)="cancel(row)">
      <mat-icon>delete</mat-icon>
    </button>
  </ng-template>
  <!--条码查询框-->
  <ng-template #$barcode>
    <mat-icon class="searchbox-icon" (click)="search()">search</mat-icon>
    <input cdkFocusInitial class="searchbox-text" placeholder="{{ 'routes.stock.pkpicking.plancode' | translate }}" [(ngModel)]="barcode" (focus)="focus = true" (blur)="focus = false" (keydown.enter)="search()">
  </ng-template>
</crud-template>
<mat-toolbar class="mat-elevation-z2 searchbox-focus" [@focus]="focus ? 'focus' : 'blur'"></mat-toolbar>
