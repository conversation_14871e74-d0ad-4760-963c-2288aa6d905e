﻿using Kean.Domain.Task.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Domain.Task.Strategies
{
    /// <summary>
    /// 上架策略上下文
    /// </summary>
    public sealed class InfeedContext<T>(IServiceProvider serviceProvider) where T : InfeedStrategy
    {
        private readonly T _strategy = Activator.CreateInstance(typeof(T),
                [.. typeof(T).GetConstructors().First().GetParameters().Select(p => p.ParameterType == typeof(IServiceProvider) ? serviceProvider : serviceProvider.GetService(p.ParameterType))]) as T; // 策略实例

        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public Task<Cell> AutoDestinationWithLaneway(string pallet, Station original, IEnumerable<string> sLaneway, int? spec) =>
            _strategy.AutoDestinationWithLaneway(pallet, original, sLaneway, spec);

        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public Task<Cell> AutoDestination(string pallet, Station original, int? spec) =>
            _strategy.AutoDestination(pallet, original, spec);
        /// <summary>
        /// 获取自动分配的目标货位
        /// </summary>
        /// <param name="pallet">载具类型</param>
        /// <param name="original">任务起始位置</param>
        /// <param name="spec">负载规格</param>
        /// <returns>自动分配的目标货位</returns>
        public Task<Cell> AutoDestination(string pallet, string remark, int planlistid, Station original, int? spec) =>
            _strategy.AutoDestination(pallet, remark, planlistid, original, spec);
    }
}
