﻿using Kean.Application.Command.ViewModels;
using Kean.Domain.Interface.RemoteClients;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Presentation.Rest.Controllers
{
    [ApiController, Route("api/labels")]
    public class LabelsController(
       IPrintClient printClient,    // ERP 接口
                                    //Application.Command.Interfaces.IPrintService prinetService,   // 库存命令服务
       Application.Query.Interfaces.IDeviceService deviceQueryService    // 设备查询服务
   ) : ControllerBase
    {
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(405)]
        [ProducesResponseType(422)]
        public async Task<IActionResult> Print()
        {
            Failure failure = new Failure();
            try
            {
                string? pdaIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                // 通过代理服务器获取真实的客户端 IP
                if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))
                {
                    pdaIp = HttpContext.Request.Headers["X-Forwarded-For"];
                }
                // 本地调试用
                if (pdaIp == "::1")
                {
                    pdaIp = "127.0.0.1";
                }
                var items = await deviceQueryService.GetPrinterIpList(null, null, null, null, null, null);
                var bindPrinter = items.FirstOrDefault(x => x.PdaIp == pdaIp);


                return StatusCode(200, new { items = items, bindPrinter = bindPrinter });
            }
            catch (Exception ex)
            {

                return StatusCode(422);
            }


        }

        [HttpPost("tcp")]
        [ProducesResponseType(200)]
        [ProducesResponseType(405)]
        [ProducesResponseType(422)]
        public async Task<IActionResult> sendPrint(
             [FromMember] PrintModel ptrintInfoList
            )
        {
            //string? pdaIp = HttpContext.Connection.RemoteIpAddress?.ToString();
            //// 通过代理服务器获取真实的客户端 IP
            //if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))
            //{
            //    pdaIp = HttpContext.Request.Headers["X-Forwarded-For"];
            //}
            //// 本地调试用
            //if (pdaIp == "::1")
            //{
            //    pdaIp = "127.0.0.1";
            //}
            //var items = await deviceQueryService.GetPrinterIpList(pdaIp, null, null, null, null, null);
            var printerIp = ptrintInfoList.printerIp;
            //foreach (var item in items)
            //{
            //    printerIp = item.PrinterIp.ToString();
            //}

            var mes = await printClient.Print(printerIp, ptrintInfoList.infoList);
            return StatusCode(201, new { msg = mes });
        }


        public class PrintModel
        {
            //打印信息：code<#>name<#>model<#>bill<#>batch<#>sn<#>qty<#>supplier
            public string infoList { get; set; }
            public string printerIp { get; set; }
        }


    }
}
