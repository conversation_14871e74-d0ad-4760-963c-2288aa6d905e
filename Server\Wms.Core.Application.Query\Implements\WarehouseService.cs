﻿using AutoMapper;
using Dapper;
using Kean.Application.Query.Interfaces;
using Kean.Application.Query.ViewModels;
using Kean.Infrastructure.Database;
using Kean.Infrastructure.Database.Repository.Default;
using Kean.Infrastructure.Database.Repository.Default.Entities;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace Kean.Application.Query.Implements
{
    /// <summary>
    /// 库房查询服务实现
    /// </summary>
    public class WarehouseService(
        IMapper mapper,     // 模型映射
        IDefaultDb database // 默认数据库
    ) : IWarehouseService
    {
        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetWarehouseCount 方法
         */
        public async Task<int> GetWarehouseCount()
        {
            return (int)(await database.From<T_WH_WAREHOUSE>()
                .Single(w => new { Count = Function.Count(w.WAREHOUSE_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetWarehouseList 方法
         */
        public async Task<IEnumerable<Warehouse>> GetWarehouseList(string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Warehouse>>(await database.From<T_WH_WAREHOUSE>()
                .Sort<T_WH_WAREHOUSE, Warehouse>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetAreaCount 方法
         */
        public async Task<int> GetAreaCount(int[] id, int? warehouse)
        {
            return (int)(await GetAreaSchema(id, warehouse)
                .Single(a => new { Count = Function.Count(a.AREA_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetAreaList 方法
         */
        public async Task<IEnumerable<Area>> GetAreaList(int[] id, int? warehouse, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Area>>(await GetAreaSchema(id, warehouse)
                .Sort<T_WH_AREA, Area>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        /*
         * 组织 GetArea 相关方法的条件
         */
        private ISchema<T_WH_AREA> GetAreaSchema(int[] id, int? warehouse)
        {
            var schema = database.From<T_WH_AREA>();
            if (id != null)
            {
                schema = schema.Where(a => id.Contains(a.AREA_ID));
            }
            if (warehouse.HasValue)
            {
                schema = schema.Where(a => a.WAREHOUSE_ID == warehouse);
            }
            return schema;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetCellCount 方法
         */
        public async Task<int> GetCellCount(int[] area, string type, bool? @in, bool? @out, string name, string state)
        {
            return (int)(await GetCellSchema(area, type, @in, @out, name, state)
                .Single(c => new { Count = Function.Count(c.CELL_ID) }))
                .Count;
        }

        /*
         * 实现 Kean.Application.Query.Interfaces.IWarehouseService.GetCellList 方法
         */
        public async Task<IEnumerable<Cell>> GetCellList(int[] area, string type, bool? @in, bool? @out, string name, string state, string sort, int? offset, int? limit)
        {
            return mapper.Map<IEnumerable<Cell>>(await GetCellSchema(area, type, @in, @out, name, state)
                .Sort<T_WH_CELL, Cell>(sort, mapper)
                .Page(offset, limit)
                .Select());
        }

        public Task<IEnumerable<Cell>> GetCellListNodata(int[] area, string type, bool? @in, bool? @out, string name, string state, string sort, int? offset, int? limit)
        {
            string sArea = "";
            if (area.Length == 0)
            {
                sArea = "AND T0.AREA_ID = 1 ";
            }
            else
            {
                foreach (int areaId in area)
                {
                    sArea += $"OR T0.AREA_ID = {areaId} ";
                }
                sArea = $"AND ({sArea[3..]}) ";
            }
            var mWH_CELL = database.Context.Query<T_WH_CELL>($"SELECT * FROM T_WH_CELL T0 WHERE (T0.CELL_STATUS = 'Full' or T0.CELL_STATUS = 'Have') {sArea} AND T0.CELL_TYPE='Cell' AND T0.RUN_STATUS = 'Enabled' AND T0.CELL_ID NOT IN (SELECT T_STORAGE_MAIN.CELL_ID FROM T_STORAGE_MAIN) ORDER BY T0.CELL_CODE", transaction: database.Context.Transaction);
            var result = mapper.Map<IEnumerable<Cell>>(mWH_CELL);
            return System.Threading.Tasks.Task.FromResult(result);

        }

        public async Task<int> getOutPutDestination(int? startCellId, string startCellCode, int areaId)
        {
            int iCellId = 0;
            if (startCellId != null)
            {
                T_WH_CELL startId = await database.From<T_WH_CELL>().Where(c => c.CELL_ID == startCellId).Single();
                if (startId != null && startId.CELL_LANEWAY != null)
                {

                    if (areaId == 0)
                    {
                        areaId = 1;
                    }
                    iCellId = (await database.From<T_WH_CELL>().Where(p => p.CELL_TYPE == "Station" && p.CELL_OUT == true && p.AREA_ID == areaId && p.CELL_CODE.Contains(startId.CELL_LANEWAY)).Single()).CELL_ID;

                }
            }
            if (iCellId != 0)
            {
                return iCellId;
            }
            if (!string.IsNullOrEmpty(startCellCode))
            {
                T_WH_CELL startCode = await database.From<T_WH_CELL>().Where(c => c.CELL_CODE == startCellCode || c.CELL_NAME == startCellCode).Single();
                if (startCode != null && startCode.CELL_LANEWAY != null)
                {
                    if (areaId == 0)
                    {
                        areaId = 1;
                    }
                    T_WH_CELL tmp = await database.From<T_WH_CELL>().Where(p => p.CELL_TYPE == "Station" && p.CELL_OUT == true && p.AREA_ID == areaId && p.CELL_LANEWAY.Contains(startCode.CELL_LANEWAY)).Single();//.Result.CELL_ID;
                    if (tmp != null)
                    {
                        iCellId = tmp.CELL_ID;
                        return iCellId;
                    }
                }
            }

            return iCellId;
        }

        public async Task<string> getOutPutDestinationCode(int? startCellId, string startCellCode, int areaId)
        {
            string sCellCode = string.Empty;
            if (startCellId != null)
            {
                T_WH_CELL start = database.From<T_WH_CELL>().Where(c => c.CELL_ID == startCellId).Single().Result;
                if (start != null)
                {
                    if (start.CELL_LANEWAY != null)
                    {
                        sCellCode = start.CELL_LANEWAY;
                    }
                }
            }
            if (!string.IsNullOrEmpty(sCellCode))
            {
                return sCellCode;
            }
            if (!string.IsNullOrEmpty(startCellCode))
            {
                T_WH_CELL startCode = database.From<T_WH_CELL>().Where(c => c.CELL_CODE == startCellCode || c.CELL_NAME == startCellCode).Single().Result;
                if (startCode.AREA_ID == 1 && startCode.CELL_LANEWAY != null)
                {
                    sCellCode = startCode.CELL_LANEWAY;
                }
            }

            return sCellCode;
        }

        /*
         * 组织 GetCell 相关方法的条件
         */
        private ISchema<T_WH_CELL> GetCellSchema(int[] area, string type, bool? @in, bool? @out, string name, string state)
        {
            var schema = database.From<T_WH_CELL>().Where(c => c.CELL_FLAG == true);
            if (area != null)
            {
                schema = schema.Where(c => area.Contains(c.AREA_ID));
            }
            if (type != null)
            {
                schema = schema.Where(c => c.CELL_TYPE == type);
            }
            if (@in.HasValue)
            {
                schema = schema.Where(c => c.CELL_IN == @in);
            }
            if (@out.HasValue)
            {
                schema = schema.Where(c => c.CELL_OUT == @out);
            }
            if (name != null)
            {
                schema = schema.Where(c => c.CELL_NAME == name);
            }
            if (state != null)
            {
                schema = schema.Where(c => c.RUN_STATUS == state);
            }
            return schema;
        }
    }
}
