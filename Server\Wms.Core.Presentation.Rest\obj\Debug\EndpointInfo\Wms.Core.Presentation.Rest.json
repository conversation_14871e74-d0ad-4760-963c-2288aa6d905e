{"openapi": "3.0.1", "info": {"title": "<PERSON>an.Presentation.Rest", "contact": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/areas": {"get": {"tags": ["Areas"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "warehouse", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Areas"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/areas/{id}": {"put": {"tags": ["Areas"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/ae868058cc3448408d8c6983b987bc00"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/cells": {"get": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "in", "in": "query", "schema": {"type": "boolean"}}, {"name": "out", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/cells/degreeupdate": {"post": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "warehouse", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "startCellCode", "in": "query", "schema": {"type": "string"}}, {"name": "endCellCode", "in": "query", "schema": {"type": "string"}}, {"name": "degree", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/cells/disablements": {"get": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/cells/disablements/batch": {"post": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "warehouse", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "remark", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "text/json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/cells/nodata": {"get": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/cells/nodata/controlout": {"post": {"tags": ["Cells"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "warehouse", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "remark", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "text/json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/2976b8be23e74b7b969dfa2c90323fb8"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/clients/category": {"get": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "text/json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/clients/category/{id}": {"put": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "text/json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/adadba7deaac4a9bb702142444b62f9a"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/clients/information": {"get": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "clientCateId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "text/json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/clients/information/{id}": {"put": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "text/json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/377c19d9d5ef4d909986dc8deec2005b"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/clients/information/batch": {"post": {"tags": ["Clients"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/devices": {"get": {"tags": ["Devices"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/devices/{id}/command": {"post": {"tags": ["Devices"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "command", "in": "query", "schema": {"type": "string"}}], "responses": {"201": {"description": "Created"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/devices/errors": {"get": {"tags": ["Devices"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "device", "in": "query", "schema": {"type": "string"}}, {"name": "code", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "startTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/instructions": {"get": {"tags": ["Instructions"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "number", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "task", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "originalDevice", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "generatedTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "generatedTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Instructions"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "device", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "forward", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "parameters", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}], "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/instructions/{id}": {"delete": {"tags": ["Instructions"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "forward", "in": "query", "schema": {"type": "boolean"}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/interfaces": {"get": {"tags": ["Interfaces"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "schema": {"type": "string"}}, {"name": "function", "in": "query", "schema": {"type": "string"}}, {"name": "unique", "in": "query", "schema": {"type": "string"}}, {"name": "result", "in": "query", "schema": {"type": "boolean"}}, {"name": "timestampFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "timestampTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/interfaces/excel": {"get": {"tags": ["Interfaces"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "schema": {"type": "string"}}, {"name": "function", "in": "query", "schema": {"type": "string"}}, {"name": "unique", "in": "query", "schema": {"type": "string"}}, {"name": "result", "in": "query", "schema": {"type": "boolean"}}, {"name": "timestampFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "timestampTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ios/applies": {"get": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "controlId", "in": "query", "schema": {"type": "string"}}, {"name": "deviceCode", "in": "query", "schema": {"type": "string"}}, {"name": "stockbarcode", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "warehouseCode", "in": "query", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "text/json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/ios/applies/{id}": {"put": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "text/json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/a911046f69bb46f3922ab2f3249b856c"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/ios/applies/batch": {"post": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/ios/controls": {"get": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "manageId", "in": "query", "schema": {"type": "string"}}, {"name": "preStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "stockbarcode", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "startWarehouseCode", "in": "query", "schema": {"type": "string"}}, {"name": "endWarehouseCode", "in": "query", "schema": {"type": "string"}}, {"name": "startDeviceCode", "in": "query", "schema": {"type": "string"}}, {"name": "endDeviceCode", "in": "query", "schema": {"type": "string"}}, {"name": "relativeControlId", "in": "query", "schema": {"type": "string"}}, {"name": "errorText", "in": "query", "schema": {"type": "string"}}, {"name": "level", "in": "query", "schema": {"type": "string"}}, {"name": "beginTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "beginTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "text/json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/ios/controls/{id}": {"put": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "text/json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/02306f8484f64285a0a4ad30a9135a5a"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/ios/controls/batch": {"post": {"tags": ["IOs"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/logs": {"get": {"tags": ["Logs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "operator", "in": "query", "schema": {"type": "string"}}, {"name": "ip<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "message", "in": "query", "schema": {"type": "string"}}, {"name": "start", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "end", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/logs/operation": {"get": {"tags": ["Logs"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "operator", "in": "query", "schema": {"type": "string"}}, {"name": "station", "in": "query", "schema": {"type": "string"}}, {"name": "opprocess", "in": "query", "schema": {"type": "string"}}, {"name": "start", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "end", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "opfunction", "in": "query", "schema": {"type": "string"}}, {"name": "opmessage", "in": "query", "schema": {"type": "string"}}, {"name": "IO", "in": "query", "schema": {"type": "string"}}, {"name": "orderseq", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/materials": {"get": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "group", "in": "query", "schema": {"type": "string"}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "text/json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/{id}": {"get": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}, "put": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "text/json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/30eba5d3b6f6491ca60bf1ddf3360aea"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/batch": {"post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/categories": {"get": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "text/json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/categories/{id}": {"put": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "text/json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/6fe5ed5abcbf429fb176fdc9c626272f"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/categories/batch": {"post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/safeties": {"get": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "warehouse", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "text/json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/safeties/{id}": {"put": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "text/json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/3a5904910cc049d791136cd8a61f9a3f"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/materials/safeties/batch": {"post": {"tags": ["Materials"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "number", "in": "query", "schema": {"type": "string"}}, {"name": "creater", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "text/json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}}}, "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/{id}": {"put": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "text/json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/7731445024774c4b8600aba2287d7dcb"}}}}, "responses": {"200": {"description": "OK"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}, "delete": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/{id}/lines": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "state", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/{id}/state": {"put": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json-patch+json": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}, "application/xml": {"schema": {"type": "integer", "format": "int32"}}, "text/xml": {"schema": {"type": "integer", "format": "int32"}}, "application/*+xml": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/archives": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "number", "in": "query", "schema": {"type": "string"}}, {"name": "creater", "in": "query", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "finalTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "finalTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/archives/{id}/lines": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/archives/{id}/lines/excel": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/archives/excel": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "number", "in": "query", "schema": {"type": "string"}}, {"name": "creater", "in": "query", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "finalTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "finalTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/confirm": {"post": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "application/json": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "text/json": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/3c897a8bf21e40c991e65aba0b0a370b"}}}}, "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/crane": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/getPSlist": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "plancode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/getStoragelist": {"post": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "orders", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/c6135ba6299a46bc88750ed68eeeac2c"}}}, {"name": "half", "in": "query", "schema": {"type": "boolean"}}, {"name": "full", "in": "query", "schema": {"type": "boolean"}}, {"name": "crane", "in": "query", "schema": {"type": "string"}}, {"name": "overFullPallet", "in": "query", "schema": {"type": "boolean"}}, {"name": "ignoreNullStorage", "in": "query", "schema": {"type": "boolean"}}, {"name": "allowdays", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/hedan": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "planid", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "tag", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/platform": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "plancode", "in": "query", "schema": {"type": "string"}}, {"name": "no", "in": "query", "schema": {"type": "string"}}, {"name": "truck", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/sendOutfeed": {"post": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "orders", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/c6135ba6299a46bc88750ed68eeeac2c"}}}, {"name": "stocks", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/bfca022d50e841aeb721f1f1fb3e7479"}}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/sendOutfeedPK": {"post": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "orders", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/c6135ba6299a46bc88750ed68eeeac2c"}}}, {"name": "stocks", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/bfca022d50e841aeb721f1f1fb3e7479"}}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/orders/types": {"get": {"tags": ["Orders"], "parameters": [{"name": "token", "in": "header", "required": true}], "responses": {"200": {"description": "OK"}}}}, "/api/params": {"get": {"tags": ["Params"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "key", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Params"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "key", "in": "query", "schema": {"type": "string"}}, {"name": "value", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/platforms": {"get": {"tags": ["Platforms"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Platforms"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "text/json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/platforms/{id}": {"put": {"tags": ["Platforms"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "text/json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/21eb6d1c9f3f4cc2a59e96da14999e1b"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/platforms/batch": {"post": {"tags": ["Platforms"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/records": {"get": {"tags": ["Records"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "record", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "transaction", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "beginTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "beginTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/records/excel": {"get": {"tags": ["Records"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "record", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "transaction", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "beginTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "beginTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/records/pallets": {"get": {"tags": ["Records"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "transaction", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "beginTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "beginTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/records/pallets/excel": {"get": {"tags": ["Records"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "transaction", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "beginTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "beginTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles": {"get": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "text/json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/roles/{id}": {"put": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "text/json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/8c7243e59d844a3f84790d12d8b92a83"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/roles/{id}/menu": {"get": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/json-patch+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}, "responses": {"200": {"description": "OK"}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/roles/batch": {"post": {"tags": ["Roles"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/sessions": {"post": {"tags": ["Sessions"], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "text/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}}}, "responses": {"201": {"description": "Created"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "423": {"description": "Locked", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}, "delete": {"tags": ["Sessions"], "parameters": [{"name": "reason", "in": "query", "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}}}}, "/api/stocks": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "pallet", "in": "query", "schema": {"type": "boolean"}}, {"name": "palletized", "in": "query", "schema": {"type": "boolean"}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "qc", "in": "query", "schema": {"type": "string"}}, {"name": "inboundTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inboundTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "overdue", "in": "query", "schema": {"type": "boolean"}}, {"name": "enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "excludeTask", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "operation", "in": "query", "schema": {"type": "string"}}, {"name": "stock", "in": "query", "schema": {"$ref": "#/components/schemas/0c163b645cd14623949513708b75f94c"}}, {"name": "full", "in": "query", "schema": {"type": "boolean"}}, {"name": "destination", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "force", "in": "query", "schema": {"type": "boolean"}}], "responses": {"201": {"description": "Created"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/stocks/distribute": {"post": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "stock", "in": "query", "schema": {"$ref": "#/components/schemas/0c163b645cd14623949513708b75f94c"}}], "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/stocks/excel": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "pallet", "in": "query", "schema": {"type": "boolean"}}, {"name": "palletized", "in": "query", "schema": {"type": "boolean"}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "qc", "in": "query", "schema": {"type": "string"}}, {"name": "inboundTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inboundTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "overdue", "in": "query", "schema": {"type": "boolean"}}, {"name": "enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "excludeTask", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/stocks/locks": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "batch", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "qc", "in": "query", "schema": {"type": "string"}}, {"name": "areatype", "in": "query", "schema": {"type": "string"}}, {"name": "qrcode", "in": "query", "schema": {"type": "string"}}, {"name": "plancode", "in": "query", "schema": {"type": "string"}}, {"name": "planid", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "inboundTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inboundTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "inventoryTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "overdue", "in": "query", "schema": {"type": "boolean"}}, {"name": "enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "stock", "in": "query", "schema": {"$ref": "#/components/schemas/0c163b645cd14623949513708b75f94c"}}, {"name": "task", "in": "query", "schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/318395e780894cd1979976fc08619eb3"}}}], "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/stocks/pallets": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "pallet", "in": "query", "schema": {"type": "boolean"}}, {"name": "palletized", "in": "query", "schema": {"type": "boolean"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "excludeTask", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/stocks/pallets/excel": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "pallet", "in": "query", "schema": {"type": "boolean"}}, {"name": "palletized", "in": "query", "schema": {"type": "boolean"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "cell", "in": "query", "schema": {"type": "string"}}, {"name": "excludeTask", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/stocks/safeties": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "warehouse", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/stocks/safeties/excel": {"get": {"tags": ["Stocks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "warehouse", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "category", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/tasks": {"get": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "timeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "timeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "operation", "in": "query", "schema": {"type": "string"}}, {"name": "task", "in": "query", "schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}], "responses": {"201": {"description": "Created"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/tasks/{id}": {"delete": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "forward", "in": "query", "schema": {"type": "boolean"}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/tasks/{id}/priority": {"put": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "application/json": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "text/json": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/97e61930cc4e4f19b1c26638ebba1181"}}}}, "responses": {"200": {"description": "OK"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/tasks/detail": {"get": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/tasks/manageList": {"get": {"tags": ["Tasks"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "area", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "barcode", "in": "query", "schema": {"type": "string"}}, {"name": "original", "in": "query", "schema": {"type": "string"}}, {"name": "destination", "in": "query", "schema": {"type": "string"}}, {"name": "timeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "timeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/triggers": {"get": {"tags": ["Triggers"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "warehouse", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "device", "in": "query", "schema": {"type": "string"}}, {"name": "result", "in": "query", "schema": {"type": "string"}}, {"name": "timeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "timeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Triggers"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/159c1faf11ef4b5abf714d5616dc36e5"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/159c1faf11ef4b5abf714d5616dc36e5"}}, "application/json": {"schema": {"$ref": "#/components/schemas/159c1faf11ef4b5abf714d5616dc36e5"}}, "text/json": {"schema": {"$ref": "#/components/schemas/159c1faf11ef4b5abf714d5616dc36e5"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/159c1faf11ef4b5abf714d5616dc36e5"}}}}, "responses": {"201": {"description": "Created"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/triggers/{id}": {"delete": {"tags": ["Triggers"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "account", "in": "query", "schema": {"type": "string"}}, {"name": "role", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "text/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/{id}": {"put": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "text/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/{id}/password": {"delete": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/batch": {"post": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4a7e14f8073f4647b6db028765d94fda"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/current": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "responses": {"200": {"description": "OK"}}}}, "/api/users/current/messages": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "subject", "in": "query", "schema": {"type": "string"}}, {"name": "source", "in": "query", "schema": {"type": "string"}}, {"name": "start", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "end", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "flag", "in": "query", "schema": {"type": "boolean"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/current/messages/{id}": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/current/messages/batch": {"post": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/4164f4f8133a430f8ce9580d98d322f6"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/4164f4f8133a430f8ce9580d98d322f6"}}, "application/json": {"schema": {"$ref": "#/components/schemas/4164f4f8133a430f8ce9580d98d322f6"}}, "text/json": {"schema": {"$ref": "#/components/schemas/4164f4f8133a430f8ce9580d98d322f6"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/4164f4f8133a430f8ce9580d98d322f6"}}}}, "responses": {"200": {"description": "OK"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/current/password": {"post": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "text/json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}}}, "responses": {"201": {"description": "Created"}, "405": {"description": "Method Not Allowed", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "text/json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}}}}, "responses": {"200": {"description": "OK"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/current/profile": {"put": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "text/json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/91925d8a79e94185859eeff805ce0451"}}}}, "responses": {"200": {"description": "OK"}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/users/current/routes": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}], "responses": {"200": {"description": "OK"}}}}, "/api/users/current/routes/{url}": {"get": {"tags": ["Users"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "url", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "419": {"description": "<PERSON><PERSON>", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "428": {"description": "Precondition Required", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/warehouses": {"get": {"tags": ["Warehouses"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Warehouses"], "parameters": [{"name": "token", "in": "header", "required": true}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "text/json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}}}, "responses": {"201": {"description": "Created"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}, "/api/warehouses/{id}": {"put": {"tags": ["Warehouses"], "parameters": [{"name": "token", "in": "header", "required": true}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"text/plain": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "text/json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/de503ecc3e984a7082de365ea0543633"}}}}, "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "410": {"description": "Gone", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}, "422": {"description": "Unprocessable Content", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/json": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/00aeb9a0881a44c887bcb6dc71a7580e"}}}}}}}}, "components": {"schemas": {"005fdc156ff24509851668f8fb61483a": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/4a01b5705b5e4b2dbfc6ef55e3004515"}, "methodImplementationFlags": {"$ref": "#/components/schemas/5b3e536fe5914bbd830c53db6007f808"}, "callingConvention": {"$ref": "#/components/schemas/8cb76efe33504c24b1aec4fbe3cd8c09"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/435c4fd26f55443c8f94b345d0a0c2d4"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "00aeb9a0881a44c887bcb6dc71a7580e": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "02306f8484f64285a0a4ad30a9135a5a": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "relativeControlId": {"type": "integer", "format": "int32", "nullable": true}, "manageId": {"type": "integer", "format": "int32", "nullable": true}, "stockbarcode": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32", "nullable": true}, "level": {"type": "string", "nullable": true}, "startWarehouseCode": {"type": "string", "nullable": true}, "startDeviceCode": {"type": "string", "nullable": true}, "endWarehouseCode": {"type": "string", "nullable": true}, "endDeviceCode": {"type": "string", "nullable": true}, "preStatus": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "errorText": {"type": "string", "nullable": true}, "beginTime": {"type": "string", "nullable": true}, "endTime": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "0c163b645cd14623949513708b75f94c": {"type": "object", "properties": {"barcode": {"type": "string", "nullable": true}, "lines": {"type": "array", "items": {"$ref": "#/components/schemas/c467cf500fd440e982212ed620854df2"}, "nullable": true}, "operator": {"type": "integer", "format": "int32"}, "tag": {"type": "string", "nullable": true}, "orders": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "13c3f4d687ce441caa8ba53d77c39549": {"type": "object", "properties": {"memberInfo": {"$ref": "#/components/schemas/c00824303edd4253868014aaa9cce47a"}, "typedValue": {"$ref": "#/components/schemas/31a0ff6247664b18aaa85349e6ce04fb"}, "memberName": {"type": "string", "nullable": true, "readOnly": true}, "isField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "159c1faf11ef4b5abf714d5616dc36e5": {"type": "object", "properties": {"type": {"type": "integer", "format": "int32"}, "warehouse": {"type": "integer", "format": "int32"}, "device": {"type": "string", "nullable": true}, "parameter": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/318395e780894cd1979976fc08619eb3"}, "nullable": true}, "timeout": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "21eb6d1c9f3f4cc2a59e96da14999e1b": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "2976b8be23e74b7b969dfa2c90323fb8": {"type": "object", "properties": {"method": {"$ref": "#/components/schemas/8b3a3abffa694e01b5bc7285910e20ce"}, "data": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "30eba5d3b6f6491ca60bf1ddf3360aea": {"type": "object", "properties": {"batch": {"type": "string", "nullable": true}, "bill": {"type": "string", "nullable": true}, "supplier": {"type": "string", "nullable": true}, "manufacturingDate": {"type": "string", "format": "date-time", "nullable": true}, "qualityState": {"type": "string", "nullable": true}, "plantCode": {"type": "string", "nullable": true}, "productionLine": {"type": "string", "nullable": true}, "clientCode": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "financialPostStatus": {"type": "string", "nullable": true}, "truckInfo": {"type": "string", "nullable": true}, "transferType": {"type": "string", "nullable": true}, "workGroup": {"type": "string", "nullable": true}, "adjustQuantity": {"type": "number", "format": "double", "nullable": true}, "adjustReason": {"type": "string", "nullable": true}, "financialPostQuantity": {"type": "number", "format": "double", "nullable": true}, "planListRepoTime": {"type": "string", "nullable": true}, "planListProdTime": {"type": "string", "nullable": true}, "financialPostTime": {"type": "integer", "format": "int32", "nullable": true}, "financialPostNo": {"type": "string", "nullable": true}, "financialPostLatestTime": {"type": "string", "nullable": true}, "financialPostUploadTime": {"type": "string", "nullable": true}, "importTime": {"type": "string", "nullable": true}, "sapNo": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "length": {"type": "number", "format": "double", "nullable": true}, "width": {"type": "number", "format": "double", "nullable": true}, "height": {"type": "number", "format": "double", "nullable": true}, "facade": {"type": "string", "nullable": true}, "effectivePeriod": {"type": "integer", "format": "int32", "nullable": true}, "expirationPeriod": {"type": "integer", "format": "int32", "nullable": true}, "qualityControl": {"type": "boolean", "nullable": true}, "minimumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "maximumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "goodsCode69": {"type": "string", "nullable": true}, "goodsCodeId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "category": {"type": "integer", "format": "int32", "nullable": true}, "group": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "palletQuantity": {"type": "number", "format": "double"}}, "additionalProperties": false}, "318395e780894cd1979976fc08619eb3": {"type": "array", "items": {"$ref": "#/components/schemas/318395e780894cd1979976fc08619eb3"}}, "31985d235eb14ab191e2bed430676fb2": {"enum": [0, 512, 1024], "type": "integer", "format": "int32"}, "31a0ff6247664b18aaa85349e6ce04fb": {"type": "object", "properties": {"argumentType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "value": {"nullable": true}}, "additionalProperties": false}, "36fd5934e97044228725a619771f0688": {"type": "object", "properties": {"attributeType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "constructor": {"$ref": "#/components/schemas/4b00f96fc68b409c9e4c41c81f104fb1"}, "constructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/31a0ff6247664b18aaa85349e6ce04fb"}, "nullable": true, "readOnly": true}, "namedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/13c3f4d687ce441caa8ba53d77c39549"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "377c19d9d5ef4d909986dc8deec2005b": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "beginDateCount": {"type": "integer", "format": "int32"}, "finalDateCount": {"type": "integer", "format": "int32"}, "model": {"type": "string", "nullable": true}, "shortName": {"type": "string", "nullable": true}, "clientCateId": {"type": "integer", "format": "int32"}, "clientCateCode": {"type": "string", "nullable": true}, "clientCateName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "37cec5a056a94f5e875838bb8545ed4f": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "3a5904910cc049d791136cd8a61f9a3f": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "material": {"type": "integer", "format": "int32"}, "warehouse": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "lowerLimit": {"type": "number", "format": "double", "nullable": true}, "upperLimit": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "3c897a8bf21e40c991e65aba0b0a370b": {"type": "object", "properties": {"planId": {"type": "integer", "format": "int32"}, "planListId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "tag": {"type": "string", "nullable": true}}, "additionalProperties": false}, "4164f4f8133a430f8ce9580d98d322f6": {"type": "object", "properties": {"method": {"$ref": "#/components/schemas/8b3a3abffa694e01b5bc7285910e20ce"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/b7a90f4f734549428905232648d93ba8"}, "nullable": true}}, "additionalProperties": false}, "435c4fd26f55443c8f94b345d0a0c2d4": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/a567956579ca4dcbb80ce503a5bac8cb"}}, "additionalProperties": false}, "4a01b5705b5e4b2dbfc6ef55e3004515": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 53248], "type": "integer", "format": "int32"}, "4a7e14f8073f4647b6db028765d94fda": {"type": "object", "properties": {"method": {"$ref": "#/components/schemas/8b3a3abffa694e01b5bc7285910e20ce"}, "data": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "4b00f96fc68b409c9e4c41c81f104fb1": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/4a01b5705b5e4b2dbfc6ef55e3004515"}, "methodImplementationFlags": {"$ref": "#/components/schemas/5b3e536fe5914bbd830c53db6007f808"}, "callingConvention": {"$ref": "#/components/schemas/8cb76efe33504c24b1aec4fbe3cd8c09"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/435c4fd26f55443c8f94b345d0a0c2d4"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}}, "additionalProperties": false}, "4ec5576c7bf547edae908158c5adb380": {"enum": [0, 1, 2, 3, 4, 8, 16, 28], "type": "integer", "format": "int32"}, "4f390d43e0a341c78ee3aa1e3c049605": {"type": "object", "properties": {"definedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/b1a22519bd6e4041aa49c6d2f6c82c8b"}, "nullable": true, "readOnly": true}, "exportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "nullable": true, "readOnly": true}, "codeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "entryPoint": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "imageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "isDynamic": {"type": "boolean", "readOnly": true}, "location": {"type": "string", "nullable": true, "readOnly": true}, "reflectionOnly": {"type": "boolean", "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "isFullyTrusted": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "escapedCodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "manifestModule": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "modules": {"type": "array", "items": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "nullable": true, "readOnly": true}, "globalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "hostContext": {"type": "integer", "format": "int64", "readOnly": true}, "securityRuleSet": {"$ref": "#/components/schemas/37cec5a056a94f5e875838bb8545ed4f"}}, "additionalProperties": false}, "5b3e536fe5914bbd830c53db6007f808": {"enum": [0, 1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 4096, 65535], "type": "integer", "format": "int32"}, "60d236c5bf724dff94e3e7ed30b92749": {"type": "object", "properties": {"handleNull": {"type": "boolean", "readOnly": true}, "type": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "id": {"type": "integer", "format": "int32"}, "current": {"type": "string", "nullable": true}, "replacement": {"type": "string", "nullable": true}}, "additionalProperties": false}, "6fc5bb7d90b44b7fa1d382ffb5e5f438": {"type": "object", "properties": {"assembly": {"$ref": "#/components/schemas/4f390d43e0a341c78ee3aa1e3c049605"}, "fullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "moduleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "scopeName": {"type": "string", "nullable": true, "readOnly": true}, "moduleHandle": {"$ref": "#/components/schemas/a9c0ae37eaf3427b8bd0bea19a33bf4b"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "6fe5ed5abcbf429fb176fdc9c626272f": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "parent": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "7731445024774c4b8600aba2287d7dcb": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "number": {"type": "string", "nullable": true}, "creater": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "lines": {"type": "array", "items": {"$ref": "#/components/schemas/972b1dd4839a486582b65c61325387e7"}, "nullable": true}}, "additionalProperties": false}, "77bdd5870bc64d3d846832eacf17156d": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/a567956579ca4dcbb80ce503a5bac8cb"}}, "additionalProperties": false}, "8670a77d01d440aa971db3b39881fe9b": {"enum": [0, 512, 1024, 4096, 8192, 16384, 32768, 62464], "type": "integer", "format": "int32"}, "8b3a3abffa694e01b5bc7285910e20ce": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "8c7243e59d844a3f84790d12d8b92a83": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "8cb76efe33504c24b1aec4fbe3cd8c09": {"enum": [1, 2, 3, 32, 64], "type": "integer", "format": "int32"}, "91925d8a79e94185859eeff805ce0451": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "account": {"type": "string", "nullable": true}, "password": {"$ref": "#/components/schemas/60d236c5bf724dff94e3e7ed30b92749"}, "avatar": {"$ref": "#/components/schemas/a77cdf79ddae4862b5f82937e7a9f54a"}, "role": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "972b1dd4839a486582b65c61325387e7": {"type": "object", "properties": {"batch": {"type": "string", "nullable": true}, "bill": {"type": "string", "nullable": true}, "supplier": {"type": "string", "nullable": true}, "manufacturingDate": {"type": "string", "format": "date-time", "nullable": true}, "qualityState": {"type": "string", "nullable": true}, "plantCode": {"type": "string", "nullable": true}, "productionLine": {"type": "string", "nullable": true}, "clientCode": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "financialPostStatus": {"type": "string", "nullable": true}, "truckInfo": {"type": "string", "nullable": true}, "transferType": {"type": "string", "nullable": true}, "workGroup": {"type": "string", "nullable": true}, "adjustQuantity": {"type": "number", "format": "double", "nullable": true}, "adjustReason": {"type": "string", "nullable": true}, "financialPostQuantity": {"type": "number", "format": "double", "nullable": true}, "planListRepoTime": {"type": "string", "nullable": true}, "planListProdTime": {"type": "string", "nullable": true}, "financialPostTime": {"type": "integer", "format": "int32", "nullable": true}, "financialPostNo": {"type": "string", "nullable": true}, "financialPostLatestTime": {"type": "string", "nullable": true}, "financialPostUploadTime": {"type": "string", "nullable": true}, "importTime": {"type": "string", "nullable": true}, "sapNo": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "length": {"type": "number", "format": "double", "nullable": true}, "width": {"type": "number", "format": "double", "nullable": true}, "height": {"type": "number", "format": "double", "nullable": true}, "facade": {"type": "string", "nullable": true}, "effectivePeriod": {"type": "integer", "format": "int32", "nullable": true}, "expirationPeriod": {"type": "integer", "format": "int32", "nullable": true}, "qualityControl": {"type": "boolean", "nullable": true}, "minimumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "maximumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "goodsCode69": {"type": "string", "nullable": true}, "goodsCodeId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "material": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "executing": {"type": "number", "format": "double"}, "finished": {"type": "number", "format": "double"}, "stock": {"type": "integer", "format": "int32", "nullable": true}, "planListFlag": {"type": "boolean"}, "planListRemark": {"type": "string", "nullable": true}, "planListRepo": {"type": "string", "nullable": true}, "planListProd": {"type": "string", "nullable": true}, "minStorageTime": {"type": "string", "nullable": true}, "maxStorageTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "97e61930cc4e4f19b1c26638ebba1181": {"type": "object", "properties": {"warehouse": {"type": "integer", "format": "int32"}, "barcode": {"type": "string", "nullable": true}, "spec": {"type": "integer", "format": "int32", "nullable": true}, "original": {"nullable": true}, "destination": {"nullable": true}, "priority": {"type": "integer", "format": "int32", "nullable": true}, "operator": {"type": "integer", "format": "int32"}, "manual": {"type": "boolean"}, "tag": {"type": "string", "nullable": true}, "previous": {"type": "integer", "format": "int32", "nullable": true}, "area": {"type": "integer", "format": "int32", "nullable": true}, "planListId": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "planId": {"type": "integer", "format": "int32"}, "manageListQuantity": {"type": "number", "format": "double", "nullable": true}, "planLock": {"type": "string", "nullable": true}}, "additionalProperties": false}, "9c4b8a4690e0492882e693dcf5c90c8f": {"enum": [0, 2, 3], "type": "integer", "format": "int32"}, "9ea49a5606b942e8a2d69b1010cee70a": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/a567956579ca4dcbb80ce503a5bac8cb"}}, "additionalProperties": false}, "a567956579ca4dcbb80ce503a5bac8cb": {"type": "object", "additionalProperties": false}, "a77cdf79ddae4862b5f82937e7a9f54a": {"type": "object", "properties": {"handleNull": {"type": "boolean", "readOnly": true}, "type": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "base64Encoding": {"type": "string", "nullable": true}}, "additionalProperties": false}, "a7d616719fd1434493ce2b54e1867228": {"type": "object", "additionalProperties": false}, "a8001cd846774435a6882592bed528d1": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "attributes": {"$ref": "#/components/schemas/31985d235eb14ab191e2bed430676fb2"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "addMethod": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "removeMethod": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "raiseMethod": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "isMulticast": {"type": "boolean", "readOnly": true}, "eventHandlerType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}}, "additionalProperties": false}, "a911046f69bb46f3922ab2f3249b856c": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "controlId": {"type": "integer", "format": "int32", "nullable": true}, "type": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "deviceCode": {"type": "string", "nullable": true}, "stockbarcode": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "createTime": {"type": "string", "nullable": true}, "parameter": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "para01": {"type": "string", "nullable": true}, "para02": {"type": "string", "nullable": true}}, "additionalProperties": false}, "a9c0ae37eaf3427b8bd0bea19a33bf4b": {"type": "object", "properties": {"mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "aa7bd63498014a17b117c4f85f5da347": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "propertyType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "attributes": {"$ref": "#/components/schemas/8670a77d01d440aa971db3b39881fe9b"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "canRead": {"type": "boolean", "readOnly": true}, "canWrite": {"type": "boolean", "readOnly": true}, "getMethod": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "setMethod": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}}, "additionalProperties": false}, "adadba7deaac4a9bb702142444b62f9a": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "beginDateCount": {"type": "integer", "format": "int32"}, "finalDateCount": {"type": "integer", "format": "int32"}, "model": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ae868058cc3448408d8c6983b987bc00": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "warehouse": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "b0185b5ec3464493ab0063e12b9fbc8a": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/4a01b5705b5e4b2dbfc6ef55e3004515"}, "methodImplementationFlags": {"$ref": "#/components/schemas/5b3e536fe5914bbd830c53db6007f808"}, "callingConvention": {"$ref": "#/components/schemas/8cb76efe33504c24b1aec4fbe3cd8c09"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/435c4fd26f55443c8f94b345d0a0c2d4"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "returnParameter": {"$ref": "#/components/schemas/c4378a678d7b482bacc2a65dde87804e"}, "returnType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "returnTypeCustomAttributes": {"$ref": "#/components/schemas/a7d616719fd1434493ce2b54e1867228"}}, "additionalProperties": false}, "b1a22519bd6e4041aa49c6d2f6c82c8b": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/4f390d43e0a341c78ee3aa1e3c049605"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "declaringMethod": {"$ref": "#/components/schemas/005fdc156ff24509851668f8fb61483a"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "underlyingSystemType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/4ec5576c7bf547edae908158c5adb380"}, "attributes": {"$ref": "#/components/schemas/e95ab32ba86a4cabbf89cc8e9f7df61a"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/ee62a682bce245b6a4ed73919fa9c936"}, "typeInitializer": {"$ref": "#/components/schemas/4b00f96fc68b409c9e4c41c81f104fb1"}, "typeHandle": {"$ref": "#/components/schemas/77bdd5870bc64d3d846832eacf17156d"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}, "genericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "nullable": true, "readOnly": true}, "declaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/4b00f96fc68b409c9e4c41c81f104fb1"}, "nullable": true, "readOnly": true}, "declaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/a8001cd846774435a6882592bed528d1"}, "nullable": true, "readOnly": true}, "declaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/f1afbf8be0084d8eb1633aae62c9ae98"}, "nullable": true, "readOnly": true}, "declaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/c00824303edd4253868014aaa9cce47a"}, "nullable": true, "readOnly": true}, "declaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/b0185b5ec3464493ab0063e12b9fbc8a"}, "nullable": true, "readOnly": true}, "declaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/b1a22519bd6e4041aa49c6d2f6c82c8b"}, "nullable": true, "readOnly": true}, "declaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/aa7bd63498014a17b117c4f85f5da347"}, "nullable": true, "readOnly": true}, "implementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "b3b8189cbeea4455a21315928ace7cdb": {"enum": [1, 2, 4, 8, 16, 32, 64, 128, 191], "type": "integer", "format": "int32"}, "b6c1bb6cc2294f269aa1f9456296cc46": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 16, 32, 64, 128, 256, 512, 1024, 4096, 8192, 32768, 38144], "type": "integer", "format": "int32"}, "b7a90f4f734549428905232648d93ba8": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "subject": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "source": {"type": "integer", "format": "int32"}, "flag": {"type": "boolean"}}, "additionalProperties": false}, "bfca022d50e841aeb721f1f1fb3e7479": {"type": "object", "properties": {"batch": {"type": "string", "nullable": true}, "bill": {"type": "string", "nullable": true}, "supplier": {"type": "string", "nullable": true}, "manufacturingDate": {"type": "string", "format": "date-time", "nullable": true}, "qualityState": {"type": "string", "nullable": true}, "plantCode": {"type": "string", "nullable": true}, "productionLine": {"type": "string", "nullable": true}, "clientCode": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "financialPostStatus": {"type": "string", "nullable": true}, "truckInfo": {"type": "string", "nullable": true}, "transferType": {"type": "string", "nullable": true}, "workGroup": {"type": "string", "nullable": true}, "adjustQuantity": {"type": "number", "format": "double", "nullable": true}, "adjustReason": {"type": "string", "nullable": true}, "financialPostQuantity": {"type": "number", "format": "double", "nullable": true}, "planListRepoTime": {"type": "string", "nullable": true}, "planListProdTime": {"type": "string", "nullable": true}, "financialPostTime": {"type": "integer", "format": "int32", "nullable": true}, "financialPostNo": {"type": "string", "nullable": true}, "financialPostLatestTime": {"type": "string", "nullable": true}, "financialPostUploadTime": {"type": "string", "nullable": true}, "importTime": {"type": "string", "nullable": true}, "sapNo": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "length": {"type": "number", "format": "double", "nullable": true}, "width": {"type": "number", "format": "double", "nullable": true}, "height": {"type": "number", "format": "double", "nullable": true}, "facade": {"type": "string", "nullable": true}, "effectivePeriod": {"type": "integer", "format": "int32", "nullable": true}, "expirationPeriod": {"type": "integer", "format": "int32", "nullable": true}, "qualityControl": {"type": "boolean", "nullable": true}, "minimumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "maximumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "goodsCode69": {"type": "string", "nullable": true}, "goodsCodeId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32", "nullable": true}, "mainId": {"type": "integer", "format": "int32"}, "material": {"type": "integer", "format": "int32", "nullable": true}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double", "nullable": true}, "lockedQuantity": {"type": "number", "format": "double", "nullable": true}, "unlockedQuantity": {"type": "number", "format": "double", "nullable": true}, "managelistquantity": {"type": "number", "format": "double", "nullable": true}, "managelistquantityEdited": {"type": "number", "format": "double", "nullable": true}, "inboundTime": {"type": "string", "format": "date-time", "nullable": true}, "inventoryTime": {"type": "string", "format": "date-time", "nullable": true}, "availableTime": {"type": "string", "format": "date-time", "nullable": true}, "unavailableTime": {"type": "string", "format": "date-time", "nullable": true}, "inventoryAge": {"type": "number", "format": "double", "nullable": true}, "materialAge": {"type": "number", "format": "double", "nullable": true}, "effectiveTime": {"type": "string", "format": "date-time", "nullable": true}, "expirationTime": {"type": "string", "format": "date-time", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "full": {"type": "boolean", "nullable": true}, "warehouse": {"type": "string", "nullable": true}, "area": {"type": "string", "nullable": true}, "cell": {"type": "string", "nullable": true}, "position": {"type": "string", "nullable": true}, "enabled": {"type": "boolean", "nullable": true}, "remark": {"type": "string", "nullable": true}, "overdue": {"type": "number", "format": "double", "nullable": true}, "lock": {"type": "integer", "format": "int32", "nullable": true}, "locker": {"type": "string", "nullable": true}, "order": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "c00824303edd4253868014aaa9cce47a": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "c4378a678d7b482bacc2a65dde87804e": {"type": "object", "properties": {"attributes": {"$ref": "#/components/schemas/f1500b9ba1c446618570bea70f15ca43"}, "member": {"$ref": "#/components/schemas/c00824303edd4253868014aaa9cce47a"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "parameterType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "position": {"type": "integer", "format": "int32", "readOnly": true}, "isIn": {"type": "boolean", "readOnly": true}, "isLcid": {"type": "boolean", "readOnly": true}, "isOptional": {"type": "boolean", "readOnly": true}, "isOut": {"type": "boolean", "readOnly": true}, "isRetval": {"type": "boolean", "readOnly": true}, "defaultValue": {"nullable": true, "readOnly": true}, "rawDefaultValue": {"nullable": true, "readOnly": true}, "hasDefaultValue": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "c467cf500fd440e982212ed620854df2": {"type": "object", "properties": {"batch": {"type": "string", "nullable": true}, "bill": {"type": "string", "nullable": true}, "supplier": {"type": "string", "nullable": true}, "manufacturingDate": {"type": "string", "format": "date-time", "nullable": true}, "qualityState": {"type": "string", "nullable": true}, "plantCode": {"type": "string", "nullable": true}, "productionLine": {"type": "string", "nullable": true}, "clientCode": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "financialPostStatus": {"type": "string", "nullable": true}, "truckInfo": {"type": "string", "nullable": true}, "transferType": {"type": "string", "nullable": true}, "workGroup": {"type": "string", "nullable": true}, "adjustQuantity": {"type": "number", "format": "double", "nullable": true}, "adjustReason": {"type": "string", "nullable": true}, "financialPostQuantity": {"type": "number", "format": "double", "nullable": true}, "planListRepoTime": {"type": "string", "nullable": true}, "planListProdTime": {"type": "string", "nullable": true}, "financialPostTime": {"type": "integer", "format": "int32", "nullable": true}, "financialPostNo": {"type": "string", "nullable": true}, "financialPostLatestTime": {"type": "string", "nullable": true}, "financialPostUploadTime": {"type": "string", "nullable": true}, "importTime": {"type": "string", "nullable": true}, "sapNo": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "length": {"type": "number", "format": "double", "nullable": true}, "width": {"type": "number", "format": "double", "nullable": true}, "height": {"type": "number", "format": "double", "nullable": true}, "facade": {"type": "string", "nullable": true}, "effectivePeriod": {"type": "integer", "format": "int32", "nullable": true}, "expirationPeriod": {"type": "integer", "format": "int32", "nullable": true}, "qualityControl": {"type": "boolean", "nullable": true}, "minimumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "maximumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "goodsCode69": {"type": "string", "nullable": true}, "goodsCodeId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "mainId": {"type": "integer", "format": "int32"}, "material": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "managelistQuantity": {"type": "number", "format": "double"}, "managelistquantityEdited": {"type": "number", "format": "double", "nullable": true}, "inboundTime": {"type": "string", "format": "date-time"}, "inventoryTime": {"type": "string", "format": "date-time"}, "availableTime": {"type": "string", "format": "date-time", "nullable": true}, "unavailableTime": {"type": "string", "format": "date-time", "nullable": true}, "effectiveTime": {"type": "string", "format": "date-time", "nullable": true}, "expirationTime": {"type": "string", "format": "date-time", "nullable": true}, "enabled": {"type": "boolean", "nullable": true}, "remark": {"nullable": true}, "lock": {"type": "integer", "format": "int32", "nullable": true}, "order": {"type": "integer", "format": "int32", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "c4d24ce30ee145a38dab8bc08153c81d": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/4f390d43e0a341c78ee3aa1e3c049605"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "declaringMethod": {"$ref": "#/components/schemas/005fdc156ff24509851668f8fb61483a"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "underlyingSystemType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/4ec5576c7bf547edae908158c5adb380"}, "attributes": {"$ref": "#/components/schemas/e95ab32ba86a4cabbf89cc8e9f7df61a"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/ee62a682bce245b6a4ed73919fa9c936"}, "typeInitializer": {"$ref": "#/components/schemas/4b00f96fc68b409c9e4c41c81f104fb1"}, "typeHandle": {"$ref": "#/components/schemas/77bdd5870bc64d3d846832eacf17156d"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "c6135ba6299a46bc88750ed68eeeac2c": {"type": "object", "properties": {"batch": {"type": "string", "nullable": true}, "bill": {"type": "string", "nullable": true}, "supplier": {"type": "string", "nullable": true}, "manufacturingDate": {"type": "string", "format": "date-time", "nullable": true}, "qualityState": {"type": "string", "nullable": true}, "plantCode": {"type": "string", "nullable": true}, "productionLine": {"type": "string", "nullable": true}, "clientCode": {"type": "string", "nullable": true}, "warehouseCode": {"type": "string", "nullable": true}, "financialPostStatus": {"type": "string", "nullable": true}, "truckInfo": {"type": "string", "nullable": true}, "transferType": {"type": "string", "nullable": true}, "workGroup": {"type": "string", "nullable": true}, "adjustQuantity": {"type": "number", "format": "double", "nullable": true}, "adjustReason": {"type": "string", "nullable": true}, "financialPostQuantity": {"type": "number", "format": "double", "nullable": true}, "planListRepoTime": {"type": "string", "nullable": true}, "planListProdTime": {"type": "string", "nullable": true}, "financialPostTime": {"type": "integer", "format": "int32", "nullable": true}, "financialPostNo": {"type": "string", "nullable": true}, "financialPostLatestTime": {"type": "string", "nullable": true}, "financialPostUploadTime": {"type": "string", "nullable": true}, "importTime": {"type": "string", "nullable": true}, "sapNo": {"type": "string", "nullable": true}, "alias": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "length": {"type": "number", "format": "double", "nullable": true}, "width": {"type": "number", "format": "double", "nullable": true}, "height": {"type": "number", "format": "double", "nullable": true}, "facade": {"type": "string", "nullable": true}, "effectivePeriod": {"type": "integer", "format": "int32", "nullable": true}, "expirationPeriod": {"type": "integer", "format": "int32", "nullable": true}, "qualityControl": {"type": "boolean", "nullable": true}, "minimumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "maximumStockPeriod": {"type": "integer", "format": "int32", "nullable": true}, "goodsCode69": {"type": "string", "nullable": true}, "goodsCodeId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "action": {"type": "array", "items": {"type": "string"}, "nullable": true}, "number": {"type": "string", "nullable": true}, "creater": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "finalTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "state": {"type": "integer", "format": "int32"}, "line": {"type": "integer", "format": "int32", "nullable": true}, "material": {"type": "integer", "format": "int32", "nullable": true}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double", "nullable": true}, "executing": {"type": "number", "format": "double", "nullable": true}, "finished": {"type": "number", "format": "double", "nullable": true}, "plancode40": {"type": "string", "nullable": true}, "plancode99": {"type": "string", "nullable": true}, "platform": {"type": "string", "nullable": true}, "storageLine": {"type": "integer", "format": "int32", "nullable": true}, "planListFlag": {"type": "boolean"}, "planListRemark": {"type": "string", "nullable": true}, "planListRepo": {"type": "string", "nullable": true}, "planListProd": {"type": "string", "nullable": true}, "minStorageTime": {"type": "string", "nullable": true}, "maxStorageTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "de503ecc3e984a7082de365ea0543633": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "e95ab32ba86a4cabbf89cc8e9f7df61a": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 24, 32, 128, 256, 1024, 2048, 4096, 8192, 16384, 65536, 131072, 196608, 262144, 264192, 1048576, 12582912], "type": "integer", "format": "int32"}, "ee62a682bce245b6a4ed73919fa9c936": {"type": "object", "properties": {"typeId": {"nullable": true, "readOnly": true}, "value": {"$ref": "#/components/schemas/9c4b8a4690e0492882e693dcf5c90c8f"}}, "additionalProperties": false}, "f1500b9ba1c446618570bea70f15ca43": {"enum": [0, 1, 2, 4, 8, 16, 4096, 8192, 16384, 32768, 61440], "type": "integer", "format": "int32"}, "f1afbf8be0084d8eb1633aae62c9ae98": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "reflectedType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "module": {"$ref": "#/components/schemas/6fc5bb7d90b44b7fa1d382ffb5e5f438"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/36fd5934e97044228725a619771f0688"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/b3b8189cbeea4455a21315928ace7cdb"}, "attributes": {"$ref": "#/components/schemas/b6c1bb6cc2294f269aa1f9456296cc46"}, "fieldType": {"$ref": "#/components/schemas/c4d24ce30ee145a38dab8bc08153c81d"}, "isInitOnly": {"type": "boolean", "readOnly": true}, "isLiteral": {"type": "boolean", "readOnly": true}, "isNotSerialized": {"type": "boolean", "readOnly": true, "deprecated": true}, "isPinvokeImpl": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "fieldHandle": {"$ref": "#/components/schemas/9ea49a5606b942e8a2d69b1010cee70a"}}, "additionalProperties": false}}}}